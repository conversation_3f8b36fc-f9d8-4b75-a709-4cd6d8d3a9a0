import tkinter as tk
from tkinter import ttk, simpledialog, messagebox, filedialog
import os
import importlib.util
import pprint
import csv

# Default content for a new/overwritten access_dict.py file
FILE_HEADER_ACCESS_CONTROLS = """# Dictionary of roles representing access levels
access_controls = """

FILE_HEADER_USER_GROUPS = """
# Dictionary of user groups, their members, and assigned roles
user_groups = """

class AccessControlEditor:
    def __init__(self, master):
        self.master = master
        master.title("Access Control Editor")
        master.geometry("800x600")
        # Set a minimum size for the window to maintain usability
        master.minsize(1000, 700)


        # --- Flexible File Path Handling ---
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.default_access_dict_path = os.path.join(script_dir, "access_dict.py")
        self.current_file_path = self.default_access_dict_path # Start with default

        self.access_controls_data = {} # For role: {permissions}
        self.user_groups_data = {}     # For group: {'users': set(), 'roles': set()}
        self.all_known_resources = set() # For all unique tool/KB names

        # --- Menu Bar ---
        self.setup_menu()

        # --- Notebook for Tabs ---
        self.notebook = ttk.Notebook(master)
        self.notebook.pack(expand=True, fill='both', padx=10, pady=10)

        # --- Tab 1: Role & Permission Management ---
        self.roles_tab = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.roles_tab, text='Role & Permission Management')
        self.setup_roles_tab()

        # --- Tab 2: Group Management ---
        self.groups_tab = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.groups_tab, text='Group Management')
        self.setup_groups_tab()

        bottom_frame = ttk.Frame(master, padding="10")
        bottom_frame.pack(fill=tk.X)

        # --- Bottom Action Buttons & Status ---
        ttk.Button(bottom_frame, text="Save Changes", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Refresh Data", command=self.load_data_and_populate).pack(side=tk.LEFT, padx=5)

        self.status_label = ttk.Label(bottom_frame, text="Status: Ready")
        self.status_label.pack(side=tk.LEFT, padx=10)

        # --- Initial Load ---
        self.load_data_and_populate()

    def setup_menu(self):
        menubar = tk.Menu(self.master)
        filemenu = tk.Menu(menubar, tearoff=0)
        filemenu.add_command(label="Open Access File...", command=self.open_access_file)
        filemenu.add_separator()
        filemenu.add_command(label="Save", command=self.save_changes)
        filemenu.add_separator()
        filemenu.add_command(label="Exit", command=self.master.quit)
        menubar.add_cascade(label="File", menu=filemenu)
        self.master.config(menu=menubar)


    def setup_roles_tab(self):
        # Main horizontal paned window for roles list and permissions assignment
        main_h_pane = ttk.PanedWindow(self.roles_tab, orient=tk.HORIZONTAL)
        main_h_pane.pack(fill=tk.BOTH, expand=True, padx=4, pady=5)

        # --- Roles Section ---
        roles_frame = ttk.LabelFrame(self.roles_tab, text="Roles", padding="10")
        # roles_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5) # Now added to pane
        main_h_pane.add(roles_frame, weight=1) # Adjust weight as needed, e.g., 1 for less space

        self.roles_listbox = tk.Listbox(roles_frame, exportselection=False, width=30)
        self.roles_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.roles_listbox.bind("<<ListboxSelect>>", self.on_role_select)
 
        roles_buttons_frame = ttk.Frame(roles_frame)
        roles_buttons_frame.pack(fill=tk.X)
        ttk.Button(roles_buttons_frame, text="Add Role", command=self.add_role).pack(side=tk.LEFT, padx=2)
        ttk.Button(roles_buttons_frame, text="Delete Role", command=self.delete_role).pack(side=tk.LEFT, padx=2)
 
        # --- Permissions Assignment Section ---
        permissions_assignment_frame = ttk.LabelFrame(self.roles_tab, text="Assign Permissions to Selected Role", padding="10")
        # permissions_assignment_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5) # Now added to pane
        main_h_pane.add(permissions_assignment_frame, weight=3) # Adjust weight, e.g., 3 for more space

        # Available Resources
        available_perm_frame = ttk.LabelFrame(permissions_assignment_frame, text="Available Resources", padding="5")
        available_perm_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        self.available_resources_listbox = tk.Listbox(available_perm_frame, exportselection=False, selectmode=tk.EXTENDED)
        self.available_resources_listbox.pack(fill=tk.BOTH, expand=True)
        
        add_new_resource_frame = ttk.Frame(available_perm_frame)
        add_new_resource_frame.pack(fill=tk.X, pady=5)
        self.new_resource_entry = ttk.Entry(add_new_resource_frame, width=20)
        self.new_resource_entry.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=(0,5))
        ttk.Button(add_new_resource_frame, text="Add New Global Resource", command=self.add_new_global_resource).pack(side=tk.LEFT)

        # Assign/Unassign Buttons
        assign_buttons_frame = ttk.Frame(permissions_assignment_frame)
        assign_buttons_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=(50,0)) # Added pady for vertical centering
        ttk.Button(assign_buttons_frame, text="Assign >>", command=self.assign_permission_to_role).pack(pady=5)
        ttk.Button(assign_buttons_frame, text="<< Unassign", command=self.unassign_permission_from_role).pack(pady=5)

        # Assigned Permissions
        assigned_perm_frame = ttk.LabelFrame(permissions_assignment_frame, text="Assigned to Role", padding="5")
        assigned_perm_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        self.role_permissions_listbox = tk.Listbox(assigned_perm_frame, exportselection=False, selectmode=tk.EXTENDED) # Was self.permissions_listbox
        self.role_permissions_listbox.pack(fill=tk.BOTH, expand=True)

    def setup_groups_tab(self):
        # Main horizontal paned window for groups list and group details
        main_h_pane_groups = ttk.PanedWindow(self.groups_tab, orient=tk.HORIZONTAL)
        main_h_pane_groups.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # --- Groups Section ---
        groups_list_frame = ttk.LabelFrame(self.groups_tab, text="User Groups", padding="10")
        # groups_list_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5) # Now added to pane
        main_h_pane_groups.add(groups_list_frame, weight=1)

        self.groups_listbox = tk.Listbox(groups_list_frame, exportselection=False, width=30)

        self.groups_listbox.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.groups_listbox.bind("<<ListboxSelect>>", self.on_group_select)

        groups_buttons_frame = ttk.Frame(groups_list_frame)
        groups_buttons_frame.pack(fill=tk.X)
        ttk.Button(groups_buttons_frame, text="Add Group", command=self.add_group).pack(side=tk.LEFT, padx=2)
        ttk.Button(groups_buttons_frame, text="Delete Group", command=self.delete_group).pack(side=tk.LEFT, padx=2)

        # --- Group Details Section (Users and Roles) ---
        group_details_frame = ttk.Frame(self.groups_tab, padding="10")
        # group_details_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5) # Now added to pane
        main_h_pane_groups.add(group_details_frame, weight=3)

        # Users in Group
        users_in_group_frame = ttk.LabelFrame(group_details_frame, text="Users in Selected Group", padding="10")
        users_in_group_frame.pack(fill=tk.BOTH, expand=True, pady=(0,5))
        self.users_in_group_listbox = tk.Listbox(users_in_group_frame, exportselection=False)
        self.users_in_group_listbox.pack(fill=tk.BOTH, expand=True, pady=(0,5))
        
        user_actions_frame = ttk.Frame(users_in_group_frame)
        user_actions_frame.pack(fill=tk.X)
        self.new_user_email_entry = ttk.Entry(user_actions_frame, width=25)
        self.new_user_email_entry.pack(side=tk.LEFT, padx=(0,5), expand=True, fill=tk.X)
        ttk.Button(user_actions_frame, text="Add User", command=self.add_user_to_group).pack(side=tk.LEFT, padx=2)
        ttk.Button(user_actions_frame, text="Remove User", command=self.remove_user_from_group).pack(side=tk.LEFT, padx=2)
        ttk.Button(user_actions_frame, text="Import CSV", command=self.import_users_csv_to_group).pack(side=tk.LEFT, padx=2)

        # Roles for Group
        roles_for_group_frame = ttk.LabelFrame(group_details_frame, text="Assign Roles to Selected Group", padding="10")
        roles_for_group_frame.pack(fill=tk.BOTH, expand=True, pady=(5,0))

        available_roles_fg_frame = ttk.LabelFrame(roles_for_group_frame, text="Available Roles", padding="5")
        available_roles_fg_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        self.available_roles_for_group_listbox = tk.Listbox(available_roles_fg_frame, exportselection=False, selectmode=tk.EXTENDED)
        self.available_roles_for_group_listbox.pack(fill=tk.BOTH, expand=True)

        assign_roles_buttons_frame = ttk.Frame(roles_for_group_frame)
        assign_roles_buttons_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=(50,0))
        ttk.Button(assign_roles_buttons_frame, text="Assign >>", command=self.assign_role_to_group).pack(pady=5)
        ttk.Button(assign_roles_buttons_frame, text="<< Unassign", command=self.unassign_role_from_group).pack(pady=5)

        assigned_roles_fg_frame = ttk.LabelFrame(roles_for_group_frame, text="Assigned to Group", padding="5")
        assigned_roles_fg_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        self.assigned_roles_for_group_listbox = tk.Listbox(assigned_roles_fg_frame, exportselection=False, selectmode=tk.EXTENDED)
        self.assigned_roles_for_group_listbox.pack(fill=tk.BOTH, expand=True)

    def _update_status(self, message, is_error=False):
        self.status_label.config(text=f"Status: {message}", foreground="red" if is_error else "black")

    def load_access_controls(self):
        """Loads access_controls dictionary from the specified Python file."""
        if not os.path.exists(self.current_file_path):
            self._update_status(f"File not found: {self.current_file_path}. Use File > Open to select one.", is_error=True)
            return {}, {} # access_controls, user_groups
        try:
            spec = importlib.util.spec_from_file_location("access_dict_module", self.current_file_path)
            access_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(access_module)
            
            loaded_access_controls = {k: set(v) for k, v in getattr(access_module, "access_controls", {}).items()}
            loaded_user_groups = {}
            raw_user_groups = getattr(access_module, "user_groups", {})
            for group_name, group_data in raw_user_groups.items():
                loaded_user_groups[group_name] = {
                    'users': set(group_data.get('users', [])),
                    'roles': set(group_data.get('roles', []))
                }
            return loaded_access_controls, loaded_user_groups
        except Exception as e:
            self._update_status(f"Error loading file: {e}", is_error=True)
            messagebox.showerror("Load Error", f"Could not load access controls from {self.current_file_path}:\n{e}")
            return {}, {}

    def save_access_controls(self):
        """Saves the current access_data to the Python file."""
        # Prepare access_controls string
        # Sort roles and permissions for consistent output
        ac_to_save = {role: sorted(list(perms)) for role, perms in self.access_controls_data.items()}
        ac_string = pprint.pformat(ac_to_save, indent=4, width=120)

        # Prepare user_groups string
        # Sort group names, users within groups, and roles within groups
        ug_to_save = {}
        for group_name, data in self.user_groups_data.items():
            ug_to_save[group_name] = {
                'users': sorted(list(data['users'])),
                'roles': sorted(list(data['roles']))
            }
        ug_to_save = dict(sorted(ug_to_save.items())) # Sort groups by name
        ug_string = pprint.pformat(ug_to_save, indent=4, width=120)

        file_content = FILE_HEADER_ACCESS_CONTROLS + ac_string + "\n\n"
        file_content += FILE_HEADER_USER_GROUPS + ug_string + "\n"

        try:
            with open(self.current_file_path, "w", encoding="utf-8") as f:
                f.write(file_content)
            self._update_status(f"Changes saved to {os.path.basename(self.current_file_path)}")
        except Exception as e:
            self._update_status(f"Error saving file: {e}", is_error=True)
            messagebox.showerror("Save Error", f"Could not save changes to {self.current_file_path}:\n{e}")

    def load_data_and_populate(self):
        self.master.title(f"Access Control Editor - {os.path.basename(self.current_file_path)}")
        self.access_controls_data.clear()
        self.user_groups_data.clear()
        self.access_controls_data, self.user_groups_data = self.load_access_controls()
        
        # Populate all_known_resources
        self.all_known_resources.clear()
        for permissions in self.access_controls_data.values():
            self.all_known_resources.update(permissions)
        # One could also add predefined resources here if needed

        self.populate_roles_listbox()
        self.populate_available_resources_listbox()
        self.populate_groups_listbox()
        self.populate_available_roles_for_group_listbox() # Ensure this is populated

        if not self.access_controls_data and not self.user_groups_data:
            if os.path.exists(self.current_file_path): # File exists but might be empty/invalid
                 self._update_status(f"Loaded {os.path.basename(self.current_file_path)}. It might be empty or invalid.", is_error=True)
        # Status is updated by load_access_controls or if file is found but empty

    def populate_roles_listbox(self):
        self.roles_listbox.delete(0, tk.END)
        self.role_permissions_listbox.delete(0, tk.END) # Clear role's permissions too
        for role in sorted(self.access_controls_data.keys()):
            self.roles_listbox.insert(tk.END, role)
        if self.roles_listbox.size() > 0:
            self.roles_listbox.select_set(0)
            self.on_role_select(None) # Trigger permission display for the first role

    def on_role_select(self, event):
        selection_indices = self.roles_listbox.curselection()
        self.role_permissions_listbox.delete(0, tk.END)
        if not selection_indices:
            return
        
        selected_role = self.roles_listbox.get(selection_indices[0])
        
        if selected_role in self.access_controls_data:
            for permission in sorted(list(self.access_controls_data[selected_role])):
                self.role_permissions_listbox.insert(tk.END, permission)
        
        # Update available resources listbox to show only those not already assigned
        self.populate_available_resources_listbox(selected_role)

    def populate_available_resources_listbox(self, selected_role=None):
        self.available_resources_listbox.delete(0, tk.END)
        assigned_permissions = set()
        if selected_role and selected_role in self.access_controls_data:
            assigned_permissions = self.access_controls_data[selected_role]
        
        for resource in sorted(list(self.all_known_resources)):
            if resource not in assigned_permissions: # Only show if not already assigned to current role
                self.available_resources_listbox.insert(tk.END, resource)


    def add_role(self):
        new_role = simpledialog.askstring("Add New Role", "Enter the name for the new role:")
        if new_role:
            new_role = new_role.strip()
            if not new_role:
                messagebox.showwarning("Invalid Input", "Role name cannot be empty.")
                return
            if new_role in self.access_controls_data:
                messagebox.showwarning("Duplicate Role", f"Role '{new_role}' already exists.")
            else:
                self.access_controls_data[new_role] = set()
                self.populate_roles_listbox()
                # Try to select the newly added role
                for i, role_in_list in enumerate(self.roles_listbox.get(0, tk.END)):
                    if role_in_list == new_role:
                        self.roles_listbox.select_clear(0, tk.END)
                        self.roles_listbox.select_set(i)
                        self.roles_listbox.see(i)
                        self.on_role_select(None)
                        break
                self._update_status(f"Role '{new_role}' added. Save changes to persist.")
                self.populate_available_roles_for_group_listbox() # Update available roles for groups

    def delete_role(self):
        selection_indices = self.roles_listbox.curselection()
        if not selection_indices:
            messagebox.showwarning("No Selection", "Please select a role to delete.")
            return
        
        selected_role = self.roles_listbox.get(selection_indices[0])
        
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete the role '{selected_role}'?"):
            if selected_role in self.access_controls_data:
                del self.access_controls_data[selected_role]
                # Also remove this role from any groups that might have it assigned
                for group_data in self.user_groups_data.values():
                    group_data['roles'].discard(selected_role)

                self.populate_roles_listbox()
                self.on_group_select(None) # Refresh group details if a role was removed
                self._update_status(f"Role '{selected_role}' deleted. Save changes to persist.")
                self.populate_available_roles_for_group_listbox() # Update available roles for groups
            else:
                messagebox.showerror("Error", "Role not found in data (this shouldn't happen).")

    def add_new_global_resource(self):
        new_resource = self.new_resource_entry.get().strip()
        if not new_resource:
            messagebox.showwarning("Invalid Input", "Resource name cannot be empty.")
            return
        if new_resource in self.all_known_resources:
            messagebox.showinfo("Already Exists", f"Resource '{new_resource}' is already known.")
        else:
            self.all_known_resources.add(new_resource)
            self.populate_available_resources_listbox(self.roles_listbox.get(self.roles_listbox.curselection()) if self.roles_listbox.curselection() else None)
            self.new_resource_entry.delete(0, tk.END)
            self._update_status(f"Global resource '{new_resource}' added. Save to persist if used by a role.")

    def assign_permission_to_role(self):
        role_selection_indices = self.roles_listbox.curselection()
        if not role_selection_indices:
            messagebox.showwarning("No Role Selected", "Please select a role first.")
            return
        selected_role = self.roles_listbox.get(role_selection_indices[0])

        permissions_to_assign_indices = self.available_resources_listbox.curselection()
        if not permissions_to_assign_indices:
            messagebox.showwarning("No Resource Selected", "Please select one or more resources from 'Available Resources' to assign.")
            return

        if selected_role in self.access_controls_data:
            assigned_count = 0
            for index in permissions_to_assign_indices:
                permission = self.available_resources_listbox.get(index)
                self.access_controls_data[selected_role].add(permission)
                assigned_count +=1
            if assigned_count > 0:
                self.on_role_select(None) # Refresh both permission listboxes for the role
                self._update_status(f"{assigned_count} permission(s) assigned to '{selected_role}'. Save to persist.")
        else:
            messagebox.showerror("Error", "Selected role not found in data.")

    def unassign_permission_from_role(self):
        role_selection_indices = self.roles_listbox.curselection()
        if not role_selection_indices:
            messagebox.showwarning("No Role Selected", "Please select a role first.")
            return
        selected_role = self.roles_listbox.get(role_selection_indices[0])

        perm_selection_indices = self.role_permissions_listbox.curselection()
        if not perm_selection_indices:
            messagebox.showwarning("No Permission Selected", "Please select one or more permissions from 'Assigned to Role' to unassign.")
            return

        if selected_role in self.access_controls_data:
            unassigned_count = 0
            for index in reversed(perm_selection_indices): # Iterate reversed for safe deletion
                permission = self.role_permissions_listbox.get(index)
                self.access_controls_data[selected_role].discard(permission)
                unassigned_count += 1
            if unassigned_count > 0:
                self.on_role_select(None) # Refresh both permission listboxes for the role
                self._update_status(f"{unassigned_count} permission(s) unassigned from '{selected_role}'. Save to persist.")
        else:
            messagebox.showerror("Error", "Selected role not found in data.")

    # --- Group Management Methods ---
    def populate_groups_listbox(self):
        self.groups_listbox.delete(0, tk.END)
        # Clear group specific details
        self.users_in_group_listbox.delete(0, tk.END)
        self.assigned_roles_for_group_listbox.delete(0, tk.END)

        for group_name in sorted(self.user_groups_data.keys()):
            self.groups_listbox.insert(tk.END, group_name)
        if self.groups_listbox.size() > 0:
            self.groups_listbox.select_set(0)
            self.on_group_select(None)

    def on_group_select(self, event):
        selection_indices = self.groups_listbox.curselection()
        self.users_in_group_listbox.delete(0, tk.END)
        self.assigned_roles_for_group_listbox.delete(0, tk.END)

        if not selection_indices:
            self.populate_available_roles_for_group_listbox() # Show all roles as available
            return

        selected_group = self.groups_listbox.get(selection_indices[0])
        if selected_group in self.user_groups_data:
            group_data = self.user_groups_data[selected_group]
            for user_email in sorted(list(group_data.get('users', set()))):
                self.users_in_group_listbox.insert(tk.END, user_email)
            for role_name in sorted(list(group_data.get('roles', set()))):
                self.assigned_roles_for_group_listbox.insert(tk.END, role_name)
        self.populate_available_roles_for_group_listbox(selected_group)


    def add_group(self):
        new_group = simpledialog.askstring("Add New Group", "Enter the name for the new group:")
        if new_group:
            new_group = new_group.strip()
            if not new_group:
                messagebox.showwarning("Invalid Input", "Group name cannot be empty.")
                return
            if new_group in self.user_groups_data:
                messagebox.showwarning("Duplicate Group", f"Group '{new_group}' already exists.")
            else:
                self.user_groups_data[new_group] = {'users': set(), 'roles': set()}
                self.populate_groups_listbox()
                # Select the new group
                for i, group_in_list in enumerate(self.groups_listbox.get(0, tk.END)):
                    if group_in_list == new_group:
                        self.groups_listbox.select_clear(0, tk.END)
                        self.groups_listbox.select_set(i)
                        self.groups_listbox.see(i)
                        self.on_group_select(None)
                        break
                self._update_status(f"Group '{new_group}' added. Save changes to persist.")

    def delete_group(self):
        selection_indices = self.groups_listbox.curselection()
        if not selection_indices:
            messagebox.showwarning("No Selection", "Please select a group to delete.")
            return
        selected_group = self.groups_listbox.get(selection_indices[0])
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete the group '{selected_group}'?"):
            if selected_group in self.user_groups_data:
                del self.user_groups_data[selected_group]
                self.populate_groups_listbox()
                self._update_status(f"Group '{selected_group}' deleted. Save changes to persist.")

    def add_user_to_group(self):
        group_sel_idx = self.groups_listbox.curselection()
        if not group_sel_idx:
            messagebox.showwarning("No Group Selected", "Please select a group first.")
            return
        selected_group = self.groups_listbox.get(group_sel_idx[0])
        
        new_email = self.new_user_email_entry.get().strip()
        if not new_email: # Basic email validation could be added here
            messagebox.showwarning("Invalid Input", "Email cannot be empty.")
            return
        
        self.user_groups_data[selected_group]['users'].add(new_email)
        self.on_group_select(None) # Refresh user list
        self.new_user_email_entry.delete(0, tk.END)
        self._update_status(f"User '{new_email}' added to '{selected_group}'. Save to persist.")

    def remove_user_from_group(self):
        group_sel_idx = self.groups_listbox.curselection()
        if not group_sel_idx: return
        selected_group = self.groups_listbox.get(group_sel_idx[0])

        user_sel_idx = self.users_in_group_listbox.curselection()
        if not user_sel_idx:
            messagebox.showwarning("No User Selected", "Please select a user to remove.")
            return
        user_to_remove = self.users_in_group_listbox.get(user_sel_idx[0])

        self.user_groups_data[selected_group]['users'].discard(user_to_remove)
        self.on_group_select(None) # Refresh user list
        self._update_status(f"User '{user_to_remove}' removed from '{selected_group}'. Save to persist.")

    def import_users_csv_to_group(self):
        group_sel_idx = self.groups_listbox.curselection()
        if not group_sel_idx:
            messagebox.showwarning("No Group Selected", "Please select a group to import users into.")
            return
        selected_group = self.groups_listbox.get(group_sel_idx[0])

        filepath = filedialog.askopenfilename(
            title="Select CSV file with user emails",
            filetypes=(("CSV files", "*.csv"), ("All files", "*.*"))
        )
        if not filepath: return

        try:
            imported_count = 0
            with open(filepath, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.reader(csvfile)
                for row in reader:
                    if row: # Assuming email is in the first column
                        email = row[0].strip()
                        if email: # Add basic validation if needed
                            self.user_groups_data[selected_group]['users'].add(email)
                            imported_count += 1
            if imported_count > 0:
                self.on_group_select(None)
                self._update_status(f"{imported_count} users imported to '{selected_group}'. Save to persist.")
                messagebox.showinfo("Import Success", f"{imported_count} users imported successfully.")
            else:
                messagebox.showinfo("Import Info", "No new users found or imported from the CSV.")
        except Exception as e:
            messagebox.showerror("Import Error", f"Failed to import users from CSV:\n{e}")
            self._update_status(f"Error importing CSV: {e}", is_error=True)

    def populate_available_roles_for_group_listbox(self, selected_group=None):
        self.available_roles_for_group_listbox.delete(0, tk.END)
        assigned_roles = set()
        if selected_group and selected_group in self.user_groups_data:
            assigned_roles = self.user_groups_data[selected_group].get('roles', set())
        
        for role_name in sorted(self.access_controls_data.keys()):
            if role_name not in assigned_roles:
                self.available_roles_for_group_listbox.insert(tk.END, role_name)

    def assign_role_to_group(self):
        group_sel_idx = self.groups_listbox.curselection()
        if not group_sel_idx: messagebox.showwarning("No Group", "Select a group."); return
        selected_group = self.groups_listbox.get(group_sel_idx[0])

        roles_to_assign_idx = self.available_roles_for_group_listbox.curselection()
        if not roles_to_assign_idx: messagebox.showwarning("No Role", "Select role(s) from 'Available Roles'."); return

        for index in roles_to_assign_idx:
            role_name = self.available_roles_for_group_listbox.get(index)
            self.user_groups_data[selected_group]['roles'].add(role_name)
        self.on_group_select(None)
        self._update_status(f"Role(s) assigned to '{selected_group}'. Save to persist.")

    def unassign_role_from_group(self):
        group_sel_idx = self.groups_listbox.curselection()
        if not group_sel_idx: messagebox.showwarning("No Group", "Select a group."); return
        selected_group = self.groups_listbox.get(group_sel_idx[0])

        roles_to_unassign_idx = self.assigned_roles_for_group_listbox.curselection()
        if not roles_to_unassign_idx: messagebox.showwarning("No Role", "Select role(s) from 'Assigned to Group'."); return
        
        for index in reversed(roles_to_unassign_idx):
            role_name = self.assigned_roles_for_group_listbox.get(index)
            self.user_groups_data[selected_group]['roles'].discard(role_name)
        self.on_group_select(None)
        self._update_status(f"Role(s) unassigned from '{selected_group}'. Save to persist.")

    # --- General Methods ---
    def save_changes(self):
        if messagebox.askyesno("Confirm Save", f"Are you sure you want to save all changes to\n{self.current_file_path}?\nThis will overwrite the file."):
            self.save_access_controls()

    def open_access_file(self):
        filepath = filedialog.askopenfilename(
            title="Open Access Control File",
            filetypes=(("Python files", "*.py"), ("All files", "*.*")),
            initialdir=os.path.dirname(self.current_file_path) # Start in the current file's directory
        )
        if filepath:
            self.current_file_path = filepath
            self._update_status(f"Attempting to load {os.path.basename(filepath)}...")
            self.load_data_and_populate()
        else:
            self._update_status("File open cancelled.")



if __name__ == "__main__":
    root = tk.Tk()
    app = AccessControlEditor(root)
    root.mainloop()
