import google.generativeai as genai
import httpx  # Import httpx for error handling
import logging

# Get a logger for this module
logger = logging.getLogger(__name__)

# Module-level model instance, initialized on first use
_translation_model = None

def _get_translation_model():
    """Initializes and returns the query translation model, relying on global genai configuration."""
    global _translation_model
    if _translation_model is None:
        try:
            # This relies on genai.configure() having been called in an importing module (e.g., agent.py)
            # Using "gemini-1.5-flash-latest" for consistency and known validity.
            # The original traceback showed "gemini-2.0-flash" which is an invalid model name.
            _translation_model = genai.GenerativeModel("gemini-1.5-flash-latest")
            logger.info("Query translation model (gemini-1.5-flash-latest) initialized.")
        except Exception as e:
            logger.error(f"Failed to initialize query translation model: {e}", exc_info=True)
            raise # Re-raise the exception to signal a critical failure if model can't be created
    return _translation_model

# Function to generate the advanced variation of the query
def query_translation(query: str) -> str:
    try:
        logger.debug(f"Attempting query translation for: '{query}'")
        model = _get_translation_model()

        response = model.generate_content(
            f"Rephrase this sentence, keeping it short and precise: {query}"
        )

        if response.parts:
            translated_text = response.text.strip()
            logger.info(f"Successfully translated query. Original: '{query}', Translated: '{translated_text}'")
            return translated_text
        elif response.prompt_feedback and response.prompt_feedback.block_reason:
            logger.warning(f"Query translation blocked. Reason: {response.prompt_feedback.block_reason_message}")
            return f"Error: Query translation was blocked by safety settings (Reason: {response.prompt_feedback.block_reason_message}). Original query: '{query}'"
        else:
            logger.warning(f"No content generated for query translation. Original query: '{query}'")
            return f"Error: No content was generated for query translation. Original query: '{query}'"

    except httpx.ConnectError as e:
        logger.error(f"Connection error during query translation: {e}", exc_info=True)
        return "Error: Unable to connect to the Gemini API for query translation. Please check your internet connection."
    except Exception as e:
        logger.error(f"An unexpected error occurred during query translation: {e}", exc_info=True)
        return f"Error: Query translation failed due to an unexpected error ({type(e).__name__}). Please check logs. Original query: '{query}'"
