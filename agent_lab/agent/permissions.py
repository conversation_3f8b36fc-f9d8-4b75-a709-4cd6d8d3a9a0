import gspread
from core.config import config
import logging

logger = logging.getLogger(__name__)

# Set up credentials
gc = gspread.service_account(filename=config.google_credentials_file)
sh = gc.open_by_key(config.permissions_sheet_id)
worksheet = sh.worksheet(config.permissions_sheet_name)


def get_authorized_users(email: str):
    """Fetch authorized users from Google Sheets"""
    try:
        logger.info(f"Fetching authorized users for email: {email}")
        # Find the email in the sheet
        emails = worksheet.find(email)
        if emails:
            # get all the values in the row
            permissions = worksheet.row_values(emails.row)
            return True #permissions[1:] # Return the list of permissions (excluding the email)

        return False # Return False if the email is not found in the sheet
    except Exception as e:
        logger.error(f"Error fetching authorized users from Google Sheets: {e}")
        # Fallback to config if Google Sheets fails
        return False # config.allowed_users


async def confirm_permissions(permissions: list):
    """Confirm user permissions"""
    users_access = permissions[1:]
    
    # use AI to confirm if the user's prompt falls under any of the access sectors
    
