from typing import Dict, Any, <PERSON>, Tuple, Optional
import google.generativeai as genai
from dotenv import load_dotenv
import os
import json
import logging
import csv
import io
import uuid
import re
import datetime

# ===== Get a logger for this module =====
logger = logging.getLogger(__name__)

# ===== Import config =====
from core.config import config

# ===== Get the dedicated agent_thoughts_logger from the logs module =====
from logs import agent_thoughts_logger

MAX_HISTORY_TURNS = 10 # Number of user/assistant turn pairs to remember

# ===== Load environment variables and configure Gemini API =====
logger.debug("Loading environment variables from .env file.")
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if not GEMINI_API_KEY:
    logger.critical("GEMINI_API_KEY not found in environment variables. Agent functionalities requiring Gemini API will fail.")
    # Optionally, raise an error to stop execution if the API key is essential
    # raise ValueError("GEMINI_API_KEY not found in environment variables. Please set it in your .env file.")
else:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        logger.info("Google Generative AI SDK configured successfully with API key.")
    except Exception as e:
        logger.critical(f"Failed to configure Google Generative AI SDK: {e}", exc_info=True)
        # Optionally, raise an error
        # raise
# ===== Import the RetrievalService for report vector DB (used globally and in Agent) =====
RetrievalService = None # Initialize to None first
try:
    from ..knowledge_base.vector_store.gemini_faiss_retriever import RetrievalService as ImportedRetrievalService
    RetrievalService = ImportedRetrievalService # Assign to the name used in the check
    logger.info("Successfully imported RetrievalService class from gemini_faiss_retriever.")
except ImportError:
    logger.error("Failed to import RetrievalService class from gemini_faiss_retriever.py. Report vector DB will not function.")

# ===== Attempt to create a global RetrievalService instance =====
_global_retrieval_service_instance = None
if RetrievalService: # Now RetrievalService is defined (either as the class or None)
    try:
        _global_retrieval_service_instance = RetrievalService()
        logger.info("Global RetrievalService instance created successfully.")
    except Exception as e:
        logger.error(f"Failed to create global RetrievalService instance: {e}", exc_info=True)

# ==== Constants for report_vector_db are now managed by gemini_faiss_retriever.py ====
# SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__)) # No longer needed here for these paths

# ===== Import query_translator after global genai configuration =====
try:
    # from ..knowledge_base.sql.db_execution import db_execute
    from .query_translator import query_translation # Relative import
    logger.info("Successfully imported query_translation.")
except ImportError:
    logger.warning("query_translator.py not found or import error. Using dummy query_translation.")
    def query_translation(query: str) -> str: # Dummy implementation for fallback
        return query

# ===== Import db_execution after query_translator =====
try:
    import sys, os
    # Ensure the path is correctly added for db_execution
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'knowledge_base', 'sql'))
    # noinspection PyUnresolvedReferences
    from db_execution import db_execute # type: ignore # Relative import
    logger.info("Successfully imported db_execution.")
except ImportError as e:
    logger.warning(f"db_execution.py not found or import error. Specific error: {e}. Using dummy db_execution that now accepts conversation_history.")
    # Dummy implementation updated to accept conversation_history
    def db_execute(script: str, knowledge_bank: str, conversation_history: list = None) -> tuple[Union[str, None], str]:
        logger.debug(f"Dummy db_execute received script: '{script}' for KB: '{knowledge_bank}'")
        script_lower = script.lower()
        # Simulate returning structured data for chart-like queries
        if ("top clients" in script_lower and ("revenue" in script_lower or "value" in script_lower or "amount" in script_lower or "distribution" in script_lower or "metric" in script_lower)) or \
           ("products by sales" in script_lower) or ("user signups" in script_lower and "daily" in script_lower):
            nl_summ = json.dumps([
                {"category": "Alpha Client", "value": 15000, "notes": "Simulated high-value client"},
                {"category": "Beta Client", "value": 12000, "notes": "Simulated medium-value client"},
                {"category": "Gamma Client", "value": 9000, "notes": "Simulated standard client"}
            ])
            logger.info(f"Dummy db_execute: Matched chart-like query. Returning simulated JSON data: {nl_summ[:100]}...")
            return None, nl_summ
        g_sql, nl_summ = None, "Error: Database path not recognized or no action taken."
        return g_sql, nl_summ

# ===== Import from your actual access control files =====
try:
    from .access_dict import access_controls as ACCESS_CONTROL_DICT, user_groups as USER_GROUPS_DICT # Relative import
    from .access_control import determine_access # Relative import
    logger.info("Successfully imported access control definitions and user groups.")
except ImportError as e:
    logger.error(f"Error importing access control modules: {e}")
    logger.warning("Using DUMMY access control and user groups. Place access_dict.py and access_control.py in the correct path.")
    # Fallback dummy implementations if files are missing/imports fail
    ACCESS_CONTROL_DICT = {
        "data_team": {"hr_sql_database", "transactions_sql_database", "chart_generator", "report_generator", "web_search", "financial_reporting_tool"},
        "general_user": {"web_search", "cleary_knowledge_base"},
        "limited_access": {"web_search"}, # Default role for unassigned users
        "<EMAIL>": {"web_search", "hr_sql_database"} # Example of email as a direct role
    }
    USER_GROUPS_DICT = {
        "support_group": {
            "roles": ["general_user"],
            "users": ["<EMAIL>", "<EMAIL>"]
        },
        "admin_group": {
            "roles": ["data_team"], # Assuming 'data_team' is a powerful role
            "users": ["<EMAIL>"]
        }
    }
    def determine_access(role: str, requested_resource: str) -> dict: # Matched signature
        logger.debug(f"(Dummy access check for role '{role}' to resource '{requested_resource}')")
        # Simplified dummy logic based on how determine_access likely works from user's access_control.py
        if role not in ACCESS_CONTROL_DICT:
            return {"status": "Access Denied", "reason": f"Role '{role}' not found."}
        # Check if the role itself exists as a direct entry (e.g. email as role)
        # or if it's a defined role like "data_team"
        if requested_resource in ACCESS_CONTROL_DICT[role]:
            return {"status": "Access Granted", "knowledge_base": requested_resource}
        else:
            return {"status": "Access Denied", "reason": f"Dummy access denied for role '{role}' to '{requested_resource}'."}


# ===== Import the web search utility =====
try:
    from ..knowledge_base.web.web_search_utility import search_sites_for_answer
    logger.info("Successfully imported search_sites_for_answer from web_search_utility.")
except ImportError:
    logger.error("Failed to import search_sites_for_answer from web_search_utility.py. Using async dummy.")
    async def search_sites_for_answer(query: str, llm: 'genai.GenerativeModel' = None, **kwargs) -> Dict[str, Any]: # Match signature, add **kwargs for flexibility
        logger.warning("Using dummy search_sites_for_answer. Web search will not function.")
        return {"answer": f"Dummy web search result for '{query}' due to import error (no sites list).", "sources": ["dummy_site"]}

# ===== Import the plot generation utility =====
try:
    from ..charting_agent.flutterplot import plot_from_dataframe
    logger.info("Successfully imported plot_from_dataframe from flutterplot.")
except ImportError:
    logger.error("Failed to import plot_from_dataframe from flutterplot.py. Plotting tool will not function.")
    plot_from_dataframe = None # Make it None if import fails


# ===== GENERAL OUTPUT GUIDELINES =====
GENERAL_OUTPUT_GUIDELINES = """
When formatting your response for the user, you *must* use Slack's mrkdwn syntax where appropriate to enhance readability and clarity:
- *bold text* for emphasis or headings.
- _italic text_ for slight emphasis.
- ~strikethrough~ for corrections or completed items.
- `inline code` for short code snippets or technical terms.
- ```code block``` for multi-line code examples (e.g., for JSON, SQL, or code snippets).
- > blockquote for quoting text.
- Bulleted lists:
  - Each item *must* begin with an asterisk (`*`) followed immediately by a single standard ASCII space character, then the item's text. Example: `* This is a bullet point.`
  - Alternatively, you can use a hyphen (`-`) followed by a single standard ASCII space character. Example: `- This is another bullet point.`
  - Each bullet item must be on its own new line.
  - If a list is preceded by introductory text (e.g., "Key points include:"), ensure there is a literal newline character (`\\n`) between that text and the first bullet item.
    Example of newline usage: `Here are the items:\\n* Item 1\\n* Item 2`
    This will render in Slack as:
    Here are the items:
    • Item 1
    • Item 2
- Numbered lists: Start each item with `1. `, `2. `, etc. (number, period, space).
  IMPORTANT: Similar newline rules as bulleted lists apply. Ensure a literal newline character (`\\n`) before the first numbered item if it's not at the start of the response.

General Guidelines for All Responses:
- **Clarity and Conciseness**: Responses should be clear, to the point, and avoid unnecessary jargon.
- **Professionalism and Helpfulness**: Maintain a professional, helpful, and polite tone.
- **Accuracy**: Prioritize accuracy. If unsure about an answer, it's better to state that or ask for clarification rather than providing potentially incorrect information.
- **Flutterwave Focus**: Your primary purpose is to assist with Flutterwave-related queries and tasks. If a query is clearly and significantly off-topic from Flutterwave, its business, products, technology, or general fintech topics relevant to Flutterwave, politely state your focus.
- **Data Sensitivity**: Be extremely cautious with sensitive information. Avoid displaying specific Personally Identifiable Information (PII) or highly confidential business figures unless explicitly and safely instructed by the system for a permitted user. If omitting details due to sensitivity, you can mention it generically (e.g., "Certain sensitive details have been omitted for privacy/security.").
- **Action-Oriented**: If a query implies an action you can take (e.g., "generate a report," "search for X"), guide the user towards that or perform it if the request is clear and permissible.
- **No Opinions/Speculation**: Stick to factual information or clearly defined AI capabilities. Avoid personal opinions or speculation on future events, unless the query is explicitly for brainstorming hypothetical scenarios and it's appropriate to do so.
- **Error Handling**: If an error occurs or you cannot fulfill a request, provide a user-friendly message and, if possible, suggest what the user might try next.
- **Attribution**: When providing information obtained from specific tools (like web search) or knowledge bases, it's good practice to indicate the source if it adds credibility or context, especially for web search results.
- **Structured Formatting**: Use Slack mrkdwn (as detailed above) to improve readability and clarity. For example, if presenting a list of items, use a bulleted list. If showing code or structured data like JSON, use a code block.
"""

# ===== LLM INSTANCE =====
class GeminiLLM:
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.debug("Initializing GeminiLLM.")
        if not GEMINI_API_KEY: # Check if API key was loaded and configured
            self.logger.error("GEMINI_API_KEY was not available or configuration failed. GeminiLLM may not work.")
            # Depending on desired behavior, could raise an error or try to operate in a degraded mode.
            # For now, it will proceed and likely fail at API call time if key is truly missing/invalid.
        self.model = genai.GenerativeModel("gemini-1.5-flash-latest")
        self.logger.debug("GeminiLLM initialized with model: models/gemini-1.5-flash-latest")

    def generate(self, prompt: str) -> str:
        self.logger.debug(f"Generating content with prompt (first 100 chars): {prompt[:100]}...")
        try:
            response = self.model.generate_content(prompt)
            if response.parts:
                self.logger.debug(f"LLM response received (first 100 chars): {response.text.strip()[:100]}...")
                return response.text.strip()
            elif response.prompt_feedback and response.prompt_feedback.block_reason:
                self.logger.warning(f"Prompt blocked, reason: {response.prompt_feedback.block_reason_message}")
                return f"Error: Content generation failed (safety: {response.prompt_feedback.block_reason_message})."
            self.logger.warning("No content generated by LLM, though no explicit block reason.")
            return "Error: No content generated."
        except Exception as e:
            self.logger.error(f"Error during LLM generation: {e}", exc_info=True)
            return "Error: LLM generation failed."

# ===== MESSAGE HISTORY FORMATTER =====
def _format_history_for_prompt(conversation_history: list) -> str:
    if not conversation_history:
        return ""
    formatted_history = "\nPrevious conversation:\n"
    for turn in conversation_history:
        role = "User" if turn["role"] == "user" else "Assistant"
        formatted_history += f"{role}: {turn['content']}\n"
    return formatted_history


class Router:
    # Added thoughts_adapter parameter
    def route(self, query: str, conversation_history: list, thoughts_adapter: logging.LoggerAdapter) -> str:
        raise NotImplementedError("Subclasses must implement the 'route' method.")
    
 # ===== SEMANTIC ROUTER =====
class SemanticRouter(Router):
    def __init__(self):
        # This logger is for SemanticRouter's own operational logging, not the detailed agent thoughts.
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.llm = GeminiLLM()
        self.logger.debug("SemanticRouter initialized.")
        self.knowledge_banks = {
            "hr_sql_database": "Structured Human Resources (HR) data: employee details, roles, hire dates, departments, salaries, leave, performance reviews. Can provide aggregated data and counts.",
            "web_search": "General web search via an internet search engine for public knowledge, current events, facts.",
            "report_vector_db": "Collection of reports and sources: merchant reports, partners report, payout reports, variance dashboard, Fraud and Chargeback Performance Report, etc.,  Use this to find reports based on their description or content similarity to the query.",
            "customer_db": "Structured Flutterwave customer data: includes customer details, account information, and related metadata. Use customer_db.csv for schema information. Queries should start with 'datamart.' and exclude 'data_warehouse'."
        }
        self.tools = {
            "report_generator": "Creates textual summaries or formatted reports from information. Use for requests like 'create a report', 'summarize findings'.",
            "table_generator": "Generates a downloadable CSV file from structured data. Use for requests like 'show this as a table', 'list the data in a table format', 'export this data'. This tool needs structured data which it will attempt to fetch from a suitable knowledge bank.",
            "plotly_chart_generator": "Generates visual charts and graphs (as interactive HTML files) from data using Plotly. Use for requests like 'plot a graph', 'show me a chart', 'visualize data'. This tool needs quantitative data (labels and values) which it will attempt to fetch from a suitable knowledge bank if not directly provided. It will provide a link to the generated chart.",
            "message_sender_tool": "Sends a message to a specified Slack channel or user. Use for requests like 'tell @user ...', 'send a message to #channel ...', 'inform team ...'. Recipient can be a user ID, channel ID, or 'CURRENT_CONVERSATION_CONTEXT'.",
            "summarize_dm_conversation_tool": "Summarizes the direct message (DM) conversation history between the requesting user and the assistant. Use when the user asks to recall, summarize, or get a digest of 'what we discussed in DMs', 'my DMs with you', or similar phrases referring to their private conversation with the assistant."
        }
        self.destinations = {**self.knowledge_banks, **self.tools}

    # ===== Added thoughts_adapter parameter =====
    def route(self, query: str, conversation_history: list, thoughts_adapter: logging.LoggerAdapter) -> str:
        thoughts_adapter.info(f"SemanticRouter: Attempting to route query: '{query}'")
        destination_names = list(self.destinations.keys())
        descriptions = "\n".join([f"- {name}: {desc}" for name, desc in self.destinations.items()]) # Full descriptions
        history_context = _format_history_for_prompt(conversation_history)

        thought_prompt = (
            f"{history_context}Current user query: '{query}'.\n\n"
            f"Considering the conversation history (if any, or if related semantically to the current query) and the current query, which of the following destinations is most relevant? "
            f"Options: {', '.join(destination_names)}.\n"
            f"Brief Descriptions:\n{descriptions}\n\n"
            f"Analyze the query. Respond with ONLY the name of the single most appropriate destination from the list (e.g., 'hr_sql_database', 'chart_generator'). "
            f"If no specific destination is a clear match or if the query is too vague, respond with 'default'."
        )
        thoughts_adapter.debug(f"SemanticRouter: Routing prompt for LLM:\n{thought_prompt}")
        llm_response = self.llm.generate(thought_prompt)
        thoughts_adapter.info(f"SemanticRouter: LLM raw response for routing: '{llm_response}'")
        cleaned_response = llm_response.strip().replace("'", "").replace("\"", "").lower()

        for dest_name_key in self.destinations.keys():
            if dest_name_key.lower() == cleaned_response:
                thoughts_adapter.info(f"SemanticRouter: Exact match for route: '{dest_name_key}'")
                return dest_name_key

        for dest_name_key in self.destinations.keys():
            if dest_name_key.lower() in cleaned_response:
                thoughts_adapter.warning(f"SemanticRouter: Matched '{dest_name_key}' as substring in LLM response '{llm_response}'. Using this.")
                return dest_name_key
            
        thoughts_adapter.warning(f"SemanticRouter: Could not definitively match LLM response '{llm_response}' to a known destination. Falling back to 'default'.")
        return "default"


# ===== Placeholder for agent interactions =====
async def query_knowledge_base(
    query: str, 
    knowledge_bank: str, 
    conversation_history: list, 
    llm: 'GeminiLLM', 
    thoughts_adapter: logging.LoggerAdapter,
    year: Optional[str] = None, # Added for customer_db
    month: Optional[str] = None,  # Added for customer_db
    is_data_for_tool: bool = False, # New flag
    agent_instance: Optional['Agent'] = None # Added agent_instance for accessing services like retrieval_service
) -> Dict[str, Any]:
    thoughts_adapter.info(f"Querying Knowledge Base. KB: '{knowledge_bank}', Query: '{query}'")
    
    response_data = {
        "final_response": "Error: Knowledge base interaction returned no information.",
        "determined_kb_or_tool": knowledge_bank,
        "generated_sql": None, # Default to None
        "final_reaction": "warning" # Default reaction for errors/no info
    }

    if knowledge_bank == "web_search":
        thoughts_adapter.info(f"Web Search: Performing DuckDuckGo search for query: '{query}'")
        
        try:
            # The llm instance for search_sites_for_answer should be passed
            # It's assumed the agent's llm_for_details can be used here.
            # Pass the underlying genai.GenerativeModel from the GeminiLLM wrapper
            search_result_dict = await search_sites_for_answer(query=query, llm=llm.model if hasattr(llm, 'model') else None)

            if search_result_dict and search_result_dict.get("answer"):
                answer_text = search_result_dict["answer"]
                # The answer_text from LLM is expected to contain inline citations.
                # We will no longer append a separate list of consulted sources.
                response_data["final_response"] = answer_text
                # Use globe reaction if there were sources (even if only inline)
                # A more robust check might be needed if search_result_dict doesn't indicate sources otherwise
                response_data["final_reaction"] = "globe_with_meridians" if search_result_dict.get("sources") else "mag"

            else:
                thoughts_adapter.warning(f"Web Search: No specific information found for '{query}'.")
                # Instead of a fixed message, generate a conversational fallback
                fallback_response = _generate_conversational_fallback_response(query, conversation_history, llm, thoughts_adapter) # llm here is the GeminiLLM instance
                response_data["final_response"] = fallback_response
                response_data["final_reaction"] = "question" # Question mark for fallback
                response_data["determined_kb_or_tool"] = f"{knowledge_bank}_conversational_fallback_no_info" # Indicate why fallback was used
        except Exception as e:
            thoughts_adapter.error(f"Web Search: Error during execution: {e}", exc_info=True)
            response_data["final_response"] = f"An error occurred while trying to search the web for '{query}'."
        
        # Web search does not generate SQL
        response_data["generated_sql"] = None

    # ==== Vector DB retrieval logic ====
    elif knowledge_bank == "report_vector_db":
        thoughts_adapter.info(f"Report Vector DB: Searching for query: '{query}'")
        # Ensure we are using the agent's initialized RetrievalService
        if _global_retrieval_service_instance:
            try:
                # Determine top_k dynamically
                query_lower = query.lower()
                keywords_for_all_reports = ["all reports", "every report", "list all", "show me all", "give me all"]
                if any(keyword in query_lower for keyword in keywords_for_all_reports):
                    top_k_reports = 10 # Or another number representing "all"
                    thoughts_adapter.info(f"Report Vector DB: Query suggests fetching all relevant reports. Setting top_k to {top_k_reports}.")
                else:
                    top_k_reports = 3 # Default
                    thoughts_adapter.info(f"Report Vector DB: Defaulting to top_k={top_k_reports} reports.")
                # Use the agent's initialized retrieval_service (instance of gemini_faiss_retriever.RetrievalService)
                retrieved_reports = _global_retrieval_service_instance.search_reports(query, top_k=top_k_reports)
                
                report_messages = []
                ask_for_more_context = False

                if retrieved_reports:
                    for item in retrieved_reports:
                        if "error" in item or "message" in item:
                            report_messages.append(f"Notification: {item.get('error') or item.get('message')}")
                            response_data["final_reaction"] = "warning"
                        else:
                            report_name = item.get('name', "N/A") # Use 'name' as per gemini_faiss_retriever
                            description = item.get('Report Description', "No description available.")
                            
                            metrics_raw = item.get('Metrics Captured', "N/A")
                            if isinstance(metrics_raw, str) and metrics_raw.strip():
                                parts = re.split(r'[;\n]', metrics_raw)
                                cleaned_parts = [part.strip() for part in parts if part.strip()]
                                metrics_display = ", ".join(cleaned_parts) if cleaned_parts else "N/A"
                            else:
                                metrics_display = "N/A"
                                
                            link = item.get('Relevant Links', 'N/A')
                            data_partner = item.get('Data Partner', 'N/A')
                            similarity_distance = item.get('similarity_distance')
                            
                            # Construct individual report string
                            report_str = (
                                f"• *Report Name*: {report_name}\n"  # Main bullet for report name
                                f"      - *Description*: _{description}_\n" # Sub-bullet for description
                                f"      - *Metrics Captured*: {metrics_display}\n" # Sub-bullet for metrics
                                f"      - *Data Partner*: {data_partner}\n"  # Sub-bullet for data partner
                                f"      - *Link*: <{link}|View Report>\n"  # Sub-bullet for link
                            )
                            report_messages.append(report_str)

                            # If similarity distance is high (e.g. > 0.5 for L2), it's less relevant.
                            if similarity_distance is not None and similarity_distance > 0.5:
                                ask_for_more_context = True
                            response_data["final_reaction"] = "page_facing_up"
                else:
                    response_data["final_response"] = "I couldn't find any reports matching your query in the database."
                    response_data["final_reaction"] = "open_file_folder"

                if report_messages:
                    intro_message = "Here are some reports that might be relevant to your query:\n\n"
                    if top_k_reports > 3 and len(report_messages) > 3 : # Adjust intro if many reports are shown
                        intro_message = f"I found {len(report_messages)} reports that seem relevant to your query:\n\n"
                    
                    final_response_str = intro_message + "\n".join(report_messages) # Join with single newline for continuous list
                    if ask_for_more_context:
                        final_response_str += "\n\n_Some of these results might not be a perfect match. If these aren't quite what you're looking for, try providing more specific details in your query!_"
                    response_data["final_response"] = final_response_str
                elif not response_data.get("final_response"): # If no reports and no error message set yet
                    response_data["final_response"] = "I couldn't find any reports matching your query in the database."
                    response_data["final_reaction"] = "open_file_folder"

            except FileNotFoundError as e:
                # This specific FileNotFoundError during search_reports is less likely for index/datastore files
                # as they are loaded at RetrievalService init. This might be for other files if the tool evolves.
                thoughts_adapter.error(f"Report Vector DB: FileNotFoundError during search: {e}.", exc_info=True)
                response_data["final_response"] = f"Error: A file required for searching the report database was not found ({e})."
                response_data["final_reaction"] = "warning"
            except Exception as e:
                thoughts_adapter.error(f"Report Vector DB: An unexpected error occurred during search: {e}", exc_info=True)
                response_data["final_response"] = f"An unexpected error occurred while searching the report database: {e}"
                response_data["final_reaction"] = "warning"
        else: # This means agent_instance.retrieval_service is None
            thoughts_adapter.error("Report Vector DB: RetrievalService not initialized in agent instance. This usually means the index/data files were not found at 'agent_lab/knowledge_base/vector_store/'.")
            response_data["final_response"] = ("Error: The report database service is not available. "
                                             "This might be because the required index files (e.g., 'report_index_gemini.faiss', 'report_data_gemini.json') "
                                             "are missing from the 'agent_lab/knowledge_base/vector_store/' directory. "
                                             "Please ensure they exist or run the indexing script (e.g., 'run_embedding.py' in that directory).")
            response_data["final_reaction"] = "warning"
        response_data["generated_sql"] = None # Report DB doesn't use SQL

    else: # For other knowledge banks (like SQL databases)
        thoughts_adapter.info(f"Database Query: Executing query against '{knowledge_bank}'. Query/Instruction: '{query}'")
        # IMPORTANT: If knowledge_bank == "customer_db", the db_execute function (or the LLM generating SQL for it)
        # MUST use the schema from 'agent_lab/schemas/customer_db.csv' and
        # ensure SQL queries start with 'datamart.' and exclude 'data_warehouse',
        # and correctly target the SQLite files in 'agent_lab/knowledge_base/sql/customer_db/'.
        # Pass conversation_history, year, and month to db_execute
        # The db_execute function will now internally decide if it needs to return raw data
        # based on its own heuristics or if we pass a flag.
        # For now, db_execute has been updated to infer based on query keywords.
        generated_sql, result_summary = db_execute(query, knowledge_bank, conversation_history, year=year, month=month)

        thoughts_adapter.info(f"Database Query: Outcome from db_execute for KB '{knowledge_bank}'.")
        thoughts_adapter.info(f"Database Query: Received SQL: {generated_sql if generated_sql else 'N/A'}")
        thoughts_adapter.info(f"Database Query: Received Summary/Error: {result_summary}")

        response_data["generated_sql"] = generated_sql
        if result_summary:
            # If db_execute returned a JSON string (because it detected it's for a tool),
            # we should use that directly for tools and not try to format it as a natural language response here.
            # We also need to check if the original request was for a tool. 
            is_potentially_table_worthy = False # Initialize the variable
            is_json_string = False
            try:
                
                if isinstance(result_summary, str):
                    data_obj = json.loads(result_summary)
                    if isinstance(data_obj, list) and len(data_obj) > 1: # Needs at least two items to be a useful table
                        if all(isinstance(item, dict) for item in data_obj): # List of dicts
                            is_potentially_table_worthy = True
                            parsed_data_for_table_str = result_summary # Store the original JSON string
            except (json.JSONDecodeError, TypeError): # TypeError if result_summary is not string-like
                thoughts_adapter.debug(f"Data from KB '{knowledge_bank}' is not a JSON list of dicts, or not a string. Not offering table view. Data: {str(result_summary)[:100]}")
                pass # Not JSON or not the right structure, so not table-worthy by this heuristic

            if is_potentially_table_worthy and parsed_data_for_table_str:
                thoughts_adapter.info(f"Data from KB '{knowledge_bank}' is potentially table-worthy. Asking user for confirmation.")
                response_data["final_response"] = "I found some information that might be best viewed as a table. Would you like to see it that way? (yes/no)"
                response_data["raw_data_for_table_str"] = parsed_data_for_table_str
                response_data["is_pending_table_confirmation"] = True
                response_data["final_reaction"] = "page_facing_up" # Icon for table data
            else:
                # Ensure result_summary is a string for the prompt
                data_to_format_str = str(result_summary)
                formatting_prompt = f"""{_format_history_for_prompt(conversation_history)}Original User Query: "{query}"
Information/Data retrieved from knowledge base '{knowledge_bank}':
---
{data_to_format_str}
---
Based on the "Original User Query" and the "Information/Data retrieved", formulate a comprehensive and user-friendly natural language response.
{GENERAL_OUTPUT_GUIDELINES}
Ensure your response directly addresses the user's query and uses Slack mrkdwn formatting for readability.
If the retrieved data is an error message, explain it politely to the user.
Pay close attention to newline requirements for lists if you generate a list.
If the search is not Flutterwave-related information politely tell the user, you would be happy to help with any flutterwave related requests, and be cautious with sensitive data.

Respond ONLY with the final user-facing answer.
User-facing response:"""
                thoughts_adapter.debug(f"Database Query (Not Table): LLM formatting prompt (first 300 chars): {formatting_prompt[:300]}")
                formatted_response = llm.generate(formatting_prompt)
                if formatted_response.startswith("Error:") or not formatted_response.strip():
                    thoughts_adapter.warning(f"Database Query (Not Table): LLM formatting failed. Using raw data/summary. LLM response: '{formatted_response}'")
                    response_data["final_response"] = data_to_format_str # Fallback to raw data/summary
                else:
                    response_data["final_reaction"] = "floppy_disk" # Success from DB
                    response_data["final_response"] = formatted_response
        else: # result_summary was None or empty
            thoughts_adapter.warning(f"Knowledge base interaction for '{knowledge_bank}' returned no information for query: '{query}'")
            # Generate a conversational fallback if no info was found
            fallback_response = _generate_conversational_fallback_response(query, conversation_history, llm, thoughts_adapter)
            response_data["final_reaction"] = "question"
            response_data["final_response"] = fallback_response
            response_data["determined_kb_or_tool"] = f"{knowledge_bank}_conversational_fallback_no_info"


    return response_data



# --- Tool Execution Functions (Simulated) ---
# These now return a simple string, but will be wrapped by _execute_specific_tool to fit the dict structure

def generate_report(
    information: str,
    llm: GeminiLLM, # Add LLM
    conversation_history: list, # Add history for context
    thoughts_adapter: logging.LoggerAdapter,
    report_title: str = "Analysis Report", # Add title
    format: str = "text" # Keep format for future use
) -> str:
    thoughts_adapter.info(f"Tool: generate_report. Title: '{report_title}'. Initial Info (first 100 chars): '{information[:100]}...' (Format: {format})")

    history_context = _format_history_for_prompt(conversation_history) # Use current history

    # The report_title parameter passed to this function serves as a default/fallback
    # The LLM will now attempt to generate a more specific title based on the information.
    report_generation_prompt = f"""{history_context}
Contextual Information Provided for the Report:
---
{information} # This string contains 'Core Subject Info' and 'Additional Context' (from hypothetical Qs)
---

Based on the "Contextual Information Provided for the Report" and relevant "Previous conversation":
1.  Suggest a concise and descriptive "suggested_report_title" for a report based on this information.

2.  Generate the "report_content". The content should be well-structured and informative:
    a.  Start with a brief introduction or overview based on the core subject.
    b.  If the "Core Subject Info" is primarily a list of items (e.g., clients, products), present this list clearly. If the list is long (e.g., more than 15-20 items), you may summarize it or present a portion and note the total count (e.g., "Displaying the first 20 of 50 clients: ...").
    c.  If "Additional Context" (derived from hypothetical questions) is available within the provided "Contextual Information", synthesize insights from this additional context into separate, clearly titled paragraphs or bullet points. For example, if a hypothetical question was about "total number of clients", and the answer was "50", include a statement like "In total, there are 50 clients."
    d.  Conclude with a brief summary or a note if further details might require more specific queries.
    e.  Use Slack mrkdwn formatting for clarity (headings, bolding, bullets if appropriate).
    f.  Ensure the report content is focused on Flutterwave-related topics and handles sensitive information appropriately.
{GENERAL_OUTPUT_GUIDELINES}

Respond ONLY with a JSON object containing "suggested_report_title" and "report_content".
Example JSON:
{{
  "suggested_report_title": "Overview of Client Portfolio",
  "report_content": "*Client Portfolio Overview*\\n\\nThis report provides an overview of our clients.\\n\\n*Client Roster*\\nDisplaying the first 20 of 30 clients:\\n- Client A\\n- Client B\\n... (and 10 more)\\n\\n*Key Client Statistics*\\n- Total number of clients: 30.\\n- Average client engagement score: 7.5/10.\\n\\nThis summary is based on currently available data."
}}

JSON Response:
"""
    thoughts_adapter.debug(f"Generate Report: LLM report generation prompt (first 300 chars): {report_generation_prompt[:300]}")
    llm_response_str = llm.generate(report_generation_prompt)
    parsed_llm_output = parse_llm_json_output(llm_response_str, thoughts_adapter)

    final_title_to_use = report_title # Default to the passed-in title
    generated_report_content = f"Based on the available information:\n- {information}\n\nFurther analysis might be required for a more detailed report." # Default content

    if parsed_llm_output:
        suggested_title_from_llm = parsed_llm_output.get("suggested_report_title")
        llm_generated_content = parsed_llm_output.get("report_content")

        if suggested_title_from_llm and len(suggested_title_from_llm.strip()) > 5 and not suggested_title_from_llm.lower().startswith("error"): # Basic check for a decent title
            final_title_to_use = suggested_title_from_llm.strip()
            thoughts_adapter.info(f"Generate Report: Using LLM suggested title: '{final_title_to_use}'")
        else:
            thoughts_adapter.info(f"Generate Report: LLM suggested title ('{suggested_title_from_llm}') was not suitable or missing. Using provided/default title: '{report_title}'")

        if llm_generated_content and not llm_generated_content.lower().startswith("error"):
            generated_report_content = llm_generated_content
            thoughts_adapter.info(f"Generate Report: LLM successfully generated report content (first 100 chars): {generated_report_content[:100]}")
        else:
            thoughts_adapter.warning(f"Generate Report: LLM did not provide suitable report_content. Using fallback content. LLM content: {llm_generated_content}")
    else:
        thoughts_adapter.warning(f"Generate Report: LLM report generation failed to parse JSON or returned error. LLM response: '{llm_response_str}'. Falling back to basic report structure.")

    # Ensure the final title is part of the report, typically at the beginning.
    # Prepend it if the LLM's content doesn't already seem to include it.
    if not final_title_to_use.lower() in generated_report_content.lower()[:len(final_title_to_use) + 20]: # Approx check if title is at start
        return f"*{final_title_to_use}*\n\n{generated_report_content}"
    return generated_report_content

def generate_csv_file_from_json(data_json_str: str, title: str, thoughts_adapter: logging.LoggerAdapter, columns_hint: str = None) -> Dict[str, Any]:
    thoughts_adapter.info(f"Tool: generate_csv_file_from_json. Title='{title}'. Data (first 100 chars): {data_json_str[:100]}...")
    
    output_info = {
        "message": "Could not generate CSV file from the provided data.",
        "filename": None,
        "preview": None # This field is not directly used in the return message construction below but could be useful
    }

    data_obj = None
    try:
        data_obj = json.loads(data_json_str)
    except json.JSONDecodeError:
        thoughts_adapter.warning(f"Tool: generate_csv_file_from_json. Input data_json_str ('{data_json_str[:100]}...') was not valid JSON. Treating as plain text.")
        # Treat as plain text if not JSON
        data_obj = [{"Content": data_json_str}] # Create a list of dicts structure for it

    # Prepare data for CSV writer
    rows_to_write = []
    headers = []

    if isinstance(data_obj, list) and data_obj:
        # Check if it's a list of dictionaries
        if all(isinstance(item, dict) for item in data_obj): # Ideal case
            # Collect all unique keys from all dictionaries to form a comprehensive header list
            # Maintain order from the first non-empty dictionary encountered for initial headers
            all_keys_ordered = []
            seen_keys = set()
            for item in data_obj:
                if isinstance(item, dict):
                    for key in item.keys():
                        if key not in seen_keys:
                            all_keys_ordered.append(key)
                            seen_keys.add(key)
            headers = all_keys_ordered
            rows_to_write = data_obj
        elif all(not isinstance(item, (dict, list)) for item in data_obj): # List of simple items
            # List of simple values (strings, numbers)
            headers = ["Value"]
            rows_to_write = [{"Value": item} for item in data_obj]
        else: # List contains mixed types or other complex structures not suitable for simple CSV
            thoughts_adapter.warning(f"Tool: generate_csv_file_from_json. Data is a list but contains mixed or complex types not suitable for direct CSV conversion. Content: {str(data_obj)[:100]}")
            output_info["message"] = "The data is a list with a complex structure that couldn't be directly converted to a simple CSV table."
            return output_info
    elif isinstance(data_obj, dict) and data_obj: # data_obj is a non-empty dict
        # Single dictionary
        headers = list(data_obj.keys())
        rows_to_write = [data_obj]
    elif not isinstance(data_obj, (list, dict)) and data_obj is not None : # Handles simple string/number if JSON was just that
        # This case is typically hit if json.loads() failed and data_obj was set to [{"Content": data_json_str}]
        headers = ["Content"]
        rows_to_write = [{"Content": str(data_obj)}]
    # If data_obj is None, or an empty list/dict initially, headers and rows_to_write will be empty.

    # If after all processing, we have no headers but do have rows, it's an issue.
    if not headers and rows_to_write:
        thoughts_adapter.error("Tool: generate_csv_file_from_json. No headers could be derived, but data rows exist. Cannot create CSV.")
        output_info["message"] = "Could not determine column headers for the CSV from the provided data structure."
        return output_info
    
    if not rows_to_write and not headers: # Completely empty or unsuitable data
        thoughts_adapter.warning(f"Tool: generate_csv_file_from_json. Data structure is empty or not suitable for CSV. Type: {type(data_obj)}")
        output_info["message"] = "The data provided was empty or its structure was not suitable for a CSV table."
        return output_info

    # Generate CSV content
    try:
        output_csv_stream = io.StringIO()
        if not headers and not rows_to_write: # Should be caught by earlier check, but as safeguard
            csv_content = "" # Empty file content
            thoughts_adapter.info("Tool: generate_csv_file_from_json. Generating an empty CSV file as data and headers are empty.")
        elif not headers and rows_to_write: # Should be caught by the check above
            thoughts_adapter.error("Tool: generate_csv_file_from_json. Inconsistent state: rows exist but no headers. This should not happen.")
            output_info["message"] = "Internal error: Inconsistent data state for CSV generation."
            return output_info
        else: # We have headers (and possibly rows)
            # Use extrasaction='ignore' in case some rows have keys not in the derived headers,
            # though the refined header logic tries to make headers comprehensive.
            writer = csv.DictWriter(output_csv_stream, fieldnames=headers, quoting=csv.QUOTE_ALL, extrasaction='ignore')
            writer.writeheader()
            if rows_to_write: # Only write rows if there are any
                writer.writerows(rows_to_write)
            csv_content = output_csv_stream.getvalue()
        output_csv_stream.close()

        # Generate filename and save
        filename_base = title.replace(' ', '_').replace('/', '_').replace('\\', '_') if title else "table_data"
        filename_base = "".join(c if c.isalnum() or c in ['_', '-'] else '_' for c in filename_base) # Sanitize further
        timestamp_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        unique_id = str(uuid.uuid4())[:8] # Short unique ID
        csv_filename = f"{filename_base}_{timestamp_str}_{unique_id}.csv" # Added timestamp
        csv_filepath = os.path.join(config.generated_files_dir, csv_filename) # Uses config for dir

        with open(csv_filepath, "w", newline='', encoding='utf-8') as f:
            f.write(csv_content)
        
        thoughts_adapter.info(f"Tool: generate_csv_file_from_json. Successfully generated CSV: {csv_filepath}")

        # Create a preview (headers + first 3 data rows)
        preview_lines = csv_content.splitlines()[:4] # Header + up to 3 data rows
        csv_preview_str = "\n".join(preview_lines)

        # Construct the download URL to point to the new custom download endpoint
        download_url = f"{config.app_base_url}/download/{csv_filename}"
        
        output_info["message"] = f"I've generated the requested data as a CSV file: <{download_url}|{csv_filename}>."
        output_info["filename"] = csv_filename
        
        if len(preview_lines) > 1: # If there's at least one data row in preview
            output_info["message"] += f"\n\nHere's a preview of the first few rows:\n```csv\n{csv_preview_str}\n```"
        elif preview_lines: # Only headers
            output_info["message"] += f"\n\nHere are the headers:\n```csv\n{csv_preview_str}\n```\n(No data rows were available in the provided data for preview)."
        else: # Empty data
            output_info["message"] += "\n(The table appears to be empty based on the data provided.)"
        
        output_info["message"] += f"\n\n*Note*: The file is saved in the '{config.generated_files_dir}' directory on the server."

    except Exception as e:
        thoughts_adapter.error(f"Tool: generate_csv_file_from_json. Error during CSV generation or saving: {e}", exc_info=True)
        output_info["message"] = f"An error occurred while generating the CSV file: {e}"

    return output_info

async def send_slack_message(recipient: str, message_text: str, original_user_identifier: str, slack_client, thoughts_adapter: logging.LoggerAdapter) -> str:
    thoughts_adapter.info(f"Tool: send_slack_message. Recipient: '{recipient}', Original User: '{original_user_identifier}', Message (first 100 chars): '{message_text[:100]}...'")
    if not slack_client:
        thoughts_adapter.error("Tool: send_slack_message. Slack client is None. Cannot send message.")
        return "Error: Internal configuration issue prevents sending Slack message (client not available)."
    
    # Attempt to create a user mention if original_user_identifier looks like a user ID (starts with U or W)
    # Otherwise, use the identifier as is (e.g., email or name).
    # A more robust solution would involve looking up the user by email to get their ID for a proper mention.
    user_mention = original_user_identifier
    if original_user_identifier.startswith(("U", "W")) and len(original_user_identifier) > 5: # Basic check for User ID format
        user_mention = f"<@{original_user_identifier}>"
    
    attributed_message = f"{user_mention} asked me to share this with you:\n\n{message_text}"
    
    try:
        # TODO: Implement robust recipient resolution (e.g., email to user ID, channel name to ID)
        # For now, assumes 'recipient' is a channel name (e.g., "#general"), user ID (e.g., "U123XYZ"), or a direct mention string Slack understands.
        response = await slack_client.chat_postMessage(channel=recipient, text=attributed_message)
        if response.get("ok"):
            success_message = f"Okay, I've sent the message to {recipient}."
            thoughts_adapter.info(f"Successfully sent message to {recipient}. Slack API Response: {response}")
            return success_message
        else:
            error_detail = response.get('error', 'Unknown error from Slack API')
            thoughts_adapter.error(f"Failed to send message to {recipient}. Slack API returned error: {error_detail}. Full response: {response}")
            return f"I couldn't send the message to {recipient}. Slack reported: `{error_detail}`."
    except Exception as e: # Catches SlackApiError and other network/unexpected errors
        error_message = f"An error occurred while trying to send the message to {recipient}. Error: `{type(e).__name__}`. Please ensure '{recipient}' is a valid channel or user ID I can message."
        thoughts_adapter.error(f"Exception in send_slack_message to {recipient}: {type(e).__name__} - {e}", exc_info=True)
        return error_message

def query_financial_reporting_tool(query_params: Dict, thoughts_adapter: logging.LoggerAdapter) -> str:
    thoughts_adapter.info(f"Tool: query_financial_reporting_tool. Params: {query_params}")
    return f"Financial report data (simulated): {query_params.get('report_type', 'N/A')}"

def access_marketing_analytics_dashboard(query_params: Dict, thoughts_adapter: logging.LoggerAdapter) -> str:
    thoughts_adapter.info(f"Tool: access_marketing_analytics_dashboard. Params: {query_params}")
    return f"Marketing analytics data (simulated): {query_params.get('metric', 'N/A')}"

def use_social_media_management_tool(action: str, thoughts_adapter: logging.LoggerAdapter, content: str = "") -> str:
    thoughts_adapter.info(f"Tool: use_social_media_management_tool. Action: '{action}'. Content (first 50 chars): {content[:50]}...")
    return f"Social media action '{action}' performed (simulated)."

def view_product_roadmap_tool(feature_query: str, thoughts_adapter: logging.LoggerAdapter) -> str:
    thoughts_adapter.info(f"Tool: view_product_roadmap_tool. Query: '{feature_query}'")
    return f"Product roadmap details for '{feature_query}' (simulated)."

def parse_llm_json_output(llm_text_response: str, thoughts_adapter: logging.LoggerAdapter) -> Union[Dict, None]:
    try:
        # First, strip any leading/trailing whitespace from the entire response
        text_to_parse = llm_text_response.strip()
        thoughts_adapter.debug(f"Attempting to parse LLM JSON output (stripped, first 200): '{text_to_parse[:200]}'")
        # Check for and remove markdown fences for JSON
        if text_to_parse.startswith("```json"):
            text_to_parse = text_to_parse[7:] # Remove ```json
        elif text_to_parse.startswith("```"): # Handle if it's just ```
            text_to_parse = text_to_parse[3:]

        # Remove trailing markdown fence if present
        if text_to_parse.endswith("```"):
            text_to_parse = text_to_parse[:-3]

        # Strip again to clean the JSON content itself
        text_to_parse = text_to_parse.strip()

        if not text_to_parse: # Handle empty string after stripping
            thoughts_adapter.error(f"Parsing LLM JSON: Content became empty after stripping. Original (first 200): {llm_text_response[:200]}")
            return None
        parsed_json = json.loads(text_to_parse)
        thoughts_adapter.debug(f"Successfully parsed LLM JSON output: {parsed_json}")
        return parsed_json
    except json.JSONDecodeError as e:
        thoughts_adapter.error(f"Parsing LLM JSON: JSONDecodeError: {e}. Attempted to parse (first 200): '{text_to_parse[:200]}'. Original response (first 200): {llm_text_response[:200]}", exc_info=True)
        return None
    except Exception as e:
        thoughts_adapter.error(f"Parsing LLM JSON: Unexpected error: {e}. Response (first 200): {llm_text_response[:200]}", exc_info=True)
        return None

def _get_tool_parameters(query: str, tool_action: str, llm: GeminiLLM, conversation_history: list, thoughts_adapter: logging.LoggerAdapter) -> Union[Dict, None]:
    history_context = _format_history_for_prompt(conversation_history)
    prompt_templates = {
        "chart_generator": f"""{history_context}Current user query: "{query}"
The user wants to generate a chart (OLD METHOD, prefer plotly_chart_generator if applicable). To do this, we first need to fetch the data to be plotted. Respond ONLY with the JSON object containing "details": {{"data_request": "...", "chart_type": "...", "title": "..."}}""",        "plotly_chart_generator": f"""{history_context}Current user query: "{query}"
The user wants to generate a visual chart using Plotly.
Your task is to:
1. Formulate a "chart_description_for_plotly" which is the user's request for the plot, possibly rephrased for clarity if needed by the plotting AI. This should describe *what* to plot and *how* (e.g., "a bar chart of sales by region", "a scatter plot of age vs income colored by gender").
2. Formulate a "data_request_query_for_chart". This is a query to fetch the *underlying data* needed for the plot from a knowledge base. It should be specific about the data required (e.g., "Get sales data per region", "Fetch age, income, and gender data for all users"). If the user's query implies using data from the immediate previous turn (e.g., "plot this data"), set this to "USE_PREVIOUS_ASSISTANT_OUTPUT".
Respond ONLY with the JSON object containing "details": {{"chart_description_for_plotly": "...", "data_request_query_for_chart": "..."}}""",
        "table_generator": f"""{history_context}Current user query: "{query}"
The user wants to generate a table. To do this, we first need to fetch the data to be displayed.

Consider the "Current user query" and "Previous conversation":
1. If the query is a direct follow-up like "export this", "show this as a table", "table this data", or similar, and the Assistant's last response in the "Previous conversation" clearly presented data (e.g., a list of items, a summary), then the user is referring to *that specific data*.
   In this case, set "data_request" to a special value "USE_PREVIOUS_ASSISTANT_OUTPUT".
   Set "title" based on the context of the previous data.

2. Otherwise (if it's a new request for a table or the reference is unclear), formulate a concise "data_request" query. This query, when executed against a knowledge base, should retrieve the necessary structured data for the table.
   Set "title" based on this new data request.

Your task is to:
- Determine "data_request" as per the logic above.
- Extract an optional "title" for the table.
- Extract an optional "columns_hint" if the user specifies particular columns or their order.
Respond ONLY with the JSON object containing "details": {{"data_request": "...", "title": "Optional Title", "columns_hint": "Optional hint for columns"}}
""",
        "message_sender_tool": f"""{history_context}Current user query: "{query}"
The user wants to send a message to a Slack channel or user.
Your task is to:
1. Extract the "recipient".   
    - If the user specifies a user (e.g., "@username", "<EMAIL>", "U123ABC"), use that.
    - If the user specifies a channel (e.g., "#general", "C123ABC"), use that.
    - If the user says "everyone", "team", "all", "the channel", or similar general terms referring to the current context, set "recipient" to the special value "CURRENT_CONVERSATION_CONTEXT".

2. Carefully analyze the "Previous conversation":
   a. If the "Current user query" is a follow-up like "send this to @user", "send the same message to #channel", "forward that to #team", etc., it likely refers to a message or data *previously displayed or generated by the Assistant*.
      - Look at the Assistant's turns in the "Previous conversation".
      - Identify the most recent Assistant message that contains substantial information (e.g., a report summary, a list of items, data details), not just a simple acknowledgment or status update (like "Okay, I've sent it.").
      - If such an informational message from the Assistant is found, the "message_content_instruction" should be the *exact, full content* of THAT informational Assistant message.
      - Example: If Assistant's relevant previous turn was "Here is the client breakdown: Client A - 10, Client B - 5.", and user says "send that to #sales", then "message_content_instruction" should be "Here is the client breakdown: Client A - 10, Client B - 5.".
   b. If the user provides a new, explicit message (e.g., "tell @john the meeting is at 5 PM", "say hi to everyone"), then "message_content_instruction" is that explicit message (e.g., "the meeting is at 5 PM", "Hi everyone!").
   c. If the user asks to send the result of a *new* operation (e.g., "generate a report on Q1 sales and send it to #finance"), then "message_content_instruction" should be a query or description for that new operation (e.g., "generate a report on Q1 sales"). The tool will then try to execute this instruction to get the content.
   d. If none of the above apply, and the instruction is unclear, set "message_content_instruction" to an empty string or a note indicating ambiguity.
Respond ONLY with the JSON object containing "details": {{"recipient": "...", "message_content_instruction": "..."}}
""",
        "report_generator": f"""{history_context}Current user query: "{query}". Goal: Generate report.
Analyze the "Current user query" and "Previous conversation" to determine the core subject of the report and formulate hypothetical questions to enrich it.

Your task is to:
1.  **Determine Core Subject for the Report**:
    *   `core_subject_source_type`: Set to "PREVIOUS_ASSISTANT_OUTPUT" if the query is a direct follow-up to the assistant's last informational response (e.g., "report on this", "analyze that"). In this case, `core_subject_query` can be null or a brief reference.
    *   `core_subject_source_type`: Set to "NEW_QUERY" if the user is requesting a report on a new topic or the reference to previous context is unclear.
    *   `core_subject_query`:
        *   If `core_subject_source_type` is "NEW_QUERY", formulate a concise query designed to fetch the primary data/information that will form the core of the report.
        *   If the user's query is specific (e.g., "report on Q1 sales for Product X"), use that as the basis (e.g., "Get Q1 sales data for Product X").
        *   If the user's query is general (e.g., "report on our transaction summary", "give me a project performance report"), transform it into a clear data request (e.g., "Get overall transaction summary data", "Fetch key performance indicators and recent updates for all active projects").
        *   The `core_subject_query` should aim to retrieve structured data or comprehensive information suitable for a report.

2.  **Generate Hypothetical Questions for Enrichment (Optional)**:
    *   Based on the "Current user query", the "Previous conversation", and the identified `core_subject_query` (or context from "PREVIOUS_ASSISTANT_OUTPUT"), generate a list of 0-3 `hypothetical_questions`.
    *   These questions should seek related information that would make the report more comprehensive. They should be answerable by querying available knowledge bases.
    *   If no relevant or useful hypothetical questions can be formed, provide an empty list `[]`. Do not force questions if the core subject is very broad or already implies a general overview.

3.  **Extract Report Title**:
    *   `report_title`: Suggest a concise title for the report based on the overall request and core subject. (e.g., "Q1 Sales Report for Product X", "Overall Transaction Summary", "Project Alpha Performance Overview").

Example 1 (Specific):
User Query: "report on Q3 sales performance for Product X"
JSON: `{{"details": {{"core_subject_source_type": "NEW_QUERY", "core_subject_query": "Get Q3 sales performance data for Product X", "hypothetical_questions": ["How did Q3 sales for Product X compare to Q2 sales?", "What were the main marketing activities for Product X in Q3?"], "report_title": "Q3 Sales Performance for Product X"}}}}`

Example 2 (General):
User Query: "Give me a report on our transaction summary"
JSON: `{{"details": {{"core_subject_source_type": "NEW_QUERY", "core_subject_query": "Get overall transaction summary data", "hypothetical_questions": [], "report_title": "Overall Transaction Summary"}}}}`

Example 3 (Follow-up):
Assistant: Here is the list of active projects: Project Alpha, Project Beta.
User Query: "report on this"
JSON: `{{"details": {{"core_subject_source_type": "PREVIOUS_ASSISTANT_OUTPUT", "core_subject_query": null, "hypothetical_questions": ["What is the budget status for Project Alpha?", "What are the key milestones for Project Beta?"], "report_title": "Report on Active Projects (Alpha, Beta)"}}}}`

Respond ONLY with a JSON object containing "details":
`{{"core_subject_source_type": "...", "core_subject_query": "...", "hypothetical_questions": ["question1", "question2", ...], "report_title": "..."}}`
""",

        "financial_reporting_tool": f"""{history_context}Current user query: "{query}". Goal: Use financial reporting tool.
        Extract: "report_type" (e.g., balance_sheet), "period" (e.g., Q1 2024).
        Respond JSON: {{"details": {{"report_type": "...", "period": "..."}}}}""",

        "marketing_analytics_dashboard": f"""{history_context}Current user query: "{query}". Goal: Access marketing dashboard.
        Extract: "metric" (e.g., campaign_roi), "campaign_name" (optional).
        Respond JSON: {{"details": {{"metric": "...", "campaign_name": "..."}}}}""",

        "social_media_management_tool": f"""{history_context}Current user query: "{query}". Goal: Use social media tool.
        Extract: "social_action" (e.g., post_update), "platform", "content_summary".
        Respond JSON: {{"details": {{"social_action": "...", "platform": "...", "content_summary": "..."}}}}""",

        "product_roadmap_tool": f"""{history_context}Current user query: "{query}". Goal: Query product roadmap.
        Extract: "feature_name" or "status_query".
        Respond JSON: {{"details": {{"query": "..."}}}}""",

        "summarize_dm_conversation_tool": f"""{history_context}Current user query: "{query}"
The user is asking to summarize their direct message (DM) conversation with you (the assistant).
This action implies fetching the DM history of the user making the request.
Respond ONLY with the JSON object: {{"details": {{"action_confirmed": true, "user_for_dm_summary": "REQUESTING_USER"}}}}""",
    }
    parameter_extraction_prompt = prompt_templates.get(tool_action)
    if not parameter_extraction_prompt:
        logger.warning(f"No parameter extraction prompt template for tool_action '{tool_action}'")
        thoughts_adapter.warning(f"Tool Param Extraction: No prompt template for tool_action '{tool_action}'. Returning original query as fallback.")
        return {"original_query": query} # Ensure 'details' key is not present or is None

    thoughts_adapter.debug(f"Tool Param Extraction: LLM prompt for '{tool_action}':\n{parameter_extraction_prompt}")
    response_text = llm.generate(parameter_extraction_prompt)
    thoughts_adapter.info(f"Tool Param Extraction: LLM raw response for '{tool_action}': '{response_text}'")
    gemini_output = parse_llm_json_output(response_text, thoughts_adapter)
    # Ensure we return the 'details' dictionary itself, or None if not found/error
    if gemini_output and "details" in gemini_output:
        thoughts_adapter.info(f"Tool Param Extraction: Successfully extracted parameters for '{tool_action}': {gemini_output['details']}")
        return gemini_output["details"]
    thoughts_adapter.warning(f"Tool Param Extraction: Failed to extract 'details' for '{tool_action}' from LLM response. Raw output: {gemini_output}. Returning original query as fallback.")
    return {"original_query": query, "error": "param_extraction_failed"}


DEFAULT_USER_ROLE = "limited_access" # Define a default role

class Agent:
    def __init__(self, slack_app=None): # Add slack_app parameter
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.retrieval_service = None  # Initialize early to ensure attribute always exists
        self.conversation_history = []
        self.conversation_states = {} # Key: conversation_id, Value: dict of state
        self.router = SemanticRouter()
        self.slack_app = slack_app # Store slack_app instance
        # Ensure GEMINI_API_KEY is available for model initialization
        if not GEMINI_API_KEY:
            self.logger.critical("GEMINI_API_KEY not found in environment. Agent cannot initialize LLM.")
            # Handle this critical failure, perhaps by raising an error
            self.llm_for_details = None 
        else:
            self.llm_for_details = GeminiLLM() # Use the GeminiLLM wrapper
        self.MAX_HISTORY_TURNS = MAX_HISTORY_TURNS # Use the module-level constant or define here

        # Store access control and user group data, and pre-process user groups for faster lookups
        self.access_controls = ACCESS_CONTROL_DICT
        self.user_groups = USER_GROUPS_DICT
        self._email_to_group_role_map = {}
        self._initialize_email_to_group_role_map()

        # Removed global pending state attributes, will be part of conversation_states
        self.logger.info("Agent initialized.")

        # Initialize RetrievalService for report_vector_db
        self.retrieval_service = None
        if RetrievalService: # Check if it was imported successfully
            try:
                self.retrieval_service = RetrievalService()
                self.logger.info("RetrievalService initialized successfully for report_vector_db.")
            except Exception as e: # Catch FileNotFoundError and other potential errors
                self.logger.error(f"Failed to initialize RetrievalService: {e}. report_vector_db will be unavailable.", exc_info=True)


    async def _execute_specific_tool(self, 
                                    tool_name: str, 
                                    details: Dict, 
                                    query: str, 
                                    user_permission_role: str, # Role for access checks
                                    original_user_id: str,      # Original ID for context (e.g., DM summary, message attribution)
                                    conversation_id: str,       # Added conversation_id
                                    router: SemanticRouter, 
                                    conversation_history: list, 
                                    llm_for_tool_data_fetching: 'GeminiLLM', 
                                    thoughts_adapter: logging.LoggerAdapter,
                                    # Add year and month, though not all tools use them directly,
                                    # but data fetching for tools might.
                                    last_retrieved_data: Union[str, None], 
                                    slack_client=None
                                    ) -> Dict[str, Any]:
        thoughts_adapter.info(f"Agent Method _execute_specific_tool: '{tool_name}'. Permission Role: '{user_permission_role}', Original User ID: '{original_user_id}'. Details: {details}")
        if tool_name == "chart_generator":
            data_req = details.get("data_request", query) 
            thoughts_adapter.info(f"Chart Generator: Data request is '{data_req}'. Routing to find data source.")
            data_kb = router.route(f"Get data for: {data_req}", conversation_history, thoughts_adapter) 
            if data_kb == "default" or data_kb in router.tools:
                thoughts_adapter.warning(f"Chart Generator: Could not route data request '{data_req}'. Defaulting data source to 'web_search'.")
                data_kb = "web_search"
            else:
                thoughts_adapter.info(f"Chart Generator: Data source for chart data determined as '{data_kb}'.")
            
            access_res = determine_access(user_permission_role, data_kb)
            if access_res["status"] != "Access Granted":
                thoughts_adapter.warning(f"Chart Generator: Access DENIED for role '{user_permission_role}' to data source '{data_kb}'. Reason: {access_res.get('reason','')}")
                return {
                    "final_response": f"Access Denied: Role '{user_permission_role}' cannot access data source '{data_kb}' for chart. {access_res.get('reason','')}",
                    "determined_kb_or_tool": tool_name,
                    "generated_sql": None
                }
            
            # For chart generator, if data_kb is customer_db, it would need year/month.
            # This implies a more complex state or re-prompting if not available.
            # For now, assuming data_req for charts won't typically hit customer_db without prior clarification,
            # or the tool's data_request needs to be very specific.
            # If data_kb is customer_db, this call to query_knowledge_base would ideally have year/month.
            # This is a deeper refactor point if tools frequently need sharded customer_db.
            # For this iteration, we'll assume non-sharded sources for tool data or that clarification happened before tool routing.
            qkb_result = await query_knowledge_base(data_req, data_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter) # year/month not passed here yet

            thoughts_adapter.info(f"Chart Generator: Result from querying data source '{data_kb}': {qkb_result}")
            chart_data_for_function = qkb_result["final_response"] 
            logger.info(f"Data received for chart generation (first 200 chars): {str(chart_data_for_function)[:200]}")
            internal_sql = qkb_result["generated_sql"]
            
            final_chart_response = generate_chart(chart_data_for_function, details.get("chart_type", "bar"), details.get("title", "Chart"), thoughts_adapter)
            return {
                "final_response": final_chart_response,
                "determined_kb_or_tool": tool_name,
                "generated_sql": internal_sql,
                "final_reaction": "chart_with_upwards_trend" # Already present, good.
            }

        elif tool_name == "report_generator":
            core_subject_source_type = details.get("core_subject_source_type")
            core_subject_query = details.get("core_subject_query")
            report_title = details.get("report_title", f"Analysis Report on: {query[:50]}")
            hypothetical_questions = details.get("hypothetical_questions", [])
            core_information_for_report = "No specific prior information was identified as the core for the report."
            all_sql_queries = []

            if core_subject_source_type == "PREVIOUS_ASSISTANT_OUTPUT":
                thoughts_adapter.info("Report Generator: Using information from previous assistant output as core subject.")
                retrieved_core_info = last_retrieved_data or (conversation_history[-1]["content"] if conversation_history and conversation_history[-1]["role"] == "assistant" else None)
                if retrieved_core_info: core_information_for_report = retrieved_core_info
                if report_title.startswith("Analysis Report on:") and conversation_history and len(conversation_history) >= 2 and conversation_history[-2]["role"] == "user":
                    report_title = f"Analysis Report on: {conversation_history[-2]['content'][:50]}"
            elif core_subject_source_type == "NEW_QUERY":
                actual_core_subject_query = core_subject_query or query
                if not actual_core_subject_query:
                    return {"final_response": "Error: Could not determine core info for report (missing query).", "determined_kb_or_tool": tool_name, "generated_sql": None}
                info_kb = router.route(f"Get information for: {actual_core_subject_query}", conversation_history, thoughts_adapter)
                info_kb = "web_search" if info_kb == "default" or info_kb in router.tools else info_kb
                access_res_core = determine_access(user_permission_role, info_kb)
                if access_res_core["status"] != "Access Granted":
                    return {"final_response": f"Access Denied: Role '{user_permission_role}' cannot access '{info_kb}' for report. {access_res_core.get('reason','')}", "determined_kb_or_tool": tool_name, "generated_sql": None}
                # Similar to chart_generator, year/month might be needed if info_kb is customer_db
                qkb_core_result = await query_knowledge_base(actual_core_subject_query, info_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter, agent_instance=self) # year/month not passed, pass agent_instance
                core_information_for_report = qkb_core_result["final_response"]
                if qkb_core_result["generated_sql"]: all_sql_queries.append(f"-- Core Subject Info SQL ({info_kb}):\n{qkb_core_result['generated_sql']}")
                if qkb_core_result["generated_sql"] is None and any(keyword in str(core_information_for_report).lower() for keyword in ["error:", "could not access", "failed to retrieve"]):
                    return qkb_core_result # Return error from KB directly
            else:
                return {"final_response": "Error: Invalid report parameters.", "determined_kb_or_tool": tool_name, "generated_sql": None}

            answered_hypothetical_questions_str_list = []
            if hypothetical_questions:
                for i, hq in enumerate(hypothetical_questions):
                    if not hq.strip(): continue
                    kb_for_hq = router.route(hq, conversation_history, thoughts_adapter)
                    if kb_for_hq == "default" or kb_for_hq in router.tools: continue
                    access_res_hq = determine_access(user_permission_role, kb_for_hq)
                    if access_res_hq["status"] != "Access Granted": continue
                    try:
                        # year/month might be needed if kb_for_hq is customer_db
                        qkb_hq_result = await query_knowledge_base(hq, kb_for_hq, conversation_history, llm_for_tool_data_fetching, thoughts_adapter) # year/month not passed
                        hq_answer, hq_sql = qkb_hq_result.get("final_response"), qkb_hq_result.get("generated_sql")
                        if hq_sql is None and any(keyword in str(hq_answer).lower() for keyword in ["error:", "could not access"]): continue
                        if hq_answer and not str(hq_answer).strip().startswith("Error: No specific information found"):
                            answered_hypothetical_questions_str_list.append(f"Regarding: \"{hq}\"\n(Info from: {kb_for_hq})\n---\n{hq_answer}\n---")
                            if hq_sql: all_sql_queries.append(f"-- HQ SQL {i+1} ({kb_for_hq} for '{hq}'):\n{hq_sql}")
                    except Exception as e: thoughts_adapter.error(f"Report Gen: Error answering HQ '{hq}': {e}", exc_info=True)
            
            final_info = f"Core Subject Info:\n---\n{core_information_for_report}\n---"
            if answered_hypothetical_questions_str_list:
                final_info += "\n\nAdditional Context:\n" + "\n\n".join(answered_hypothetical_questions_str_list)
            if core_information_for_report == "No specific prior information was identified as the core for the report." and not answered_hypothetical_questions_str_list:
                final_info = "Could not retrieve specific information for the report."

            final_report_response = generate_report(final_info, llm_for_tool_data_fetching, conversation_history, thoughts_adapter, report_title)
            return {
                "final_response": final_report_response, 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": "\n\n".join(all_sql_queries) if all_sql_queries else None,
                "final_reaction": "memo" # Already present, good.
            }

        elif tool_name == "table_generator":
            data_req = details.get("data_request", query)
            table_title = details.get("title", "Data Table")
            columns_hint = details.get("columns_hint")
            table_data_json_str, internal_sql = None, None

            if data_req == "USE_PREVIOUS_ASSISTANT_OUTPUT":
                table_data_json_str = last_retrieved_data
                if not table_data_json_str and conversation_history and conversation_history[-1]["role"] == "assistant":
                    table_data_json_str = conversation_history[-1]["content"]
                if not table_data_json_str: table_data_json_str = "{}"
                if (not table_title or table_title == "Data Table") and conversation_history and len(conversation_history) >=2 and conversation_history[-2]["role"] == "user":
                    table_title = f"Table for: {conversation_history[-2]['content'][:50]}"
            else:
                data_kb = router.route(f"Get data for: {data_req}", conversation_history, thoughts_adapter)
                data_kb = "web_search" if data_kb == "default" or data_kb in router.tools else data_kb
                access_res = determine_access(user_permission_role, data_kb)
                if access_res["status"] != "Access Granted":
                    return {"final_response": f"Access Denied: Role '{user_permission_role}' cannot access '{data_kb}' for table. {access_res.get('reason','')}", "determined_kb_or_tool": tool_name, "generated_sql": None}
                # year/month might be needed if data_kb is customer_db
                qkb_result = await query_knowledge_base(data_req, data_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter) # year/month not passed
                table_data_json_str, internal_sql = qkb_result["final_response"], qkb_result.get("generated_sql")

            if table_data_json_str is None:
                return {"final_response": "Error: Could not retrieve data for table.", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql}
            
            csv_gen_result = generate_csv_file_from_json(table_data_json_str, table_title, thoughts_adapter, columns_hint)
            return {
                "final_response": csv_gen_result["message"], 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": internal_sql,
                "final_reaction": "page_facing_up" if csv_gen_result.get("filename") else "warning"
            }
        elif tool_name == "message_sender_tool":
            recipient = details.get("recipient")
            message_instruction = details.get("message_content_instruction")
            actual_message_text = ""
            internal_sql_for_message_content = None

            if not recipient or message_instruction is None:
                return {
                    "final_response": "Error: Missing recipient or message content for message_sender_tool.", 
                    "determined_kb_or_tool": tool_name, 
                    "generated_sql": None,
                    "final_reaction": "warning"
                }
            if message_instruction == "USE_PREVIOUS_ASSISTANT_OUTPUT":
                actual_message_text = last_retrieved_data
                if not actual_message_text and conversation_history and conversation_history[-1]["role"] == "assistant":
                    actual_message_text = conversation_history[-1]["content"]
                if not actual_message_text: actual_message_text = "Couldn't find previous info to send."
                try: # Format if JSON
                    json.loads(actual_message_text)
                    actual_message_text = f"Data you asked to send:\n```json\n{actual_message_text}\n```"
                except json.JSONDecodeError: pass
            elif any(message_instruction.lower().startswith(phrase) for phrase in ["generate a report", "get table for"]):
                data_kb = router.route(f"Get data for: {message_instruction}", conversation_history, thoughts_adapter)
                data_kb = "web_search" if data_kb == "default" or data_kb in router.tools else data_kb
                # year/month might be needed if data_kb is customer_db
                qkb_result = await query_knowledge_base(message_instruction, data_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter) # year/month not passed
                actual_message_text = qkb_result.get("final_response", "Could not retrieve content for message.")
                internal_sql_for_message_content = qkb_result.get("generated_sql")
            else:
                actual_message_text = message_instruction
            
            if not slack_client:
                return {"final_response": "Error: Slack client missing for message_sender_tool.", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_message_content, "final_reaction": "warning"}

            send_status = await send_slack_message(recipient, actual_message_text, original_user_id, slack_client, thoughts_adapter)
            return {"final_response": send_status, "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_message_content}
            
        elif tool_name == "financial_reporting_tool":
            response_text = query_financial_reporting_tool(details, thoughts_adapter)
            return {
                "final_response": response_text, 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": None,
                "final_reaction": "briefcase"
            }
        elif tool_name == "marketing_analytics_dashboard":
            response_text = access_marketing_analytics_dashboard(details, thoughts_adapter)
            return {
                "final_response": response_text, 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": None,
                "final_reaction": "bar_chart"
            }
        elif tool_name == "social_media_management_tool":
            response_text = use_social_media_management_tool(details.get("social_action","view"), thoughts_adapter, details.get("content_summary",""))
            return {
                "final_response": response_text, 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": None,
                "final_reaction": "speech_balloon"
            }
        elif tool_name == "product_roadmap_tool":
            response_text = view_product_roadmap_tool(details.get("query","general status"), thoughts_adapter)
            return {
                "final_response": response_text, 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": None,
                "final_reaction": "spiral_calendar_pad"
            }
        elif tool_name == "summarize_dm_conversation_tool":
            dm_user_id = original_user_id # Use original_user_id to fetch specific user's DM history
            thoughts_adapter.info(f"Summarize DM Tool: Attempting to summarize DMs for user ID: {dm_user_id}")

            try:
                dm_conversation_state = self._get_conversation_state(dm_user_id)
                dm_history = dm_conversation_state.get("history", []) # type: ignore
                thoughts_adapter.debug(f"Summarize DM Tool: Fetched DM history for {dm_user_id}. Number of turns: {len(dm_history)}")

                if not dm_history:
                    thoughts_adapter.info(f"Summarize DM Tool: No DM history found for user {dm_user_id}.")
                    return {
                        "final_response": "I couldn't find any direct message history with you to summarize.", 
                        "determined_kb_or_tool": tool_name, 
                        "generated_sql": None, 
                        "final_reaction": "open_file_folder"
                    }
                formatted_dm_history = _format_history_for_prompt(dm_history)
                summarization_prompt = f"""The following is a direct message (DM) conversation history between a user and you (Zia, an AI assistant).
The user has requested a summary of this DM conversation.
--- DM HISTORY START ---
{formatted_dm_history}
--- DM HISTORY END ---
Your task is to provide a concise, structured summary of this DM conversation.
Focus on the key topics discussed, main questions asked by the user, and significant information or outcomes.
{GENERAL_OUTPUT_GUIDELINES}
Use Slack mrkdwn formatting for clarity (e.g., bullet points for topics, bolding for key terms).
If the conversation is very short or seems trivial, provide a brief note to that effect.
Respond ONLY with the structured summary.
Summary:"""
                thoughts_adapter.debug(f"Summarize DM Tool: Summarization prompt (first 300 chars): {summarization_prompt[:300]}...")
                summary = llm_for_tool_data_fetching.generate(summarization_prompt)
                thoughts_adapter.info(f"Summarize DM Tool: Generated summary (first 100 chars): {summary[:100]}...")
                return {
                    "final_response": summary, 
                    "determined_kb_or_tool": tool_name, 
                    "generated_sql": None, 
                    "final_reaction": "scroll"
                }            
            except Exception as e: # More general exception handling
                thoughts_adapter.error(f"Summarize DM Tool: Error during DM summarization for user {dm_user_id}: {e}", exc_info=True)
                return {
                    "final_response": f"Sorry, I encountered an error while trying to summarize our DMs: {type(e).__name__}", 
                    "determined_kb_or_tool": tool_name, 
                    "generated_sql": None, 
                    "final_reaction": "warning"
                }            
        # This is the fallback return if the tool_name doesn't match any of the above elif blocks
        thoughts_adapter.error(f"Tool Execution: Tool '{tool_name}' is not recognized or its execution logic is missing within _execute_specific_tool.")
        return {
            "final_response": f"Error: The tool '{tool_name}' is not recognized or I'm not equipped to handle it yet.",
            "determined_kb_or_tool": tool_name,
            "generated_sql": None
        }

    # This return statement was previously outside the _execute_specific_tool method, causing the error.
    # It should be part of the method's logic, typically as a final fallback if no specific tool is matched.
    # However, the above 'elif' chain should cover all tools or end with a specific fallback.
    # The corrected logic above now includes a final fallback within the method.
    # This dedented block is now effectively removed by ensuring the method has its own comprehensive return paths.

async def _handle_tool_destination(
    agent_instance: 'Agent', 
    query: str, tool_name: str, 
    actual_user_role: str, # Role for permission checks
    original_user_id: str,  # Original user ID for context
    conversation_id: str, # Added conversation_id for context like "CURRENT_CONVERSATION_CONTEXT"
    router: 'SemanticRouter', conversation_history: list, last_retrieved_data: Union[str, None], slack_client, # Added slack_client
    llm_for_details: GeminiLLM, thoughts_adapter: logging.LoggerAdapter
) -> Dict[str, Any]: # Added async
    thoughts_adapter.info(f"Handling Tool Destination: Tool='{tool_name}', Query='{query}', Role='{actual_user_role}'")
    details = _get_tool_parameters(query, tool_name, llm_for_details, conversation_history, thoughts_adapter)
    if not details or details.get("error") == "param_extraction_failed": # Check 'error' key
        thoughts_adapter.warning(f"Handle Tool: Parameter extraction failed for tool '{tool_name}'. Query: '{query}'")
        return {
            "final_response": f"I understood you want to use {tool_name}, but I had trouble with the specific details. Please rephrase.",
            "determined_kb_or_tool": tool_name,
            "generated_sql": None
        }
    thoughts_adapter.info(f"Handle Tool: Parameters extracted for '{tool_name}': {details}")
    # Call the Agent's _execute_specific_tool method
    return await agent_instance._execute_specific_tool(tool_name, details, query, actual_user_role, original_user_id, conversation_id, router, conversation_history, llm_for_details, thoughts_adapter, last_retrieved_data, slack_client)

def _generate_conversational_fallback_response(query: str, conversation_history: list, llm_for_details: 'GeminiLLM', thoughts_adapter: logging.LoggerAdapter) -> str:
    raise NotImplementedError


async def _handle_default_routing(
    agent_instance: 'Agent', query: str, actual_user_role: str, original_user_id: str, router: 'SemanticRouter', conversation_history: list, 
    llm_for_details: 'GeminiLLM', thoughts_adapter: logging.LoggerAdapter, conversation_id: str # Added conversation_id
) -> Dict[str, Any]:
    # Ensure agent_instance is passed to query_knowledge_base calls within this function
    thoughts_adapter.info(
        f"Handling Default Routing: SemanticRouter defaulted. Query='{query}', Role='{actual_user_role}'. Using general LLM call for action/detail extraction."
    )
    
    all_routable_destinations = {**router.knowledge_banks, **router.tools}
    # destination_names_for_prompt = ', '.join(all_routable_destinations.keys()) # Not used in current prompt
    history_context = _format_history_for_prompt(conversation_history)
    # The agent_instance parameter is available in this function's scope.

    prompt = f"""{history_context}Current user query: "{query}".
    Analyze the current query, considering the conversation history (if any). Determine action and details.
    Actions: "knowledge_base", "specific_tool", "unknown".

    1. If the primary goal is to query a knowledge_base directly for information:
       - Identify bank from: {', '.join(router.knowledge_banks.keys())}.
       - JSON: {{"action": "knowledge_base", "details": {{"knowledge_bank": "bank_name"}}}}

    2. If "specific_tool":
       - Identify tool from: {', '.join(router.tools.keys())}.
       - Then, extract parameters for that specific tool.
       - For 'chart_generator', the parameters should include a 'data_request' field which is a query to get the actual data for the chart (e.g., "top 5 products and their sales figures").
       - Example for chart_generator: {{"action": "specific_tool", "details": {{"tool_name": "chart_generator", "parameters": {{"data_request": "List top clients by revenue", "chart_type": "bar", "title": "Top Clients by Revenue"}}}}}}
       - For other tools, extract relevant parameters.
       - JSON: {{"action": "specific_tool", "details": {{"tool_name": "chosen_tool", "parameters": {{...tool_specific_extracted_params...}}}}}}
       
    3. If "unknown":
       - JSON: {{"action": "unknown", "details": {{}}}}

    Respond ONLY with JSON.
    """
    thoughts_adapter.debug(f"Default Routing: LLM prompt for action/detail extraction:\n{prompt}")
    response_text = llm_for_details.generate(prompt)
    thoughts_adapter.info(f"Default Routing: LLM raw response for action/detail extraction: '{response_text}'")
    gemini_output = parse_llm_json_output(response_text, thoughts_adapter)

    if not gemini_output or "action" not in gemini_output:
        thoughts_adapter.error(f"Default Routing: LLM response parsing failed or 'action' key missing. Output: {gemini_output}")
        final_fallback_response = _generate_conversational_fallback_response(query, conversation_history, llm_for_details, thoughts_adapter)
        return {
            "final_response": final_fallback_response,
            "determined_kb_or_tool": "default_conversational_fallback_parser_error",
            "generated_sql": None
        }


    action = gemini_output["action"]
    details = gemini_output.get("details", {})

    if action == "knowledge_base":
        kb = details.get("knowledge_bank")
        if kb and kb in router.knowledge_banks:
            thoughts_adapter.info(f"Default Routing: Determined action 'knowledge_base', target KB: '{kb}'")
            access_res = determine_access(actual_user_role, kb)
            if access_res["status"] != "Access Granted":
                thoughts_adapter.warning(f"Default Routing (KB): Access DENIED for role '{actual_user_role}' to KB '{kb}'. Reason: {access_res.get('reason', '')}")
                return {
                    "final_response": f"Access Denied: {access_res.get('reason', f'Role {actual_user_role} cannot access KB {kb}')}",
                    "determined_kb_or_tool": kb,
                    "generated_sql": None
                }
            thoughts_adapter.info(f"Default Routing (KB): Access GRANTED for role '{actual_user_role}' to KB '{kb}'.")
            # If kb is customer_db, this call needs year/month.
            # This implies that default routing leading to customer_db would also need prior clarification.
            # For now, assuming default routing won't directly hit customer_db without more specific user intent that would have been routed earlier.
            return await query_knowledge_base(query, kb, conversation_history, llm_for_details, thoughts_adapter, agent_instance=agent_instance)
        else:
            thoughts_adapter.warning(f"Default Routing (KB): LLM suggested invalid/no bank ('{kb}'). Defaulting to 'web_search'.")
            access_res = determine_access(actual_user_role, "web_search")
            if access_res["status"] != "Access Granted":
                thoughts_adapter.warning(f"Default Routing (KB): Access DENIED for role '{actual_user_role}' to fallback 'web_search'. Reason: {access_res.get('reason', '')}")
                return {
                    "final_response": f"Access Denied: {access_res.get('reason', f'Role {actual_user_role} cannot access fallback web_search')}",
                    "determined_kb_or_tool": "web_search",
                    "generated_sql": None
                }
            thoughts_adapter.info(f"Default Routing (KB): Access GRANTED for role '{actual_user_role}' to fallback 'web_search'.")
            return await query_knowledge_base(query, "web_search", conversation_history, llm_for_details, thoughts_adapter, agent_instance=agent_instance)
            
    elif action == "specific_tool":
        tool_name = details.get("tool_name")
        tool_params = details.get("parameters") # Get the whole parameters dict

        if not tool_name or tool_name not in router.tools:
            thoughts_adapter.error(f"Default Routing (Tool): LLM suggested an unknown or unspecified tool: '{tool_name}'")
            return {
                "final_response": f"Fallback LLM suggested an unknown or unspecified tool: '{tool_name}'. Please clarify.",
                "determined_kb_or_tool": tool_name if tool_name else "unknown_tool_fallback",
                "generated_sql": None
            }
        thoughts_adapter.info(f"Default Routing: Determined action 'specific_tool', target Tool: '{tool_name}', initial params: {tool_params}")
        access_res = determine_access(actual_user_role, tool_name)
        if access_res["status"] != "Access Granted":
            thoughts_adapter.warning(f"Default Routing (Tool): Access DENIED for role '{actual_user_role}' to tool '{tool_name}'. Reason: {access_res.get('reason', '')}")
            return {
                "final_response": f"Access Denied: {access_res.get('reason', f'Role {actual_user_role} cannot use tool {tool_name}')}",
                "determined_kb_or_tool": tool_name,
                "generated_sql": None
            }
        
        # If parameters are missing or not a dict, try specific extraction
        if not isinstance(tool_params, dict) or not tool_params:
            thoughts_adapter.warning(f"Default Routing (Tool): LLM identified tool '{tool_name}', but parameters missing/invalid. Attempting specific parameter extraction.")
            tool_params = _get_tool_parameters(query, tool_name, llm_for_details, conversation_history, thoughts_adapter)
            if not tool_params or tool_params.get("error") == "param_extraction_failed": # Check 'error' key
                thoughts_adapter.warning(f"Default Routing (Tool): Specific parameter extraction failed for tool '{tool_name}' after fallback attempt.")
                return {
                    "final_response": f"I understood you want to use {tool_name}, but had trouble with the specific details after fallback. Please rephrase.",
                    "determined_kb_or_tool": tool_name,
                    "generated_sql": None # Or potentially details.get("original_query") if that's useful
                }
        thoughts_adapter.info(f"Default Routing (Tool): Access GRANTED for role '{actual_user_role}' to tool '{tool_name}'. Proceeding with execution using params: {tool_params}")
        return await agent_instance._execute_specific_tool(
            tool_name, tool_params, query, 
            actual_user_role, original_user_id, conversation_id, # Pass conversation_id
            router, # Pass router
            conversation_history, 
            llm_for_details, # Pass llm_for_details as llm_for_tool_data_fetching
            thoughts_adapter, 
            None, # last_retrieved_data (not available in default routing context)
            agent_instance.slack_app.client if hasattr(agent_instance, 'slack_app') and agent_instance.slack_app else None
        )

    else: 
        thoughts_adapter.warning(f"Default Routing: LLM returned unknown action: '{action}'")
        final_fallback_response = _generate_conversational_fallback_response(query, conversation_history, llm_for_details, thoughts_adapter)
        return {
            "final_response": final_fallback_response,
            "determined_kb_or_tool": f"default_conversational_fallback_unknown_action ({action})",
            "generated_sql": None
        }

DEFAULT_USER_ROLE = "limited_access" # Define a default role

class Agent:
    def __init__(self, slack_app=None): # Add slack_app parameter
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.conversation_history = []
        self.conversation_states = {} # Key: conversation_id, Value: dict of state
        self.router = SemanticRouter()
        self.slack_app = slack_app # Store slack_app instance
        # Ensure GEMINI_API_KEY is available for model initialization
        if not GEMINI_API_KEY:
            self.logger.critical("GEMINI_API_KEY not found. Agent cannot initialize LLM.")
            # Handle this critical failure, perhaps by raising an error
            self.llm_for_details = None 
        else:
            self.llm_for_details = GeminiLLM() # Use the GeminiLLM wrapper
        self.MAX_HISTORY_TURNS = MAX_HISTORY_TURNS # Use the module-level constant or define here

        # Store access control and user group data, and pre-process user groups for faster lookups
        self.access_controls = ACCESS_CONTROL_DICT
        self.user_groups = USER_GROUPS_DICT
        self._email_to_group_role_map = {}
        self._initialize_email_to_group_role_map()

        # Removed global pending state attributes, will be part of conversation_states
        self.logger.info("Agent initialized.")

    async def _add_reaction_safely(self, reaction_name: str, channel_id: Optional[str], message_ts: Optional[str], thoughts_adapter: logging.LoggerAdapter):
        if self.slack_app and self.slack_app.client and channel_id and message_ts and reaction_name:
            try:
                await self.slack_app.client.reactions_add(
                    channel=channel_id,
                    name=reaction_name,
                    timestamp=message_ts
                )
                thoughts_adapter.info(f"Added reaction '{reaction_name}'.")
            except Exception as e:
                thoughts_adapter.error(f"Failed to add reaction '{reaction_name}': {e}", exc_info=True)

    async def _execute_specific_tool(self, 
                                    tool_name: str, 
                                    details: Dict, 
                                    query: str, 
                                    user_permission_role: str, # Role for access checks
                                    original_user_id: str,      # Original ID for context (e.g., DM summary, message attribution)
                                    conversation_id: str,       # Added conversation_id
                                    router: SemanticRouter, 
                                    conversation_history: list, 
                                    llm_for_tool_data_fetching: 'GeminiLLM', 
                                    thoughts_adapter: logging.LoggerAdapter, 
                                    last_retrieved_data: Union[str, None], 
                                    slack_client=None
                                    ) -> Dict[str, Any]:
        if tool_name == "plotly_chart_generator":
            if not plot_from_dataframe: # Check if imported
                thoughts_adapter.error("Plotly Chart Generator: plot_from_dataframe function not available (import failed).")
                return {"final_response": "Error: The plotting tool is currently unavailable.", "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "warning"}

            chart_description = details.get("chart_description_for_plotly", query)
            data_request_query = details.get("data_request_query_for_chart", query)
            chart_data_df = None
            internal_sql_for_chart_data = None

            if data_request_query == "USE_PREVIOUS_ASSISTANT_OUTPUT":
                data_str = last_retrieved_data or (conversation_history[-1]["content"] if conversation_history and conversation_history[-1]["role"] == "assistant" else None)
                if data_str:
                    try:
                        chart_data_df = pd.read_json(io.StringIO(data_str)) # Assumes previous data was JSON convertible to DataFrame
                        thoughts_adapter.info(f"Plotly Chart Generator: Successfully created DataFrame from previous assistant output. Shape: {chart_data_df.shape if chart_data_df is not None else 'None'}")
                    except Exception as e:
                        thoughts_adapter.error(f"Plotly Chart Generator: Failed to create DataFrame from previous output: {e}. Data: {data_str[:200]}")
                        return {"final_response": "I couldn't use the previous data to make a chart. It might not be in the right format.", "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "warning"}
            else:
                data_kb = router.route(f"Get data for: {data_request_query}", conversation_history, thoughts_adapter)
                data_kb = "web_search" if data_kb == "default" or data_kb in router.tools else data_kb
                access_res = determine_access(user_permission_role, data_kb)
                if access_res["status"] != "Access Granted":
                    return {"final_response": f"Access Denied: Role '{user_permission_role}' cannot access data source '{data_kb}' for chart. {access_res.get('reason','')}", "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "no_entry_sign"}
                
                qkb_result = await query_knowledge_base(data_request_query, data_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter, agent_instance=self) # Pass agent_instance
                data_str_from_kb = qkb_result.get("final_response")
                internal_sql_for_chart_data = qkb_result.get("generated_sql")
                if data_str_from_kb:
                    try:
                        # Attempt to parse as JSON first
                        chart_data_df = pd.read_json(io.StringIO(data_str_from_kb))
                        thoughts_adapter.info(f"Plotly Chart Generator: Successfully created DataFrame from KB '{data_kb}' (parsed as JSON). Shape: {chart_data_df.shape if chart_data_df is not None else 'None'}")
                    except ValueError: # If not valid JSON for pd.read_json
                        thoughts_adapter.info(f"Plotly Chart Generator: Data from KB '{data_kb}' not direct JSON for DataFrame, trying as CSV-like string.")
                        try:
                            chart_data_df = pd.read_csv(io.StringIO(data_str_from_kb)) # Fallback for CSV-like string
                            thoughts_adapter.info(f"Plotly Chart Generator: Successfully created DataFrame from KB '{data_kb}' (parsed as CSV). Shape: {chart_data_df.shape if chart_data_df is not None else 'None'}")
                        except Exception as e_csv:
                            thoughts_adapter.error(f"Plotly Chart Generator: Failed to create DataFrame from KB data (tried JSON then CSV): {e_csv}. Data: {str(data_str_from_kb)[:200]}")
                            return {"final_response": f"I got data from {data_kb}, but couldn't prepare it for charting (tried JSON and CSV). Data: {str(data_str_from_kb)[:100]}", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_chart_data, "final_reaction": "warning"}
                    except Exception as e_json: # Other errors from read_json
                        thoughts_adapter.error(f"Plotly Chart Generator: Failed to create DataFrame from KB data (JSON attempt failed with non-ValueError): {e_json}. Data: {str(data_str_from_kb)[:200]}")
                        return {"final_response": f"I got data from {data_kb}, but encountered an issue preparing it for charting (JSON error). Data: {str(data_str_from_kb)[:100]}", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_chart_data, "final_reaction": "warning"}

            if chart_data_df is None or chart_data_df.empty:
                return {"final_response": "I couldn't find any data to plot based on your request.", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_chart_data, "final_reaction": "warning"}

            plot_filename_relative = plot_from_dataframe(chart_data_df, chart_description, config.plot_hosting_dir, GEMINI_API_KEY, preferred_format="html")

            if plot_filename_relative and not plot_filename_relative.startswith("Error:"):
                plot_url = f"{config.app_base_url}/{config.plot_hosting_url_path}/{plot_filename_relative}"
                return {"final_response": f"I've generated the chart for you! You can view it here: <{plot_url}|{chart_description[:50].replace('_',' ')}...>", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_chart_data, "final_reaction": "chart_with_upwards_trend"}
            else:
                return {"final_response": f"Sorry, I couldn't generate the chart. {plot_filename_relative}", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql_for_chart_data, "final_reaction": "x"}

        if tool_name == "report_generator":
            core_subject_source_type = details.get("core_subject_source_type")
            core_subject_query = details.get("core_subject_query")
            report_title = details.get("report_title", f"Analysis Report on: {query[:50]}")
            hypothetical_questions = details.get("hypothetical_questions", [])
            core_information_for_report = "No specific prior information was identified as the core for the report."
            all_sql_queries = []

            if core_subject_source_type == "PREVIOUS_ASSISTANT_OUTPUT":
                thoughts_adapter.info("Report Generator: Using information from previous assistant output as core subject.")
                retrieved_core_info = last_retrieved_data or (conversation_history[-1]["content"] if conversation_history and conversation_history[-1]["role"] == "assistant" else None)
                if retrieved_core_info: core_information_for_report = retrieved_core_info
                if report_title.startswith("Analysis Report on:") and conversation_history and len(conversation_history) >= 2 and conversation_history[-2]["role"] == "user":
                    report_title = f"Analysis Report on: {conversation_history[-2]['content'][:50]}"
            elif core_subject_source_type == "NEW_QUERY":
                actual_core_subject_query = core_subject_query or query
                if not actual_core_subject_query:
                    return {"final_response": "Error: Could not determine core info for report (missing query).", "determined_kb_or_tool": tool_name, "generated_sql": None}
                info_kb = router.route(f"Get information for: {actual_core_subject_query}", conversation_history, thoughts_adapter)
                info_kb = "web_search" if info_kb == "default" or info_kb in router.tools else info_kb
                access_res_core = determine_access(user_permission_role, info_kb)
                if access_res_core["status"] != "Access Granted":
                    return {"final_response": f"Access Denied: Role '{user_permission_role}' cannot access '{info_kb}' for report. {access_res_core.get('reason','')}", "determined_kb_or_tool": tool_name, "generated_sql": None}
                qkb_core_result = await query_knowledge_base(actual_core_subject_query, info_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter)
                core_information_for_report = qkb_core_result["final_response"]
                if qkb_core_result["generated_sql"]: all_sql_queries.append(f"-- Core Subject Info SQL ({info_kb}):\n{qkb_core_result['generated_sql']}")
                if qkb_core_result["generated_sql"] is None and any(keyword in str(core_information_for_report).lower() for keyword in ["error:", "could not access", "failed to retrieve"]):
                    return qkb_core_result # Return error from KB directly
            else:
                return {"final_response": "Error: Invalid report parameters.", "determined_kb_or_tool": tool_name, "generated_sql": None}

            answered_hypothetical_questions_str_list = []
            if hypothetical_questions:
                for i, hq in enumerate(hypothetical_questions):
                    if not hq.strip(): continue
                    kb_for_hq = router.route(hq, conversation_history, thoughts_adapter)
                    if kb_for_hq == "default" or kb_for_hq in router.tools: continue
                    access_res_hq = determine_access(user_permission_role, kb_for_hq)
                    if access_res_hq["status"] != "Access Granted": continue
                    try:
                        qkb_hq_result = await query_knowledge_base(hq, kb_for_hq, conversation_history, llm_for_tool_data_fetching, thoughts_adapter)
                        hq_answer, hq_sql = qkb_hq_result.get("final_response"), qkb_hq_result.get("generated_sql")
                        if hq_sql is None and any(keyword in str(hq_answer).lower() for keyword in ["error:", "could not access"]): continue
                        if hq_answer and not str(hq_answer).strip().startswith("Error: No specific information found"):
                            answered_hypothetical_questions_str_list.append(f"Regarding: \"{hq}\"\n(Info from: {kb_for_hq})\n---\n{hq_answer}\n---")
                            if hq_sql: all_sql_queries.append(f"-- HQ SQL {i+1} ({kb_for_hq} for '{hq}'):\n{hq_sql}")
                    except Exception as e: thoughts_adapter.error(f"Report Gen: Error answering HQ '{hq}': {e}", exc_info=True)
            
            final_info = f"Core Subject Info:\n---\n{core_information_for_report}\n---"
            if answered_hypothetical_questions_str_list:
                final_info += "\n\nAdditional Context:\n" + "\n\n".join(answered_hypothetical_questions_str_list)
            if core_information_for_report == "No specific prior information was identified as the core for the report." and not answered_hypothetical_questions_str_list:
                final_info = "Could not retrieve specific information for the report."

            final_report_response = generate_report(final_info, llm_for_tool_data_fetching, conversation_history, thoughts_adapter, report_title)
            return {"final_response": final_report_response, "determined_kb_or_tool": tool_name, "generated_sql": "\n\n".join(all_sql_queries) if all_sql_queries else None}

        elif tool_name == "table_generator":
            data_req = details.get("data_request", query)
            table_title = details.get("title", "Data Table")
            columns_hint = details.get("columns_hint")
            table_data_json_str, internal_sql = None, None

            if data_req == "USE_PREVIOUS_ASSISTANT_OUTPUT":
                table_data_json_str = last_retrieved_data
                if not table_data_json_str and conversation_history and conversation_history[-1]["role"] == "assistant":
                    table_data_json_str = conversation_history[-1]["content"]
                if not table_data_json_str: table_data_json_str = "{}"
                if (not table_title or table_title == "Data Table") and conversation_history and len(conversation_history) >=2 and conversation_history[-2]["role"] == "user":
                     table_title = f"Table for: {conversation_history[-2]['content'][:50]}"
            else:
                data_kb = router.route(f"Get data for: {data_req}", conversation_history, thoughts_adapter)
                data_kb = "web_search" if data_kb == "default" or data_kb in router.tools else data_kb
                access_res = determine_access(user_permission_role, data_kb)
                if access_res["status"] != "Access Granted":
                    return {"final_response": f"Access Denied: Role '{user_permission_role}' cannot access '{data_kb}' for table. {access_res.get('reason','')}", "determined_kb_or_tool": tool_name, "generated_sql": None}
                qkb_result = await query_knowledge_base(data_req, data_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter)
                table_data_json_str, internal_sql = qkb_result["final_response"], qkb_result.get("generated_sql")

            if table_data_json_str is None:
                return {"final_response": "Error: Could not retrieve data for table.", "determined_kb_or_tool": tool_name, "generated_sql": internal_sql}
            
            csv_gen_result = generate_csv_file_from_json(table_data_json_str, table_title, thoughts_adapter, columns_hint)
            return {
                "final_response": csv_gen_result["message"], 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": internal_sql,
                "final_reaction": "page_facing_up" if csv_gen_result.get("filename") else "warning"
            }

        elif tool_name == "message_sender_tool":
            recipient_param = details.get("recipient") # Renamed to avoid conflict with a potential 'recipient' variable
            message_instruction = details.get("message_content_instruction")
            actual_message_text = ""
            internal_sql_for_message_content = None

            target_recipient = recipient_param # Default to the extracted parameter

            if recipient_param == "CURRENT_CONVERSATION_CONTEXT":
                target_recipient = conversation_id # Use the actual conversation_id (channel/DM ID)
                thoughts_adapter.info(f"Message Sender Tool: Recipient is CURRENT_CONVERSATION_CONTEXT, using conversation_id: {conversation_id}")


            if not target_recipient or message_instruction is None: # Check target_recipient
                return {"final_response": "Error: Missing recipient or message content.", "determined_kb_or_tool": tool_name, "generated_sql": None}

            if message_instruction == "USE_PREVIOUS_ASSISTANT_OUTPUT":
                actual_message_text = last_retrieved_data
                if not actual_message_text and conversation_history and conversation_history[-1]["role"] == "assistant":
                    actual_message_text = conversation_history[-1]["content"]
                if not actual_message_text: actual_message_text = "Couldn't find previous info to send."
                try: # Format if JSON
                    json.loads(actual_message_text)
                    actual_message_text = f"Data you asked to send:\n```json\n{actual_message_text}\n```"
                except json.JSONDecodeError: pass
            elif any(message_instruction.lower().startswith(phrase) for phrase in ["generate a report", "get table for"]):
                data_kb = router.route(f"Get data for: {message_instruction}", conversation_history, thoughts_adapter)
                data_kb = "web_search" if data_kb == "default" or data_kb in router.tools else data_kb
                qkb_result = await query_knowledge_base(message_instruction, data_kb, conversation_history, llm_for_tool_data_fetching, thoughts_adapter)
                actual_message_text = qkb_result.get("final_response", "Could not retrieve content for message.")
                internal_sql_for_message_content = qkb_result.get("generated_sql")
            else:
                actual_message_text = message_instruction
            
            if not slack_client:
                return {
                    "final_response": "Error: Slack client missing for message_sender_tool.", 
                    "determined_kb_or_tool": tool_name, 
                    "generated_sql": internal_sql_for_message_content,
                    "final_reaction": "warning"
                }
            send_status = await send_slack_message(target_recipient, actual_message_text, original_user_id, slack_client, thoughts_adapter) # Use target_recipient
            return {
                "final_response": send_status, 
                "determined_kb_or_tool": tool_name, 
                "generated_sql": internal_sql_for_message_content, 
                "final_reaction": "arrow_right" if "Okay, I've sent" in send_status else "warning"
            }            
        elif tool_name == "financial_reporting_tool":
            response_text = query_financial_reporting_tool(details, thoughts_adapter)
            return {"final_response": response_text, "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "briefcase"}
        elif tool_name == "marketing_analytics_dashboard":
            response_text = access_marketing_analytics_dashboard(details, thoughts_adapter)
            return {"final_response": response_text, "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "bar_chart"}
        elif tool_name == "social_media_management_tool":
            response_text = use_social_media_management_tool(details.get("social_action","view"), thoughts_adapter, details.get("content_summary",""))
            return {"final_response": response_text, "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "speech_balloon"}
        elif tool_name == "product_roadmap_tool":
            response_text = view_product_roadmap_tool(details.get("query","general status"), thoughts_adapter)
            return {"final_response": response_text, "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "spiral_calendar_pad"}
        elif tool_name == "summarize_dm_conversation_tool":
            dm_user_id = original_user_id # Use original_user_id to fetch specific user's DM history
            thoughts_adapter.info(f"Summarize DM Tool: Attempting to summarize DMs for user ID: {dm_user_id}")

            try:
                dm_conversation_state = self._get_conversation_state(dm_user_id)
                dm_history = dm_conversation_state.get("history", [])
                thoughts_adapter.debug(f"Summarize DM Tool: Fetched DM history for {dm_user_id}. Number of turns: {len(dm_history)}")

                if not dm_history:
                    thoughts_adapter.info(f"Summarize DM Tool: No DM history found for user {dm_user_id}.")
                    return {"final_response": "I couldn't find any direct message history with you to summarize.", "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "open_file_folder"}

                formatted_dm_history = _format_history_for_prompt(dm_history)
                summarization_prompt = f"""The following is a direct message (DM) conversation history between a user and you (Zia, an AI assistant).
The user has requested a summary of this DM conversation.
--- DM HISTORY START ---
{formatted_dm_history}
--- DM HISTORY END ---
Your task is to provide a concise, structured summary of this DM conversation.
Focus on the key topics discussed, main questions asked by the user, and significant information or outcomes.
{GENERAL_OUTPUT_GUIDELINES}
Use Slack mrkdwn formatting for clarity (e.g., bullet points for topics, bolding for key terms).
If the conversation is very short or seems trivial, provide a brief note to that effect.
Respond ONLY with the structured summary.
Summary:"""
                thoughts_adapter.debug(f"Summarize DM Tool: Summarization prompt (first 300 chars): {summarization_prompt[:300]}...")
                summary = llm_for_tool_data_fetching.generate(summarization_prompt)
                thoughts_adapter.info(f"Summarize DM Tool: Generated summary (first 100 chars): {summary[:100]}...")
                return {"final_response": summary, "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "scroll"}
            except Exception as e: # More general exception handling
                thoughts_adapter.error(f"Summarize DM Tool: Error during DM summarization for user {dm_user_id}: {e}", exc_info=True)
                return {"final_response": f"Sorry, I encountered an error while trying to summarize our DMs: {type(e).__name__}", "determined_kb_or_tool": tool_name, "generated_sql": None, "final_reaction": "warning"}
            
        thoughts_adapter.error(f"Tool Execution: Tool '{tool_name}' execution not fully implemented after parameter extraction.")
        return {
            "final_response": f"Error: Tool '{tool_name}' execution not fully implemented after parameter extraction.",
            "determined_kb_or_tool": tool_name,
            "generated_sql": None,
            "final_reaction": "x"
        }

    def _get_conversation_state(self, conversation_id: str) -> dict:
        """
        Retrieves or initializes the state for a given conversation_id.
        """
        if conversation_id not in self.conversation_states:
            self.logger.info(f"Initializing new conversation state for ID: {conversation_id}")
            self.conversation_states[conversation_id] = {
                "history": [],
                "pending_table_confirmation": False,
                "data_for_pending_table_str": None,
                "original_query_for_pending_table": None,
                "pending_customer_db_clarification": False, # New state for customer_db
                "original_query_for_customer_db": None,     # New state
                "clarified_year_month_for_customer_db": None, # New state (e.g., {"year": "2023", "month": "Apr"})
                "pending_reformulation_confirmation": False, # For query reformulation confirmation
                "original_query_for_reformulation_confirm": None, # Stores the original query
                "reformulated_query_for_confirm": None, # Stores the reformulated query
                "title_for_pending_table": None,
                "last_retrieved_structured_data_for_tool_follow_up": None
            }
        return self.conversation_states[conversation_id]

    def _initialize_email_to_group_role_map(self):
        """
        Pre-processes user_groups to create a direct email -> group_role map.
        If a user is in multiple groups, the role from the first group encountered (iteration order)
        that has roles defined will be used.
        """
        self.logger.debug("Initializing email to group role map.")
        for group_name, group_data in self.user_groups.items():
            group_roles = group_data.get("roles", [])
            if group_roles:
                # Use the first role defined for the group
                role_to_assign = group_roles[0]
                for user_email in group_data.get("users", []):
                    if user_email not in self._email_to_group_role_map:
                        # Only assign if this email hasn't been mapped yet
                        # This ensures the first group (in iteration order) a user belongs to determines their role
                        self._email_to_group_role_map[user_email] = role_to_assign
                        self.logger.debug(f"Mapped email '{user_email}' to role '{role_to_assign}' from group '{group_name}'.")
                    else:
                        self.logger.debug(f"Email '{user_email}' already mapped to role '{self._email_to_group_role_map[user_email]}'. Skipping assignment from group '{group_name}'.")
        self.logger.info(f"Email to group role map initialized with {len(self._email_to_group_role_map)} entries.")

    # Added thoughts_adapter parameter
    def _determine_interaction_state(self, query: str, conversation_history: list, thoughts_adapter: 'logging.LoggerAdapter') -> str:
        """
        Determines if the current query needs full routing or can be handled by direct LLM brainstorming.
        """
        thoughts_adapter.info(f"State Determination: Starting for query: '{query}'")
        query_lower_stripped = query.strip().lower()

        if not self.llm_for_details: # Should not happen if process_query checks first
            thoughts_adapter.error("State Determination: LLM for details not available. Defaulting to NEEDS_ROUTING.")
            return "NEEDS_ROUTING"

        # Check for simple acknowledgements first
        simple_acks = ["thank you", "thanks", "okay", "ok", "got it", "understood", "sounds good", "great", "perfect", "cool"]
        if query_lower_stripped in simple_acks or (len(query_lower_stripped.split()) <= 2 and any(ack in query_lower_stripped for ack in simple_acks)):
            thoughts_adapter.info(f"State Determination: Query '{query}' identified as a simple acknowledgement. Setting state to CAN_BRAINSTORM.")
            return "CAN_BRAINSTORM"

        history_context = _format_history_for_prompt(conversation_history)
        prompt = f"""{history_context}Current User Query (potentially reformulated for context): "{query}"

Based on the "Conversation History" and the "Current User Query", assess the nature of the query to decide if it requires full routing for data retrieval/tool execution, or if it can be handled through a more direct conversational approach (potentially with a light, targeted data lookup for enhancement).

A) NEEDS_ROUTING:
   The query's primary goal is to:
   1. Execute a specific tool (e.g., generate a chart, create a report, send a message, export data).
      - Example: "Show me sales data as a bar chart."
      - Example: "Create a report on user engagement for last month."
   2. Retrieve *new* factual information or structured data from a knowledge base that is not already present in the immediate conversation.
      - Example: "What are the latest HR policies on vacation?"
      - Example: "List all transactions for customer ID 123."
      - Example: "Search the web for recent news on AI advancements."
   3. Answer a follow-up question that explicitly requires fetching *new data points* or a different slice of data related to a previous topic.
      - Example (after discussing Product A sales): "What were the sales figures for Product B in the same period?" (Requires new data for Product B)
      - Example (after seeing total revenue): "Can you break that revenue down by product category?" (Requires new granular data)
   4. Address a complex question requiring synthesis of information from multiple distinct sources or a structured problem-solving approach that goes beyond a simple conversational exchange.
   5. The query introduces a *new topic or subject matter* that is distinct from the immediately preceding conversation turns, even if phrased conversationally.
      - Example (after discussing Q1 sales): "Okay, cool. Now, can you tell me about our company's remote work policy?" (New topic: remote work policy, should be NEEDS_ROUTING)

B) CAN_BRAINSTORM:
   The query is primarily:
   1. A simple greeting (e.g., "hello", "hi"), an acknowledgement (e.g., "thank you", "okay"), or general chit-chat.
   2. A question about your (the AI assistant's) identity, capabilities, or purpose.
      - Example: "Who are you?", "What can you help me with?"
   3. A follow-up question asking for *clarification, explanation, elaboration, or rephrasing* of information the assistant *has just provided* or that is clearly available in the recent conversation history.
      - Example (after assistant states a policy *on the current topic*): "Can you explain what 'section 3.2' of that policy means?"
      - Example (after assistant gives a complex answer): "Could you put that in simpler terms?"
      - Example (after assistant lists items): "Tell me more about the first item you mentioned."
   4. A follow-up that can be answered using logical reasoning based *only* on the existing conversation context or very general knowledge, without needing to fetch new data or use a tool.
      - Example (after discussing a feature): "So, does that mean it's available to all users?" (If context implies the answer)
   5. A request for a general discussion or brainstorming about a topic where the user isn't asking for specific, hard data points but rather for ideas, opinions (within AI's ethical bounds), or a general exploration of a concept based on provided context.
      - Example: "What are some potential benefits of implementing X, based on what we've discussed?"

If the query involves asking about the *user's own personal information* that the AI can provide (e.g., "What's my email?", "What's my role?", "What can I access?"), this should be handled by CAN_BRAINSTORM (specifically, rule 3 of the _construct_brainstorm_prompt will trigger a 'user_self_info_request'). If it's PII the AI *cannot* access (e.g. "Where do I live?"), rule 4 of _construct_brainstorm_prompt will handle it.

Respond with ONLY "NEEDS_ROUTING" for A-type queries, or "CAN_BRAINSTORM" for B-type queries.
If unsure, lean towards "NEEDS_ROUTING".
State:"""
        thoughts_adapter.debug(f"State Determination: LLM prompt:\n{prompt}")
        response_text = self.llm_for_details.generate(prompt).strip()
        thoughts_adapter.info(f"State Determination: LLM raw response: '{response_text}'")

        if response_text == "CAN_BRAINSTORM":
            thoughts_adapter.info("State Determination: Determined state: CAN_BRAINSTORM")
            return "CAN_BRAINSTORM"
        elif response_text == "NEEDS_ROUTING":
            thoughts_adapter.info("State Determination: Determined state: NEEDS_ROUTING")
            return "NEEDS_ROUTING"
        else:
            thoughts_adapter.warning(f"State Determination: LLM returned an unexpected response ('{response_text}'). Defaulting to NEEDS_ROUTING.") # type: ignore
            return "NEEDS_ROUTING" # Default to routing if unsure or malformed response

    def _construct_brainstorm_prompt(self, query: str, conversation_history: list, accessible_kbs: list[str], thoughts_adapter: 'logging.LoggerAdapter') -> str:
        """
        Constructs a prompt for the LLM to generate a conversational response based on history and current query.
        Allows the LLM to optionally request a lookup from accessible KBs.
        """
        history_context = _format_history_for_prompt(conversation_history)
        simple_greetings = ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"]
        is_simple_greeting = query.strip().lower() in simple_greetings
        
        # Combined simple greetings and acknowledgements for Rule 1
        simple_greetings_acks = ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening", "thank you", "thanks", "okay", "ok", "got it", "understood", "sounds good", "great", "perfect", "cool"]
        is_simple_greeting_ack = query.strip().lower() in simple_greetings_acks or (len(query.strip().lower().split()) <= 2 and any(ack in query.strip().lower() for ack in simple_greetings_acks))

        accessible_kbs_str = ", ".join(accessible_kbs) if accessible_kbs else "None"

        persona_description = "I am *Zia*, your Flutterwave AI assistant! :wave:\nI'm designed to enable Flutterwave employees to easily access key information and analysis.\nMy goal is to provide accurate and helpful responses to your queries. I am constantly learning and improving!"

        prompt = f"""{history_context}Current User Query: "{query}"
Accessible Knowledge Bases for potential context: [{accessible_kbs_str}]

You are Zia, a helpful and conversational AI assistant.
{GENERAL_OUTPUT_GUIDELINES}
Ensure your `content` field in the JSON response uses this Slack mrkdwn formatting where appropriate to enhance readability.

Overall Focus: Your primary purpose is to assist with Flutterwave-related queries and tasks.
If a query is clearly and significantly off-topic from Flutterwave, its business, products, technology, or general fintech topics relevant to Flutterwave,
politely state your focus. Example: {{"type": "direct_answer", "content": "I'm focused on assisting with Flutterwave-related topics. How can I help you with that?"}}

Instructions:
1.  If the query is a simple greeting (e.g., "hello", "hi"), acknowledgement (e.g., "thank you", "okay", "got it"), or very brief general chit-chat:
    Provide a brief, polite, and contextually appropriate response. Avoid reintroducing unrelated past topics from the conversation history unless the user's brief query *explicitly* references it.
    Examples: If user says "Thank you", respond with "You're welcome!". If user says "Okay", respond with "Great!" or "Sounds good."
    Output JSON: {{"type": "direct_answer", "content": "Your brief, polite reply here."}}


2.  If the query is about *your* identity (the AI assistant, Zia, e.g., "who are you?", "what can you do?"):
    a.  If the query also clearly indicates the introduction is for a wider audience like "the channel", "everyone", "the team", or "us all" (e.g., "introduce yourself to the channel"):
        Output JSON: {{"type": "broadcast_self_introduction", "content": "{persona_description}"}}
    b.  Otherwise (it's a direct question to you about yourself, or the audience is not specified as broad):
        Output JSON: {{"type": "direct_answer", "content": "{persona_description}"}}


3.  If the query is about the *user's identity* or *their access permissions* that you *can* provide information about (e.g., "who am I?", "what's my email?", "what is my role?", "tell me about my access", "what information can I access?", "which information do I have access to?", "what tools can I use?", "what tools do I have access to?"):
    Indicate that user self-information is being requested. The agent will then construct the actual response. Output JSON:
    {{"type": "user_self_info_request"}}

4.  If the query is asking about other *user's personal information* that you (the AI assistant) *cannot and should not access or disclose* (e.g., "where do I live?", "what is my phone number?", "what is my mother's maiden name?"):
    Respond politely that you don't have access to their personal information. Output JSON:
    {{"type": "direct_answer", "content": "I'm an AI assistant, so I don't have access to that kind of personal information."}}

5.  If the "Current User Query" is a vague follow-up (e.g., "tell me more about them/that", "what about X?", "details?", "what can you tell me about them?") to information or a list of items the Assistant *just provided in the immediately preceding turn* in the "Conversation History":
    Acknowledge you provided the information/list. Explain that to provide more details, you need the user to be more specific about what they want to know about "them/that/X".
    Do NOT attempt a new KB lookup for this type of vague follow-up.
    Output JSON:
    {{"type": "direct_answer", "content": "I just mentioned [brief reference to previous info, e.g., 'those clients', 'that policy']. What specifically would you like to know more about them/it? For example, are you interested in [suggest 1-2 types of specific details they might ask for, like 'their industry or recent projects']?"}}
6.  If the query is significantly off-topic (see "Overall Focus" above) and not covered by other rules:
    Output JSON: {{"type": "direct_answer", "content": "My expertise is in Flutterwave-related topics. How can I assist you with something in that area?"}}

7.  For other queries (not covered by rules 1-6), first try to answer directly using ONLY the "Conversation History" and the "Current User Query".
    If you can provide a complete and helpful answer this way, output JSON:
    {{"type": "direct_answer", "content": "Your answer based on history/query."}}
8.  If you believe a brief lookup in ONE of the "Accessible Knowledge Bases" would significantly improve your answer for a query (that is not covered by rules 1-6) or help clarify something for the user conversationally:
    - Identify the MOST relevant knowledge base from the list: [{accessible_kbs_str}].
    - Formulate a concise "query_for_kb" to retrieve the specific piece of information. This query should be a natural language question.
    - Output JSON:
      {{"type": "needs_kb_lookup", "kb_name": "chosen_kb_name", "query_for_kb": "your_specific_question_for_the_kb"}}
    - Example: If user asks "What was that policy about remote work again?" and 'company_policies_kb' is accessible, you might output:
      {{"type": "needs_kb_lookup", "kb_name": "company_policies_kb", "query_for_kb": "Retrieve summary of remote work policy."}}
    
9.  If you cannot answer directly (using only history/query) for a query (that is not covered by rules 1-6) AND a KB lookup doesn't seem appropriate or helpful for a conversational response in this mode (and it's not off-topic):
    Output JSON for a polite punt:
    {{"type": "direct_answer", "content": "I'm not sure how to help with that in our current conversation. Could you try rephrasing or asking something more specific?"}}

10. If, after attempting to answer directly (rule 7) or identify a KB lookup (rule 8), the "Current User Query" is still too vague or lacks specific details needed for a meaningful response or a targeted KB lookup (and it's not off-topic):
    Formulate a concise "clarification_question" to ask the user for the missing information.
    Output JSON:
    {{"type": "needs_clarification", "clarification_question": "Your specific question to the user to get the missing detail."}}
    Example: If user asks "Tell me about our projects", you might output:
    {{"type": "needs_clarification", "clarification_question": "Sure, I can tell you about our projects. Are you interested in specific projects, their current status, budget, or something else?"}}

Constraints:
-   Only request a KB lookup if it's for a specific piece of information to enhance a conversational answer. Do NOT use it for complex data analysis or tasks requiring tools.
-   If requesting a lookup, choose only ONE KB from the "Accessible Knowledge Bases" list. If "None" are accessible, do not attempt a lookup.
-   Your "query_for_kb" should be a natural language question.
-   Your "clarification_question" should be polite and specific.
-   Do NOT make up information.
Respond ONLY with the JSON object based on the instructions above.
"""
        thoughts_adapter.debug(f"Enhanced Brainstorm Prompt Construction: Prompt ready for LLM.")
        return prompt

    def _get_role_for_email(self, email: str, thoughts_adapter: 'logging.LoggerAdapter') -> str:
        """
        Determines the role for a user based on their email using pre-processed maps.
        """
        thoughts_adapter.info(f"Resolving role for email: '{email}'")

        # Priority 1: Email directly listed in access_controls (acting as a role name)
        if email in self.access_controls:
            thoughts_adapter.info(f"Email '{email}' found directly in access_controls. Using email as role name.")
            return email

        # Priority 2: Email mapped to a group role via pre-computed map
        assigned_role = self._email_to_group_role_map.get(email)
        if assigned_role:
            thoughts_adapter.info(f"Email '{email}' found in pre-computed group map. Assigning role: '{assigned_role}'.")
            return assigned_role

        thoughts_adapter.warning(f"No specific role or group role found for email '{email}'. Assigning default role: '{DEFAULT_USER_ROLE}'.")
        return DEFAULT_USER_ROLE


    # Added thoughts_adapter parameter
    def _reformulate_query_with_history(self, query: str, conversation_history: list, thoughts_adapter: 'logging.LoggerAdapter') -> str:
        """
        Reformulates the user's query using the conversation history to make it standalone.
        """
        thoughts_adapter.info(f"Query Reformulation: Starting for query: '{query}'")
        if not self.llm_for_details:
            thoughts_adapter.error("Query Reformulation: LLM for details not available. Returning original query.")
            return query

        thoughts_adapter.debug(f"Query Reformulation: Conversation history (last {len(conversation_history)} turns) provided.")

        if not conversation_history:
            thoughts_adapter.debug("Query Reformulation: No conversation history, using query as is.")
            return query

        # Heuristic: Only reformulate if query is short, or contains specific keywords/pronouns
        # indicating strong dependence on history.
        query_lower = query.lower()
        needs_reformulation_due_to_content = False
        
        pronouns_needing_context = ["it", "that", "those", "they", "them", "he", "she", "him", "her", "this", "these"]
        if any(f" {pronoun} " in query_lower or query_lower.startswith(pronoun + " ") or query_lower.endswith(" " + pronoun) for pronoun in pronouns_needing_context):
            needs_reformulation_due_to_content = True
            thoughts_adapter.debug("Query Reformulation: Query contains context-dependent pronouns.")

        if not needs_reformulation_due_to_content:
            follow_up_starters = ["what about", "how about", "and for", "and the", "and what if", "show that as", "tell me more about that"]
            if any(query_lower.startswith(starter) for starter in follow_up_starters):
                needs_reformulation_due_to_content = True
                thoughts_adapter.debug("Query Reformulation: Query starts with a follow-up phrase.")

        MIN_WORDS_FOR_POTENTIAL_SKIP = 7 
        if len(query.split()) >= MIN_WORDS_FOR_POTENTIAL_SKIP and not needs_reformulation_due_to_content:
            thoughts_adapter.info(f"Query Reformulation: Skipped LLM call based on heuristic (length >= {MIN_WORDS_FOR_POTENTIAL_SKIP} words and no strong context cues). Query: '{query}'")
            return query
        
        thoughts_adapter.info(f"Query Reformulation: Proceeding with LLM call for query: '{query}' (is_short: {len(query.split()) < MIN_WORDS_FOR_POTENTIAL_SKIP}, needs_reformulation_due_to_content: {needs_reformulation_due_to_content})")

        history_context = _format_history_for_prompt(conversation_history)

        prompt = f"""{history_context}Current user query: "{query}"
Considering the conversation history, rephrase the "Current user query" to be a standalone, complete question that incorporates relevant context from the history.
- Resolve pronouns (e.g., "it", "that", "they") and ambiguous references.
- If the current query is a follow-up (e.g., "What about for last quarter?", "Show that as a pie chart"), expand it into a full question based on the previous turn.
- If the current query is already clear and self-contained, you can return it as is or with minimal clarification.
- Crucially, if the 'Current user query' introduces a *completely new topic* that has no clear semantic link to the 'Previous conversation', the reformulated query should primarily be the 'Current user query' itself, perhaps with minor clarifications for standalone understanding, but *without forcing an artificial connection* to the unrelated history.
- The goal is to make the query understandable without needing to read the prior conversation.
Example 1 (Follow-up):
User: What were the total sales for product X in Q1?
Assistant: Total sales for product X in Q1 were $50,000.
Current user query: "What about for Q2?"
Reformulated query: "What were the total sales for product X in Q2?"
Example 2 (Pronoun/Reference):
User: List my top 5 customers by revenue.
Assistant: Here are your top 5 customers: [list].
Current user query: "Can you show that as a bar chart?"
Reformulated query: "Can you show my top 5 customers by revenue as a bar chart?"
Example 3 (Vague follow-up referring to assistant's last statement):
User: Can you tell me about X?
Assistant: X is a complex topic involving A, B, and C. [details about A, B, C]
Current user query: "Hmm, what do you think about this?" or "Tell me more about that."
Reformulated query: "What are your thoughts on X which involves A, B, and C (as you just described)?" or "Can you elaborate on X (which you just described as involving A, B, and C)?"
Example 4 (New, unrelated topic):
User: ...and that's the sales summary for Q1.
Assistant: Got it. Sales for Q1 were $X.
Current user query: "What is our company's policy on remote work?"
Reformulated query: "What is our company's policy on remote work?"
Example 5 (Incorporating user's answer to a clarification question):
User: Tell me about our projects.
Assistant: Sure, which specific projects are you interested in, or what kind of information are you looking for (e.g., status, budget)?
Current user query: "The ones in the Alpha category, and I want to know their current status."
Reformulated query: "What is the current status of projects in the Alpha category?"

Respond ONLY with the reformulated query.
Reformulated query:"""

        thoughts_adapter.debug(f"Query Reformulation: LLM prompt:\n{prompt}")
        response_text = self.llm_for_details.generate(prompt)
        thoughts_adapter.info(f"Query Reformulation: LLM response: '{response_text}'")

        if response_text.startswith("Error:"):
            thoughts_adapter.warning(f"Query Reformulation: LLM generation returned an error. Original query: '{query}'. LLM response: '{response_text}'. Using original query.")
            return query
        elif not response_text: # Explicitly handle empty string
            thoughts_adapter.warning(f"Query Reformulation: LLM returned an empty response. Original query: '{query}'. Using original query.")
            return query
        else: # Success
            thoughts_adapter.info(f"Query Reformulation: Successfully reformulated query from '{query}' to '{response_text}'.")
            return response_text

    # Helper function to check original query ambiguity
    def _check_original_query_ambiguity(self, original_query: str, conversation_history: list, thoughts_adapter: logging.LoggerAdapter) -> str:
        thoughts_adapter.info(f"Checking ambiguity of original query: '{original_query}'")
        if not self.llm_for_details:
            thoughts_adapter.error("Ambiguity Check: LLM for details not available. Assuming PRECISE.")
            return "PRECISE"

        history_context = _format_history_for_prompt(conversation_history)
        prompt = f"""{history_context}Original User Query: "{original_query}"

Analyze the "Original User Query" in the context of the "Previous conversation" (if any).
Determine if the "Original User Query" is AMBIGUOUS or PRECISE.

- AMBIGUOUS means the query is unclear, vague, could have multiple interpretations, or heavily relies on unstated context that makes its intent uncertain without further clarification or reformulation.
  Examples of AMBIGUOUS queries:
    - "Tell me about it." (What is "it"?)
    - "What's the status?" (Status of what?)
    - "More details." (Details about what?)
    - "Fix this." (What needs fixing and how?)
    - "The usual." (What is "the usual"?)

- PRECISE means the query has a clear, specific, and actionable intent, even if it's short. It might still be a follow-up, but its meaning is clear.
  Examples of PRECISE queries:
    - "What is the capital of France?"
    - "Show me my top 5 clients by revenue."
    - "What about for Q2?" (Assuming previous turn discussed Q1 sales for a specific product)
    - "Yes" (In response to a confirmation question)
    - "Generate a report on that." (If "that" was clearly defined in the assistant's previous turn)

Respond with ONLY "AMBIGUOUS" or "PRECISE".
Assessment:"""
        thoughts_adapter.debug(f"Ambiguity Check: LLM prompt:\n{prompt}")
        response_text = self.llm_for_details.generate(prompt).strip()
        thoughts_adapter.info(f"Ambiguity Check: LLM response: '{response_text}'")

        if response_text in ["AMBIGUOUS", "PRECISE"]:
            return response_text
        else:
            thoughts_adapter.warning(f"Ambiguity Check: LLM returned unexpected response ('{response_text}'). Defaulting to PRECISE.")
            return "PRECISE"

    async def process_query_and_manage_history(self, query: str, user_identifier: str, interaction_id: str, conversation_id: str, channel_id: Optional[str] = None, message_ts: Optional[str] = None) -> Tuple[Dict[str, Any], list]:
        """
        Processes the user's query, manages conversation history, and determines access based on user_identifier (email).
        user_identifier: The email address of the user.
        conversation_id: The unique identifier for the current conversation (e.g., user_id for DMs, channel_id for channels).
        """
        original_user_query = query # Preserve the original query
        # Create a LoggerAdapter for this specific interaction
        thoughts_adapter = logging.LoggerAdapter(agent_thoughts_logger, {'interaction_id': interaction_id})
        thoughts_adapter.info(f"Processing query. User Identifier (Email): '{user_identifier}', Conversation ID: '{conversation_id}', Original Query: '{query}'")
        
        # Initial "acknowledged" reaction is added by slack_setup.py
        # await self._add_reaction_safely("white_check_mark", channel_id, message_ts, thoughts_adapter)

        current_conversation_state = self._get_conversation_state(conversation_id)
        conversation_history = current_conversation_state["history"] # Use history from the specific conversation state

        # Snapshot of history *before* this turn for logging context
        history_snapshot_for_logging = list(conversation_history)
        thoughts_adapter.debug(f"Current conversation history (for context, {len(history_snapshot_for_logging)} turns): {history_snapshot_for_logging}")

        # --- Handle pending reformulation confirmation ---
        if current_conversation_state["pending_reformulation_confirmation"]:
            thoughts_adapter.info(f"Pending reformulation confirmation is TRUE. User response: '{original_user_query}'")
            affirmative_responses = ["yes", "yeah", "yep", "sure", "okay", "ok", "go ahead", "please", "do it", "correct", "that's right"]
            if original_user_query.strip().lower() in affirmative_responses:
                thoughts_adapter.info("User confirmed reformulated query.")
                query = current_conversation_state["reformulated_query_for_confirm"] # Use the confirmed reformulated query
                agent_outcome_dict = {"final_response": f"Okay, proceeding with: \"{query}\"", 
                                      "determined_kb_or_tool": "reformulation_confirmed_proceeding",
                                      "final_reaction": "thumbsup"} 
                # This response is temporary; the agent will now process the confirmed query.
                # We need to allow the flow to continue to process 'query'.
            else: # User did not confirm (or asked something else)
                thoughts_adapter.info("User did not confirm reformulated query or asked something else.")
                agent_outcome_dict = {
                    "final_response": "Okay, I won't use that interpretation. Could you please rephrase your original request?",
                    "determined_kb_or_tool": "reformulation_declined_rephrase",
                    "final_reaction": "thinking_face"
                }
                # Reset pending state and return this outcome
                current_conversation_state["pending_reformulation_confirmation"] = False
                current_conversation_state["original_query_for_reformulation_confirm"] = None
                current_conversation_state["reformulated_query_for_confirm"] = None
                # Update history and return
                final_response_for_history_pending = agent_outcome_dict.get("final_response", "Error: No response generated.")
                current_conversation_state["history"].append({"role": "user", "content": original_user_query})
                current_conversation_state["history"].append({"role": "assistant", "content": final_response_for_history_pending})
                # Trim history if needed
                if len(current_conversation_state["history"]) > self.MAX_HISTORY_TURNS * 2:
                    current_conversation_state["history"] = current_conversation_state["history"][-(self.MAX_HISTORY_TURNS * 2):]
                return agent_outcome_dict, history_snapshot_for_logging
            # Reset pending state after handling
            current_conversation_state["pending_reformulation_confirmation"] = False
            current_conversation_state["original_query_for_reformulation_confirm"] = None
            current_conversation_state["reformulated_query_for_confirm"] = None

        # --- Handle pending table confirmation ---
        if current_conversation_state["pending_table_confirmation"]:
            thoughts_adapter.info(f"Pending table confirmation is TRUE. User query: '{query}'")
            affirmative_responses = ["yes", "yeah", "yep", "sure", "okay", "ok", "go ahead", "please", "do it", "show me", "display it", "yes please"]
            if query.strip().lower() in affirmative_responses:
                thoughts_adapter.info("User confirmed table view.")
                if current_conversation_state["data_for_pending_table_str"] and self.llm_for_details:
                    table_title = current_conversation_state["title_for_pending_table"] if current_conversation_state["title_for_pending_table"] else f"Data for: {current_conversation_state['original_query_for_pending_table']}"
                    csv_generation_result = generate_csv_file_from_json(
                        data_json_str=current_conversation_state["data_for_pending_table_str"],
                        title=table_title,
                        thoughts_adapter=thoughts_adapter
                    )
                    agent_outcome_dict = {
                        "final_response": csv_generation_result["message"],
                        "determined_kb_or_tool": "table_generator_from_confirmation_csv",
                    "generated_sql": None, # SQL was already logged if applicable from the initial data fetch
                    "final_reaction": "page_facing_up" if csv_generation_result.get("filename") else "warning"
                    }
                elif not self.llm_for_details:
                    thoughts_adapter.error("LLM for details not available for table generation during confirmation.")
                    agent_outcome_dict = {"final_response": "Error: Cannot generate table due to an internal issue.", "determined_kb_or_tool": "table_generator_error_no_llm"}
                else: # Data was missing
                    thoughts_adapter.warning("User confirmed table view, but no data was stored for pending table.")
                    agent_outcome_dict = {"final_response": "I was ready to show you a table, but I seem to have misplaced the data. Could you ask your original question again?", "determined_kb_or_tool": "pending_table_data_missing"}
            else: # User did not confirm (or asked something else, which will be processed as a new query if logic falls through)
                thoughts_adapter.info("User did not confirm table view or asked something else. Resetting pending state and processing new query.")
                agent_outcome_dict = {"final_response": "Okay, I won't display that as a table. How else can I help you?", "determined_kb_or_tool": "table_confirmation_declined"}
                # Reset pending state here. If 'query' is more than just "no", it will be processed.
                if query.strip().lower() not in ["no", "nope", "don't", "cancel"]:
                    # If it's a new query, we need to fall through to process it.
                    # For now, let's assume a simple "no" means we just give the above response.
                    # A more complex handling would re-process 'query' if it's a new question.
                    pass # For now, if not affirmative, we give the "Okay, I won't display..." message.
            # Reset pending state for this conversation
            current_conversation_state["pending_table_confirmation"] = False
            current_conversation_state["data_for_pending_table_str"] = None
            current_conversation_state["original_query_for_pending_table"] = None
            current_conversation_state["title_for_pending_table"] = None
            # This path should now go to the history management and return block at the end of the function.            
        
        # --- Handle pending customer_db year/month clarification ---
        if current_conversation_state["pending_customer_db_clarification"]:
            thoughts_adapter.info(f"Pending customer_db year/month clarification. User query: '{query}'")
            # Attempt to parse year and month from the user's current query
            # This is a simple heuristic; a more robust parser or LLM call might be needed.
            parsed_year, parsed_month = None, None
            year_match = re.search(r'\b(20\d{2})\b', query) # Matches 20xx
            if year_match:
                parsed_year = year_match.group(1)
            
            # Simple month name matching (case-insensitive)
            month_map = {name.lower(): name for name in ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]}
            for m_lower, m_actual in month_map.items():
                if m_lower in query.lower():
                    parsed_month = m_actual
                    break
            
            if parsed_year and parsed_month:
                thoughts_adapter.info(f"Successfully parsed year '{parsed_year}' and month '{parsed_month}' from user clarification.")
                current_conversation_state["clarified_year_month_for_customer_db"] = {"year": parsed_year, "month": parsed_month}
                original_customer_db_query = current_conversation_state["original_query_for_customer_db"]
                current_conversation_state["pending_customer_db_clarification"] = False # Reset state
                current_conversation_state["original_query_for_customer_db"] = None
                
                # Re-process the original query for customer_db, now with year/month
                # This will effectively re-route, but this time query_knowledge_base will get year/month
                # We need to ensure the original query is used, not the clarification text.
                query = original_customer_db_query # Restore original query for processing
                thoughts_adapter.info(f"Proceeding with original customer_db query '{query}' using clarified year/month.")
                # Fall through to normal processing with the original query and now-set year/month.
            else:
                thoughts_adapter.warning(f"Could not parse year/month from clarification: '{query}'. Re-prompting.")
                agent_outcome_dict = {
                    "final_response": "I couldn't quite understand the year and month. Please provide them clearly, for example: '2023 Apr' or 'for September 2024'.",
                    "determined_kb_or_tool": "customer_db_clarification_reprompt",
                    "generated_sql": None,
                    "final_reaction": "question"
                }
                # Keep pending_customer_db_clarification = True
                # Update history and return (similar to table confirmation)
                # This path should now go to the history management and return block at the end of the function.



        if not self.llm_for_details:
            self.logger.error("LLM for details not initialized. Cannot process query.")
            agent_outcome_dict = {
                "final_response": "Error: Agent's internal LLM is not available.",
                "determined_kb_or_tool": "internal_error",
                "generated_sql": None,
                "final_reaction": "warning"
            }
            thoughts_adapter.critical("LLM for details not initialized. Cannot process query.")
            # Ensure history is updated and the correct tuple is returned even in this error case
            final_response_for_history_error = agent_outcome_dict.get("final_response", "Error: No response generated by agent.")
            current_conversation_state["history"].append({"role": "user", "content": original_user_query})
            current_conversation_state["history"].append({"role": "assistant", "content": final_response_for_history_error})
            if len(current_conversation_state["history"]) > self.MAX_HISTORY_TURNS * 2:
                current_conversation_state["history"] = current_conversation_state["history"][-(self.MAX_HISTORY_TURNS * 2):]
                thoughts_adapter.debug(f"Conversation history for {conversation_id} trimmed to last {self.MAX_HISTORY_TURNS} turns.")
                return agent_outcome_dict, history_snapshot_for_logging

        # If agent_outcome_dict was set by a pending state handler above that results in an immediate return
        if 'agent_outcome_dict' in locals() and (current_conversation_state["pending_reformulation_confirmation"] or current_conversation_state["pending_table_confirmation"] or current_conversation_state["pending_customer_db_clarification"]):
            return agent_outcome_dict, history_snapshot_for_logging

        actual_user_role = self._get_role_for_email(user_identifier, thoughts_adapter)
        thoughts_adapter.info(f"Resolved email '{user_identifier}' to role: '{actual_user_role}'")

        query_to_process = query # This is the original query or the one confirmed from a previous reformulation

        interaction_state = self._determine_interaction_state(query_to_process, conversation_history, thoughts_adapter)
        thoughts_adapter.info(f"Determined interaction state: '{interaction_state}' for query: '{query_to_process}'")        

        if interaction_state == "CAN_BRAINSTORM":
            thoughts_adapter.info(f"Determined interaction state: '{interaction_state}' for query: '{query_to_process}'")

            thoughts_adapter.info("State is CAN_BRAINSTORM. Attempting enhanced brainstorming.")

            user_permissions = self.access_controls.get(actual_user_role, set())
            accessible_kbs_for_user = [kb for kb in self.router.knowledge_banks if kb in user_permissions]
            thoughts_adapter.debug(f"User '{actual_user_role}' has access to KBs for brainstorming: {accessible_kbs_for_user}")

            brainstorm_prompt = self._construct_brainstorm_prompt(
                query_to_process, # Use the query_to_process
                conversation_history,
                accessible_kbs_for_user,
                thoughts_adapter
            )
            llm_brainstorm_response_str = self.llm_for_details.generate(brainstorm_prompt)
            thoughts_adapter.info(f"CAN_BRAINSTORM: Initial LLM response string (first 300 chars): {llm_brainstorm_response_str[:300]}")

            parsed_brainstorm_action = parse_llm_json_output(llm_brainstorm_response_str, thoughts_adapter)

            if parsed_brainstorm_action and "type" in parsed_brainstorm_action:
                action_type = parsed_brainstorm_action["type"]
                if action_type == "direct_answer":
                    final_response_from_brainstorm = parsed_brainstorm_action.get("content", "I'm ready for your next question. What can I do for you?")
                    thoughts_adapter.info(f"CAN_BRAINSTORM: Type 'direct_answer'. Response: {final_response_from_brainstorm}")
                    agent_outcome_dict = {
                        "final_response": final_response_from_brainstorm,
                        "determined_kb_or_tool": "conversational_llm_direct",
                        "generated_sql": None,
                        "final_reaction": "thought_bubble" 
                    }
                elif action_type == "broadcast_self_introduction":
                    persona_description_for_broadcast = self._construct_brainstorm_prompt("", [], [], thoughts_adapter).split('persona_description = "')[1].split('"')[0] 
                    final_response_from_brainstorm = parsed_brainstorm_action.get("content", persona_description_for_broadcast)
                    thoughts_adapter.info(f"CAN_BRAINSTORM: Type 'broadcast_self_introduction'. Response: {final_response_from_brainstorm}")
                    agent_outcome_dict = {
                        "final_response": final_response_from_brainstorm,
                        "determined_kb_or_tool": "conversational_llm_broadcast_self_intro",
                        "generated_sql": None,
                        "final_reaction": "wave", 
                        "skip_final_user_tagging": True 
                    }
                elif action_type == "user_self_info_request":
                    thoughts_adapter.info("CAN_BRAINSTORM: Type 'user_self_info_request'. Constructing user info response.")
                    response_text = f"You are interacting as '{user_identifier}' and your resolved role is '{actual_user_role}'.\n"
                    agent_outcome_dict = {
                        "final_response": response_text,
                        "determined_kb_or_tool": "conversational_llm_user_self_info",
                        "generated_sql": None,
                        "final_reaction": "id"
                    }
                elif action_type == "needs_kb_lookup":
                    kb_name_requested = parsed_brainstorm_action.get("kb_name")
                    query_for_kb = parsed_brainstorm_action.get("query_for_kb")

                    if kb_name_requested and query_for_kb and kb_name_requested in accessible_kbs_for_user:
                        thoughts_adapter.info(f"CAN_BRAINSTORM: Type 'needs_kb_lookup'. KB: '{kb_name_requested}', Query: '{query_for_kb}'")
                        
                        await self._add_reaction_safely("mag", channel_id, message_ts, thoughts_adapter)

                        kb_result_dict = await query_knowledge_base(
                            query_for_kb,
                            kb_name_requested,
                            conversation_history,
                            self.llm_for_details, # This is the llm instance
                            thoughts_adapter,
                            agent_instance=self # Pass the agent instance itself
                        )
                        kb_info = kb_result_dict.get("final_response", "No specific information was found regarding that.")
                        thoughts_adapter.info(f"CAN_BRAINSTORM: Info from KB '{kb_name_requested}' (first 200 chars): {str(kb_info)[:200]}...")

                        synthesis_prompt = f"""{_format_history_for_prompt(conversation_history)}Original User Query: "{query_to_process}"
You previously determined you needed information from the '{kb_name_requested}' knowledge base to answer the query: "{query_to_process}"
Information retrieved from '{kb_name_requested}':
---
{kb_info}
---
Now, using this retrieved information and the conversation history, provide a comprehensive and conversational answer to the "Original User Query".
If the retrieved information is not helpful or insufficient, acknowledge that politely and answer as best as you can based on the original query and history.
Do NOT mention the internal step of fetching data unless it's natural to the conversation (e.g., "I looked up the policy, and it says...").
{GENERAL_OUTPUT_GUIDELINES}
Keep your response user-focused and directly address their original query.
Respond ONLY with your final user-facing answer.
User-facing response:"""
                        thoughts_adapter.debug(f"CAN_BRAINSTORM: Synthesis prompt (first 300 chars): {synthesis_prompt[:300]}")
                        final_synthesized_answer = self.llm_for_details.generate(synthesis_prompt)  # type: ignore

                        if final_synthesized_answer.startswith("Error:") or not final_synthesized_answer.strip():
                            thoughts_adapter.warning(
                                f"CAN_BRAINSTORM: Synthesis LLM failed or returned empty. Using KB info directly or a fallback. LLM response: '{final_synthesized_answer}'"
                            )
                            final_response_from_brainstorm = (
                                kb_info
                                if kb_info != "No specific information was found regarding that."
                                else "I looked that up but couldn't find the specific details just now. How else can I assist?"
                            )
                        else:
                            final_response_from_brainstorm = final_synthesized_answer

                        agent_outcome_dict = {
                            "final_response": final_response_from_brainstorm,
                            "determined_kb_or_tool": f"conversational_llm_with_kb_lookup ({kb_name_requested})",
                            "generated_sql": kb_result_dict.get("generated_sql"),
                            "final_reaction": "mag",  # Magnifying glass for lookup
                        }
                    thoughts_adapter.warning(
                        f"CAN_BRAINSTORM: 'needs_kb_lookup' but invalid params or KB ('{kb_name_requested}') not in accessible list ({accessible_kbs_for_user}). Query for KB: '{query_for_kb}'. Defaulting."
                    )
                    final_response_from_brainstorm = "I was about to look that up but ran into a small snag. Could you try asking in a different way, or perhaps I can help with something else?"
                    agent_outcome_dict = {
                        "final_response": final_response_from_brainstorm,
                        "determined_kb_or_tool": "conversational_llm_kb_lookup_failed_access_or_param",
                        "generated_sql": None,
                        "final_reaction": "warning",
                    }
                elif action_type == "needs_clarification":
                    clarification_question = parsed_brainstorm_action.get("clarification_question", "I need a bit more information to help with that. Could you please provide more details?")
                    thoughts_adapter.info(f"CAN_BRAINSTORM: Type 'needs_clarification'. Question: {clarification_question}")
                    agent_outcome_dict = {
                        "final_response": clarification_question,
                        "determined_kb_or_tool": "conversational_llm_needs_clarification",
                        "generated_sql": None,
                        "final_reaction": "thought_bubble" # Clarification is part of brainstorming
                    }
                else: 
                    thoughts_adapter.warning(f"CAN_BRAINSTORM: Parsed action type unknown or missing: '{action_type if 'action_type' in locals() else 'N/A'}'. Raw parsed: {parsed_brainstorm_action}. Defaulting.")
                    final_response_from_brainstorm = "I'm not quite sure how to respond to that. Could you try rephrasing?"
                    final_reaction_for_unknown = "shrug"
                    agent_outcome_dict = {
                        "final_response": final_response_from_brainstorm,
                        "determined_kb_or_tool": "conversational_llm_unknown_action_type",
                        "generated_sql": None, "final_reaction": final_reaction_for_unknown
                    }
            else: 
                thoughts_adapter.error(f"CAN_BRAINSTORM: Failed to parse LLM JSON output for brainstorm action. Raw: {llm_brainstorm_response_str[:300]}. Defaulting.")
                simple_greetings = ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"]
                if query_to_process.strip().lower() in simple_greetings: 
                    final_response_from_brainstorm = "Hello! How can I help you today?"
                else:
                    final_response_from_brainstorm = "I'm having a little trouble formulating a response right now. How about we try something else?"
                agent_outcome_dict = {
                    "final_response": final_response_from_brainstorm,
                    "determined_kb_or_tool": "conversational_llm_json_parse_failed",
                    "generated_sql": None,
                    "final_reaction": "warning"
                }
        else:  # Covers "NEEDS_ROUTING"
            if interaction_state != "NEEDS_ROUTING": # Log if it defaulted
                thoughts_adapter.warning(f"Interaction state was '{interaction_state}', defaulting to NEEDS_ROUTING for query: '{query_to_process}'")
            await self._add_reaction_safely("hourglass_flowing_sand", channel_id, message_ts, thoughts_adapter)
            
            thoughts_adapter.info(f"NEEDS_ROUTING: Query: '{query_to_process}'. Original query for ambiguity check: '{original_user_query}'")
            
            # Perform ambiguity check and reformulation only if NEEDS_ROUTING and not already a confirmed reformulation
            # The 'query' variable holds the original user input for this turn, or a confirmed reformulated query from a *previous* turn.
            # 'original_user_query' holds the raw input for *this current turn*.
            if not current_conversation_state["pending_reformulation_confirmation"]: # Only if not already in a reformulation cycle
                ambiguity_status = self._check_original_query_ambiguity(original_user_query, conversation_history, thoughts_adapter)
                reformulated_by_llm = self._reformulate_query_with_history(original_user_query, conversation_history, thoughts_adapter)
                
                if ambiguity_status == "AMBIGUOUS" and reformulated_by_llm.strip().lower() != original_user_query.strip().lower():
                    current_conversation_state["pending_reformulation_confirmation"] = True
                    current_conversation_state["original_query_for_reformulation_confirm"] = original_user_query
                    current_conversation_state["reformulated_query_for_confirm"] = reformulated_by_llm
                    agent_outcome_dict = {
                        "final_response": f"Just to be sure, did you mean: \"{reformulated_by_llm}\"? (yes/no)",
                        "determined_kb_or_tool": "reformulation_confirmation_request",
                        "final_reaction": "question"
                    }
                    # This outcome will be returned, and the next interaction will handle the user's yes/no.
                else: # Query was precise, or reformulation didn't change it, or it's already a confirmed query
                    query_to_process = reformulated_by_llm # Use the (potentially unchanged by this step) reformulated query
            # If it was a pending reformulation that got confirmed, query_to_process is already set earlier.

            # If agent_outcome_dict was set by the reformulation confirmation request, return it.
            if 'agent_outcome_dict' in locals() and agent_outcome_dict.get("determined_kb_or_tool") == "reformulation_confirmation_request":
                # History update and return will happen at the end of the function.
                pass
            else: # Proceed with routing
                thoughts_adapter.info(f"Proceeding with standard routing for query: '{query_to_process}'")
                destination_name = self.router.route(query_to_process, conversation_history, thoughts_adapter)
                thoughts_adapter.info(f"SemanticRouter selected destination: '{destination_name}' for query: '{query_to_process}' (Role: '{actual_user_role}')")
                
                if destination_name in self.router.tools:
                    await self._add_reaction_safely("gear", channel_id, message_ts, thoughts_adapter)
                elif destination_name in self.router.knowledge_banks and not (destination_name == "customer_db" and not current_conversation_state.get("clarified_year_month_for_customer_db")):
                    # Add "books" only if we are actually querying, not if we need clarification for customer_db
                    await self._add_reaction_safely("books", channel_id, message_ts, thoughts_adapter)
                elif destination_name == "default":
                    await self._add_reaction_safely("brain", channel_id, message_ts, thoughts_adapter)

                if destination_name != "default": # This check is after adding reaction based on destination type
                    access_result = determine_access(actual_user_role, destination_name)
                    thoughts_adapter.info(f"Access Check: Role='{actual_user_role}', Resource='{destination_name}', Status='{access_result['status']}', Reason='{access_result.get('reason', 'N/A')}'")
                    if access_result["status"] != "Access Granted":
                        reason = access_result.get('reason', f"Role '{actual_user_role}' lacks permission for '{destination_name}'.")
                        agent_outcome_dict = {
                            "final_response": f"Access Denied: {reason}",
                            "determined_kb_or_tool": destination_name,
                            "generated_sql": None,
                            "final_reaction": "no_entry_sign"
                        }
                    else: # Access granted, proceed with routing
                        if destination_name in self.router.tools:
                            agent_outcome_dict = await _handle_tool_destination(self, query_to_process, destination_name, actual_user_role, user_identifier, conversation_id, self.router, conversation_history, current_conversation_state["last_retrieved_structured_data_for_tool_follow_up"], self.slack_app.client if self.slack_app else None, self.llm_for_details, thoughts_adapter)
                        elif destination_name in self.router.knowledge_banks:
                            if destination_name == "customer_db" and not current_conversation_state.get("clarified_year_month_for_customer_db"):
                                thoughts_adapter.info(f"Destination is 'customer_db', but year/month not yet clarified. Setting pending state.")
                                current_conversation_state["pending_customer_db_clarification"] = True
                                current_conversation_state["original_query_for_customer_db"] = query_to_process 
                                agent_outcome_dict = {
                                    "final_response": "To query the customer database, I need to know which year and month you're interested in. For example, you can say 'for 2023 April' or 'show data from May 2024'.",
                                    "determined_kb_or_tool": "customer_db_needs_clarification",
                                    "generated_sql": None,
                                    "final_reaction": "calendar"
                                }
                            else: 
                                year_to_pass, month_to_pass = None, None
                                if destination_name == "customer_db" and current_conversation_state.get("clarified_year_month_for_customer_db"):
                                    year_to_pass = current_conversation_state["clarified_year_month_for_customer_db"]["year"]
                                    month_to_pass = current_conversation_state["clarified_year_month_for_customer_db"]["month"]
                                    current_conversation_state["clarified_year_month_for_customer_db"] = None 

                                agent_outcome_dict = await query_knowledge_base(query_to_process, destination_name, conversation_history, self.llm_for_details, thoughts_adapter, year=year_to_pass, month=month_to_pass, agent_instance=self) # Pass agent_instance
                                if agent_outcome_dict.get("is_pending_table_confirmation"):
                                    current_conversation_state["pending_table_confirmation"] = True
                                    current_conversation_state["data_for_pending_table_str"] = agent_outcome_dict.get("raw_data_for_table_str")
                                    current_conversation_state["original_query_for_pending_table"] = query_to_process
                                    current_conversation_state["title_for_pending_table"] = f"Data regarding: {query_to_process}"
                                    thoughts_adapter.info(f"Setting pending table confirmation from KB '{destination_name}'. Data stored. Original query for table: {query_to_process}")
                        else: 
                            thoughts_adapter.error(f"Logical error: Destination '{destination_name}' was access-checked but not a known tool or KB.")
                            agent_outcome_dict = {
                                "final_response": f"An unexpected routing error occurred after access check for destination '{destination_name}'.",
                                "determined_kb_or_tool": f"routing_logic_error ({destination_name})",
                                "generated_sql": None,
                                "final_reaction": "warning"
                            }
                elif destination_name == "default": 
                    thoughts_adapter.info("Routing resulted in 'default'. Initiating default handling logic.")
                    agent_outcome_dict = await _handle_default_routing(self, query_to_process, actual_user_role, user_identifier, conversation_id, self.router, conversation_history, self.llm_for_details, thoughts_adapter) # type: ignore
                    if agent_outcome_dict.get("is_pending_table_confirmation"):
                        current_conversation_state["pending_table_confirmation"] = True
                        current_conversation_state["data_for_pending_table_str"] = agent_outcome_dict.get("raw_data_for_table_str")
                        current_conversation_state["original_query_for_pending_table"] = query_to_process
                        current_conversation_state["title_for_pending_table"] = f"Data regarding: {query_to_process}"
                        thoughts_adapter.info(f"Setting pending table confirmation from default routing. Data stored. Original query for table: {query_to_process}")
                else: 
                    thoughts_adapter.error(f"Unhandled destination '{destination_name}' from SemanticRouter. This should not happen.")
                    agent_outcome_dict = {
                        "final_response": final_response_from_brainstorm,
                        "determined_kb_or_tool": "conversational_llm_json_parse_failed",
                        "generated_sql": None,
                        "final_reaction": "warning"
                    }

        # If agent_outcome_dict was not set by a pending state that returns early (like reformulation request),
        # it means we processed the query and have a result.
        if 'agent_outcome_dict' not in locals(): # Should ideally always be set by this point
            thoughts_adapter.error("Agent outcome dict not set before history management. This indicates a logic flow error.")
            agent_outcome_dict = {"final_response": "An unexpected internal error occurred.", "determined_kb_or_tool": "internal_flow_error", "final_reaction": "x"}
        # Manage conversation history and return
        # This block is now guaranteed to run after agent_outcome_dict is finalized.
        final_response_for_history = agent_outcome_dict.get("final_response", "Error: No final response in agent_outcome_dict.")
        thoughts_adapter.info(f"Final response for user: '{final_response_for_history}'")

                # --- Enhanced User Tagging Logic ---
        slack_id_for_mention = None
        user_name_for_greeting = None

        if user_identifier: # user_identifier is the email or Slack ID passed to this method
            # Check if user_identifier is already a Slack ID
            if (user_identifier.startswith(("U", "W"))) and len(user_identifier) > 5:
                slack_id_for_mention = user_identifier
                thoughts_adapter.info(f"User identifier '{user_identifier}' is already a Slack ID.")
            # If it's not a Slack ID but contains '@', assume it's an email and try to look up Slack ID
            elif "@" in user_identifier and self.slack_app and self.slack_app.client:
                try:
                    thoughts_adapter.info(f"User identifier '{user_identifier}' looks like an email. Attempting users.lookupByEmail.")
                    email_lookup_response = await self.slack_app.client.users_lookupByEmail(email=user_identifier)
                    if email_lookup_response and email_lookup_response.get("ok"):
                        found_user = email_lookup_response.get("user", {})
                        slack_id_for_mention = found_user.get("id")
                        if slack_id_for_mention:
                            thoughts_adapter.info(f"Successfully looked up Slack ID '{slack_id_for_mention}' for email '{user_identifier}'.")
                            # Optionally, try to get name from this response if profile is included
                            user_profile = found_user.get("profile", {})
                            user_full_name = user_profile.get("display_name") or user_profile.get("real_name_normalized") or user_profile.get("real_name")
                            if user_full_name:
                                user_name_for_greeting = user_full_name.split()[0].capitalize()
                        else:
                            thoughts_adapter.warning(f"users.lookupByEmail for '{user_identifier}' response OK but no user ID found.")
                    else:
                        thoughts_adapter.warning(f"users.lookupByEmail for '{user_identifier}' failed. Error: {email_lookup_response.get('error', 'Unknown error')}")
                except Exception as e: # Handles API errors, network issues, etc.
                    thoughts_adapter.error(f"Exception during users.lookupByEmail for '{user_identifier}': {e}", exc_info=True)
            
            # If we have a Slack ID (either original or looked up) and haven't gotten name yet, try users_info
            if slack_id_for_mention and not user_name_for_greeting and self.slack_app and self.slack_app.client:
                try:
                    user_info_response = await self.slack_app.client.users_info(user=slack_id_for_mention)
                    if user_info_response and user_info_response.get("ok"):
                        user_profile = user_info_response.get("user", {}).get("profile", {})
                        user_full_name = user_profile.get("display_name") or user_profile.get("real_name_normalized") or user_profile.get("real_name")
                        if user_full_name:
                            user_name_for_greeting = user_full_name.split()[0].capitalize()
                            thoughts_adapter.info(f"Fetched user's first name: '{user_name_for_greeting}' for Slack ID {slack_id_for_mention} via users.info.")
                    else:
                        thoughts_adapter.warning(f"users.info for {slack_id_for_mention} failed. Error: {user_info_response.get('error', 'Unknown error')}")
                except Exception as e:
                    thoughts_adapter.error(f"Exception fetching user info via users.info for {slack_id_for_mention}: {e}", exc_info=True)

            if slack_id_for_mention:
                # Check if response already starts with a Slack user mention (e.g., <@U...>, <@W...>)
                # and if tagging is not explicitly skipped
                already_tagged = re.match(r"^<@[UW][A-Z0-9]{8,}>", final_response_for_history) is not None
                
                if not agent_outcome_dict.get("skip_final_user_tagging", False) and not already_tagged:
                    tag_prefix = f"<@{slack_id_for_mention}>"
                    # Optional: Add name to greeting if available and desired
                    # if user_name_for_greeting: tag_prefix += f" Hey {user_name_for_greeting},"
                    final_tagged_response = f"{tag_prefix} {final_response_for_history}"
                    agent_outcome_dict["final_response"] = final_tagged_response
                    final_response_for_history = final_tagged_response 
                    thoughts_adapter.info(f"Prepended user mention/greeting. Updated final response for {slack_id_for_mention}: '{final_response_for_history}'")
                else:
                    thoughts_adapter.info(f"Skipped final user tagging for {slack_id_for_mention} due to 'skip_final_user_tagging' flag.")
            else:
                thoughts_adapter.warning(f"Could not determine Slack ID for mention for user_identifier '{user_identifier}'. Not prepending mention.")
            if already_tagged:
                thoughts_adapter.info(f"Final response for {slack_id_for_mention} already started with a user tag. Not prepending another.")
        else:
            thoughts_adapter.warning(f"User identifier is None or empty. Not prepending mention.")
        # --- End of Enhanced User Tagging Logic ---


        # Store raw data if the response was from a KB and potentially table-worthy, or if it's a direct KB response not pending table.
        if agent_outcome_dict.get("determined_kb_or_tool", "").endswith(("_conversational_fallback_no_info", "_table_confirmation_declined")):
            current_conversation_state["last_retrieved_structured_data_for_tool_follow_up"] = None # Clear if fallback or declined
        elif agent_outcome_dict.get("raw_data_for_table_str"): # This is set by query_knowledge_base if data is structured for table
            current_conversation_state["last_retrieved_structured_data_for_tool_follow_up"] = agent_outcome_dict["raw_data_for_table_str"]
            thoughts_adapter.info(f"Stored raw_data_for_table_str ('{current_conversation_state['last_retrieved_structured_data_for_tool_follow_up'][:100]}...') for potential tool follow-up in conv {conversation_id}.")
        elif agent_outcome_dict.get("determined_kb_or_tool") in self.router.knowledge_banks and not agent_outcome_dict.get("is_pending_table_confirmation"):
            # If it was a KB call, not pending table confirmation, and not raw_data_for_table_str, store the final_response as potential context.
            current_conversation_state["last_retrieved_structured_data_for_tool_follow_up"] = final_response_for_history # Could be formatted text
            thoughts_adapter.info(f"Stored final_response from KB ('{current_conversation_state['last_retrieved_structured_data_for_tool_follow_up'][:100]}...') for potential tool follow-up in conv {conversation_id}.")
        elif not (agent_outcome_dict.get("determined_kb_or_tool") in self.router.tools) : # If not a tool and not a KB, clear it.
             current_conversation_state["last_retrieved_structured_data_for_tool_follow_up"] = None

        thoughts_adapter.info(f"Determined KB/Tool for logging: '{agent_outcome_dict.get('determined_kb_or_tool', 'N/A')}'")
        thoughts_adapter.info(f"Generated SQL for logging: '{agent_outcome_dict.get('generated_sql', 'N/A')}'")

        # Ensure a default final_reaction if none was set (e.g., successful direct answer from brainstorm)
        if "final_reaction" not in agent_outcome_dict:
            agent_outcome_dict["final_reaction"] = "white_check_mark" # Default success

        current_conversation_state["history"].append({"role": "user", "content": original_user_query}) # Log the original query
        current_conversation_state["history"].append({"role": "assistant", "content": final_response_for_history})
        if len(current_conversation_state["history"]) > self.MAX_HISTORY_TURNS * 2:
            current_conversation_state["history"] = current_conversation_state["history"][-(self.MAX_HISTORY_TURNS * 2):]
            thoughts_adapter.debug(f"Conversation history for {conversation_id} trimmed to last {self.MAX_HISTORY_TURNS} turns. Current length: {len(current_conversation_state['history'])}")
        thoughts_adapter.info("Interaction processing complete.")
        return agent_outcome_dict, history_snapshot_for_logging