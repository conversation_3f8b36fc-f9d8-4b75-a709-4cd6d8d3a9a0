from .access_dict import access_controls  # Import the access dictionary using relative import
import logging

# Get a logger for this module
logger = logging.getLogger(__name__)

def determine_access(role, requested_knowledge_base):
    """
    Determines if a user has access to a specific knowledge base based on their role.

    Args:
        role (str): The role of the user.
        requested_knowledge_base (str): The knowledge base the user wants to access.

    Returns:
        dict: A dictionary indicating whether access is granted or denied.
    """
    logger.debug(f"Determining access for role '{role}' to resource '{requested_knowledge_base}'.")
    # Check if the role exists in the access control dictionary
    if role not in access_controls:
        logger.warning(f"Access Denied: Role '{role}' does not exist in access_controls.")
        return {
            "status": "Access Denied",
            "reason": f"Role '{role}' does not exist in the access control list.",
        }

    # Check if the requested knowledge base is in the allowed set for the role
    if requested_knowledge_base in access_controls[role]:
        logger.info(f"Access Granted: Role '{role}' to resource '{requested_knowledge_base}'.")
        return {
            "status": "Access Granted",
            "knowledge_base": requested_knowledge_base,
        }
    else:
        logger.warning(
            f"Access Denied: Resource '{requested_knowledge_base}' not accessible for role '{role}'."
        )
        return {
            "status": "Access Denied",
            "knowledge_base": requested_knowledge_base,
            "reason": f"'{requested_knowledge_base}' is not accessible for role '{role}'.",
        }


# # Example usage (kept commented as in original)
# if __name__ == "__main__":
# #    logging.basicConfig(level=logging.DEBUG) # Basic config for direct script run
#     role = "Role_1"  # Replace with the user's role
#     requested_base = "Database"  # Replace with the requested knowledge base
#     access = determine_access(role, requested_base)
#     logger.debug(access) # Changed print to logger.debug for consistency if uncommented