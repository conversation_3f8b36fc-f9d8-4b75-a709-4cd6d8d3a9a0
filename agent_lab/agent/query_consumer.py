from core.config import config as credentials 
from sqlalchemy import create_engine
import pandas as pd
import psycopg2 


host = credentials.host
port = credentials.port
username = credentials.dwh_u
password = credentials.dwh_p
dbname = credentials.dbname


def read_sql_query_dwh(script):
    conn = psycopg2.connect(f'dbname={dbname} host={host} port={port} user={username} password={password}')
    dfs = pd.read_sql_query(script,conn)
    conn.close()
    return dfs






