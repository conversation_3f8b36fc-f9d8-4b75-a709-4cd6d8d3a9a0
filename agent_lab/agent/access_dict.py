# Dictionary of roles representing access levels
access_controls = {   'admin': [   'cleary_knowledge_base',
                 'financial_reporting_tool',
                 'hr_sql_database',
                 'jira_project_management',
                 'kissflow',
                 'marketing_analytics_dashboard',
                 'product_roadmap_tool',
                 'slack_communication_archive',
                 'social_media_management_tool',
                 'transactions_sql_database',
                 'vector_store',
                 'report_vector_db',
                 'web_search'],
    'dev': [   'cleary_knowledge_base',
               'customer_db',
               'hr_sql_database',
               'jira_project_management',
               'message_sender_tool',
               'plotly_chart_generator',
               'report_generator',
               'slack_communication_archive',
               'report_vector_db',
               'summarize_dm_conversation_tool',
               'table_generator',
               'transactions_sql_database',
               'user_info_tool',
               'vector_store',
               'web_search'],
    'hr_team': ['cleary_knowledge_base', 'hr_sql_database', 'web_search'],
    'limited_access': ['web_search'],
    'management': [   'cleary_knowledge_base',
                      'hr_sql_database',
                      'jira_project_management',
                      'slack_communication_archive',
                      'transactions_sql_database',
                      'web_search'],
    'manager': [],
    'marketing_team': ['kissflow', 'marketing_analytics_dashboard', 'social_media_management_tool', 'web_search'],
    'product_team': ['jira_project_management', 'product_roadmap_tool'],
    'sales': [],
    'security': ['product_roadmap_tool', 'transactions_sql_database', 'web_search']}


# Dictionary of user groups, their members, and assigned roles
user_groups = {   'FLW_DATA_TEAM': {   'roles': ['dev'],
                         'users': ['<EMAIL>', '<EMAIL>', '<EMAIL>']},
    'Security': {'roles': ['security'], 'users': ['<EMAIL>']}}
