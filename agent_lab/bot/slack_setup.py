from agent_lab.agent.permissions import get_authorized_users
from core.config import config
from slack_bolt.async_app import <PERSON><PERSON><PERSON><PERSON>
from agent_lab.agent.query_translator import query_translation
import logs # This will execute the logging setup in logs.py upon import
from agent_lab.agent.agent import Agent # Import the Agent class
import logging # Import logging to use the logger
import asyncio # Import asyncio

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

slack_bolt_app = AsyncApp(token=config.slack_bot_token)

# Store the bot's own user ID
bot_user_id = None

# Instantiate the agent once globally when the app starts
try:
    # Pass the slack_bolt_app to the Agent so it can use the client if needed (e.g., for message_sender_tool)
    flaire_agent = Agent(slack_app=slack_bolt_app)
    print("Zia AI Assistant initialized successfully.")
except Exception as e:
    logger.critical(f"CRITICAL: Failed to initialize Flaire AI Agent: {e}", exc_info=True)
    flaire_agent = None # Set to None if initialization fails

async def fetch_bot_user_id_on_startup(client):
    """Fetches and stores the bot's user ID using the provided client."""
    global bot_user_id
    if not bot_user_id:
        try:
            auth_test_response = await client.auth_test()
            bot_user_id = auth_test_response.get("user_id")
            if bot_user_id:
                print(f"Successfully fetched Bot User ID: {bot_user_id}")
                # return
            else:
                logger.error(f"Could not extract bot_user_id from auth_test response: {auth_test_response}")
            return
        except Exception as e:
            logger.error(f"Error fetching bot user ID during startup: {e}", exc_info=True)


@slack_bolt_app.event("message")
async def handle_message_events(message, say, client): # Added client to access client.users_info, say to send messages
    print(f"App message event received: User {message.get('user')}, Text: {message.get('text')}")
    global bot_user_id
    if not bot_user_id: # Ensure bot_user_id is fetched if it wasn't during startup (e.g., local dev without full startup sequence)
        logger.warning("Bot User ID not yet fetched. Attempting to fetch now...")
        await fetch_bot_user_id_on_startup(client) # Use the event's client
        if not bot_user_id:
            logger.error("Bot User ID is still not available. Cannot process mentions correctly.")
            # Optionally, inform the user if it's a direct interaction attempt
            # await say("I'm having trouble identifying myself right now. Please try again in a moment.")
            return

    if not flaire_agent:
        logger.error("Agent not initialized. Cannot process message.")
        await say("I'm currently unable to process requests due to an internal issue. Please try again later.")
        return

    user_id = message.get("user")
    text = message.get("text", "") # Default to empty string if not present
    channel_id = message.get("channel")
    channel_type = message.get("channel_type")
    message_ts = message.get("ts") # Timestamp of the incoming message
    thread_ts = message.get("thread_ts") # Timestamp of the parent message if in a thread

    processed_text = text
    conversation_id = None
    should_process = False

    if channel_type == "im": # Direct Message
        conversation_id = user_id # Conversation is with the user
        should_process = True
        print(f"Direct message received from user {user_id}. Conversation ID: {conversation_id}. Text: '{text[:50]}...'")
    elif bot_user_id and f"<@{bot_user_id}>" in text: # Mention in a channel/group
        conversation_id = channel_id # Conversation is within the channel context
        processed_text = text.replace(f"<@{bot_user_id}>", "").strip() # Remove mention
        should_process = True
        print(f"Bot mentioned by user {user_id} in channel {channel_id}. Conversation ID: {conversation_id}. Processed text: '{processed_text[:50]}...'")
    else: # Message in a channel not mentioning the bot
        print(f"Message in channel {channel_id} (type: {channel_type}) by user {user_id} without bot mention. Ignoring. Text: '{text[:50]}...'")
        return

    if not should_process or not processed_text: # If it was just a mention with no further text, or shouldn't process
        print(f"Not processing message. should_process: {should_process}, processed_text empty: {not processed_text}. Original text: '{text[:50]}...'")
        return

    # Add initial reaction
    if message_ts and channel_id:
        try:
            await client.reactions_add(channel=channel_id, name="white_check_mark", timestamp=message_ts)
            print(f"Added 'white_check_mark' (acknowledged) reaction to message {message_ts} in channel {channel_id}.")
        except Exception as e:
            logger.error(f"Failed to add initial 'white_check_mark' reaction in handle_message_events: {e}", exc_info=True)
    try:

        user_info = await client.users_info(user=user_id) # Use the event's client
        user_email = user_info.get("user", {}).get("profile", {}).get("email")
        if not user_email:
            logger.warning(f"Could not retrieve email for user_id: {user_id}. Using user_id as identifier.")
            user_email = user_id
        # print(f"User {user_email} is authorized to use the agent.")
        # approved = get_authorized_users(user_email)
        # print(f"User {user_email} is {approved} to use the agent.")
        
        # if not approved:
        #     logger.warning(f"User {user_email} is not authorized to use the agent. Using fallback permissions.")
        #     return await say(
        #         channel=channel_id,
        #         text="You are not authorized to use this agent. Please contact support if you believe this is an error."
        #     )
        agent_result_dict = await logs.handle_interaction_and_log(
            user_query=processed_text,
            user_role=user_email,
            agent_instance=flaire_agent,
            conversation_id=conversation_id, # Pass the determined conversation_id
            channel_id=channel_id,           # Pass for potential mid-process reactions
            message_ts=message_ts            # Pass for potential mid-process reactions
        )
        
        agent_final_text_response = agent_result_dict.get("final_response", "Sorry, I encountered an issue processing your request.")
        final_suggested_reaction = agent_result_dict.get("final_reaction")

        # Add the agent-suggested final reaction
        if final_suggested_reaction and message_ts and channel_id:
            try:
                await client.reactions_add(channel=channel_id, name=final_suggested_reaction, timestamp=message_ts)
                print(f"Added final reaction '{final_suggested_reaction}' to message {message_ts} in channel {channel_id}.")
            except Exception as e:
                logger.error(f"Failed to add final_reaction '{final_suggested_reaction}' in handle_message_events: {e}", exc_info=True)

        reply_args = {"text": agent_final_text_response}
        # If the original message was in a thread, reply in that thread.
        # If it's a direct message (channel_type == "im"), thread_ts might be None,
        # and say() will correctly post directly to the IM.
        if thread_ts:
            reply_args["thread_ts"] = thread_ts
        await say(**reply_args)
    except Exception as e:
        logger.error(f"Error processing message in handle_message_events: {e}", exc_info=True)
        await say("Sorry, I encountered an error while processing your request. Please try again.")