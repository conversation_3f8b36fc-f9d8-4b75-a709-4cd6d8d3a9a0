{"cells": [{"cell_type": "code", "execution_count": 1, "id": "01eb18db", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "b9b408aa", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "reportType", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "webUrl", "rawType": "object", "type": "string"}, {"name": "createdDateTime", "rawType": "object", "type": "string"}, {"name": "modifiedDateTime", "rawType": "object", "type": "string"}, {"name": "modifiedBy", "rawType": "object", "type": "string"}, {"name": "created<PERSON>y", "rawType": "object", "type": "string"}, {"name": "Data Partner", "rawType": "object", "type": "string"}, {"name": "Description", "rawType": "object", "type": "string"}, {"name": "Metrics Captured", "rawType": "object", "type": "string"}], "ref": "a38b0384-e05a-4af2-868b-989ae13fc326", "rows": [["0", "PowerBIReport", "[App] Settlement team dashboard v3", "https://app.powerbi.com/groups/me/apps/e9eaf066-79aa-4d61-918d-df0c3d46ff85/reports/08fdd65b-2e82-427a-a495-04515d76bb28", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable"], ["1", "PowerBIReport", "[App] main MPGS", "https://app.powerbi.com/groups/me/apps/e9eaf066-79aa-4d61-918d-df0c3d46ff85/reports/612880de-2b56-4d65-abcc-7b88bb3b0c48", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable"], ["2", "PowerBIReport", "Sales and Marketing Sample PBIX", "https://app.powerbi.com/groups/me/reports/114fdc88-8670-4b51-a3b2-14318db96e1c", "2020-08-23T22:38:40.457Z", "2020-08-23T22:38:40.457Z", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable"], ["3", "PowerBIReport", "[App] Sales and Marketing Sample PBIX", "https://app.powerbi.com/groups/me/apps/1e116555-24cc-4c8a-ba17-21ac2022c69f/reports/114fdc88-8670-4b51-a3b2-14318db96e1c", "2020-08-23T22:38:40.72Z", "2020-08-23T22:38:40.72Z", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable"], ["4", "PowerBIReport", "Project for Web Report Pack", "https://app.powerbi.com/groups/me/reports/8c0968f5-5228-48b8-9788-62045a0dce6d", "2020-09-23T17:56:28.403Z", "2020-09-23T17:56:28.403Z", "Unavailable", "Unavailable", "Unavailable", "Unavailable", "Unavailable"]], "shape": {"columns": 10, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reportType</th>\n", "      <th>name</th>\n", "      <th>webUrl</th>\n", "      <th>createdDateTime</th>\n", "      <th>modifiedDateTime</th>\n", "      <th>modifiedBy</th>\n", "      <th>createdBy</th>\n", "      <th>Data Partner</th>\n", "      <th>Description</th>\n", "      <th>Metrics Captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>PowerBIReport</td>\n", "      <td>[App] Settlement team dashboard v3</td>\n", "      <td>https://app.powerbi.com/groups/me/apps/e9eaf06...</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PowerBIReport</td>\n", "      <td>[App] main MPGS</td>\n", "      <td>https://app.powerbi.com/groups/me/apps/e9eaf06...</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>PowerBIReport</td>\n", "      <td>Sales and Marketing Sample PBIX</td>\n", "      <td>https://app.powerbi.com/groups/me/reports/114f...</td>\n", "      <td>2020-08-23T22:38:40.457Z</td>\n", "      <td>2020-08-23T22:38:40.457Z</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>PowerBIReport</td>\n", "      <td>[App] Sales and Marketing Sample PBIX</td>\n", "      <td>https://app.powerbi.com/groups/me/apps/1e11655...</td>\n", "      <td>2020-08-23T22:38:40.72Z</td>\n", "      <td>2020-08-23T22:38:40.72Z</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>PowerBIReport</td>\n", "      <td>Project for Web Report Pack</td>\n", "      <td>https://app.powerbi.com/groups/me/reports/8c09...</td>\n", "      <td>2020-09-23T17:56:28.403Z</td>\n", "      <td>2020-09-23T17:56:28.403Z</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "      <td>Unavailable</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      reportType                                   name  \\\n", "0  PowerBIReport     [App] Settlement team dashboard v3   \n", "1  PowerBIReport                        [App] main MPGS   \n", "2  PowerBIReport        Sales and Marketing Sample PBIX   \n", "3  PowerBIReport  [App] Sales and Marketing Sample PBIX   \n", "4  PowerBIReport            Project for Web Report Pack   \n", "\n", "                                              webUrl  \\\n", "0  https://app.powerbi.com/groups/me/apps/e9eaf06...   \n", "1  https://app.powerbi.com/groups/me/apps/e9eaf06...   \n", "2  https://app.powerbi.com/groups/me/reports/114f...   \n", "3  https://app.powerbi.com/groups/me/apps/1e11655...   \n", "4  https://app.powerbi.com/groups/me/reports/8c09...   \n", "\n", "            createdDateTime          modifiedDateTime   modifiedBy  \\\n", "0               Unavailable               Unavailable  Unavailable   \n", "1               Unavailable               Unavailable  Unavailable   \n", "2  2020-08-23T22:38:40.457Z  2020-08-23T22:38:40.457Z  Unavailable   \n", "3   2020-08-23T22:38:40.72Z   2020-08-23T22:38:40.72Z  Unavailable   \n", "4  2020-09-23T17:56:28.403Z  2020-09-23T17:56:28.403Z  Unavailable   \n", "\n", "     createdBy Data Partner  Description Metrics Captured  \n", "0  Unavailable  Unavailable  Unavailable      Unavailable  \n", "1  Unavailable  Unavailable  Unavailable      Unavailable  \n", "2  Unavailable  Unavailable  Unavailable      Unavailable  \n", "3  Unavailable  Unavailable  Unavailable      Unavailable  \n", "4  Unavailable  Unavailable  Unavailable      Unavailable  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('reports.csv')\n", "df.head(5)"]}, {"cell_type": "code", "execution_count": 9, "id": "5ad90f29", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['reportType', 'name', 'webUrl', 'createdDateTime', 'modifiedDateTime',\n", "       'modifiedBy', 'createdBy', 'Data Partner', 'Description',\n", "       'Metrics Captured'],\n", "      dtype='object')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 6, "id": "e39d9c21", "metadata": {}, "outputs": [], "source": ["df = df.fillna('Unavailable')"]}, {"cell_type": "code", "execution_count": null, "id": "018bf951", "metadata": {}, "outputs": [], "source": ["new_df = df['name']"]}, {"cell_type": "code", "execution_count": 4, "id": "b174d921", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "int64", "type": "integer"}], "ref": "8dd1c02b-d728-4f42-97d4-5359c51a941f", "rows": [["Report", "0"], ["Report Description", "5"], ["Metrics Captured", "2"], ["Report Type", "0"], ["Relevant Links", "0"], ["Workspace", "0"], ["Data Partner", "0"], ["Unnamed: 7", "154"], ["Unnamed: 8", "153"]], "shape": {"columns": 1, "rows": 9}}, "text/plain": ["Report                  0\n", "Report Description      5\n", "Metrics Captured        2\n", "Report Type             0\n", "Relevant Links          0\n", "Workspace               0\n", "Data Partner            0\n", "Unnamed: 7            154\n", "Unnamed: 8            153\n", "dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum()"]}, {"cell_type": "code", "execution_count": 6, "id": "58a3178c", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "object", "type": "string"}, {"name": "0", "rawType": "float64", "type": "float"}], "ref": "7644914e-a104-4cbc-9aea-f274e9e88864", "rows": [["Report", "0.0"], ["Report Description", "0.0"], ["Metrics Captured", "0.0"], ["Report Type", "0.0"], ["Relevant Links", "0.0"], ["Workspace", "0.0"], ["Data Partner", "0.0"], ["Unnamed: 7", "0.0"], ["Unnamed: 8", "0.0"]], "shape": {"columns": 1, "rows": 9}}, "text/plain": ["Report                0.0\n", "Report Description    0.0\n", "Metrics Captured      0.0\n", "Report Type           0.0\n", "Relevant Links        0.0\n", "Workspace             0.0\n", "Data Partner          0.0\n", "Unnamed: 7            0.0\n", "Unnamed: 8            0.0\n", "dtype: float64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_no_null.isnull().sum()"]}, {"cell_type": "code", "execution_count": 8, "id": "b59813f5", "metadata": {}, "outputs": [], "source": ["csv_filename = 'report_data.csv'\n", "\n", "df.to_csv(csv_filename, index=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "16f2ff34", "metadata": {}, "outputs": [], "source": ["cse_id=\"e320dbfc9b10a4882\"\n", "api_key=\"AIzaSyA_SDwiKK-5ck-R-Do8e95cxmlBSabRwN8\""]}, {"cell_type": "code", "execution_count": 9, "id": "7d632779", "metadata": {}, "outputs": [], "source": ["from googleapiclient.discovery import build\n", "\n", "def google_custom_search(api_key, cse_id, query):\n", "    service = build(\"customsearch\", \"v1\", developerKey=api_key)\n", "    res = service.cse().list(q=query, cx=cse_id).execute()\n", "    return res.get('items', [])"]}, {"cell_type": "code", "execution_count": 10, "id": "e77df841", "metadata": {}, "outputs": [], "source": ["search_results = google_custom_search(api_key, cse_id, \"What's the latest news on flutterwave?\")"]}, {"cell_type": "code", "execution_count": 12, "id": "3bed7679", "metadata": {}, "outputs": [{"data": {"text/plain": ["list"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["type(search_results)"]}, {"cell_type": "code", "execution_count": 14, "id": "60506327", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'kind': 'customsearch#result', 'title': 'Communication Regarding Media Reports on Flutterwave Kenya ...', 'htmlTitle': 'Communication Regarding Media Reports on <b>Flutterwave</b> Kenya ...', 'link': 'https://flutterwave.com/ng/blog/on-media-reports-about-flutterwave-kenya', 'displayLink': 'flutterwave.com', 'snippet': 'Jul 7, 2022 ... Enter your email to get the latest news from the Flutterwave team, and knowledge you need to build a profitable business. Subscribe. More\\xa0...', 'htmlSnippet': 'Jul 7, 2022 <b>...</b> Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team, and knowledge you need to build a profitable business. Subscribe. More&nbsp;...', 'formattedUrl': 'https://flutterwave.com/ng/blog/on-media-reports-about-flutterwave-kenya', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/ng/blog/on-media-reports-about-<b>flutterwave</b>-kenya', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQiwkLjSopEpfUFSoKgHxjS_s3tcjYmHzFzUmtVwK-i43IFY9NuXwuJbDs&s', 'width': '300', 'height': '168'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2022/07/Email_blog_1.jpg', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Communication Regarding Media Reports on Flutterwave Kenya | The Flutterwave Blog', 'og:site_name': 'Communication Regarding Media Reports on Flutterwave Kenya | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Communication Regarding Media Reports on Flutterwave Kenya | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'Claims of financial improprieties involving the company in Kenya are entirely false, and we have the records to verify this. Through our financial institution partners, we collect and pay on behalf of merchants and corporate entities. In the process we earn our fees through a transaction charge, records of which are available and can be […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2022/07/Email_blog_1.jpg', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'Claims of financial improprieties involving the company in Kenya are entirely false, and we have the records to verify this. Through our financial institution partners, we collect and pay on behalf of merchants and corporate entities. In the process we earn our fees through a transaction charge, records of which are available and can be […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2022/07/Email_blog_1.jpg'}]}}, {'kind': 'customsearch#result', 'title': 'Barter is Changing Things Up! | The Flutterwave Blog', 'htmlTitle': 'Barter is Changing Things Up! | The <b>Flutterwave</b> Blog', 'link': 'https://flutterwave.com/ng/blog/barter-is-changing-things-up', 'displayLink': 'flutterwave.com', 'snippet': 'Feb 1, 2023 ... What You Can Still Do: Transferring funds from your Barter balance to bank accounts. Bill payments and Airtime purchases from your current\\xa0...', 'htmlSnippet': 'Feb 1, 2023 <b>...</b> <b>What</b> You Can Still Do: Transferring funds from your Barter balance to bank accounts. Bill payments and Airtime purchases from your <b>current</b>&nbsp;...', 'formattedUrl': 'https://flutterwave.com/ng/blog/barter-is-changing-things-up', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/ng/blog/barter-is-changing-things-up', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS5_ts1i_LiWUqDdCSjIRE2jReY6l1R7-sHP78cm9-A14M4ZW2O3-fksgMf&s', 'width': '311', 'height': '162'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2023/02/Blog-Announcement-1.jpeg', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Barter is Changing Things Up! | The Flutterwave Blog', 'og:site_name': 'Barter is Changing Things Up! | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Barter is Changing Things Up! | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'For the past few months, we have been hard at work reimagining Barter to become a platform servicing payment and money transfer needs beyond what it satisfies today. This is a big change from what Barter was known for, and we want to ensure that the transition is seamless for you.\\xa0 To meet this commitment, […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2023/02/Blog-Announcement-1.jpeg', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'For the past few months, we have been hard at work reimagining Barter to become a platform servicing payment and money transfer needs beyond what it satisfies today. This is a big change from what Barter was known for, and we want to ensure that the transition is seamless for you.\\xa0 To meet this commitment, […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2023/02/Blog-Announcement-1.jpeg'}]}}, {'kind': 'customsearch#result', 'title': 'The Flutterwave Storefront Just Got Better With Our Latest ...', 'htmlTitle': 'The <b>Flutterwave</b> Storefront Just Got Better With Our <b>Latest</b> ...', 'link': 'https://flutterwave.com/ng/blog/the-flutterwave-storefront-just-got-better-with-our-latest-enhancements', 'displayLink': 'flutterwave.com', 'snippet': 'Mar 26, 2024 ... Enter your email to get the latest news from the Flutterwave team, and knowledge you need to build a profitable business. Subscribe. More\\xa0...', 'htmlSnippet': 'Mar 26, 2024 <b>...</b> Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team, and knowledge you need to build a profitable business. Subscribe. More&nbsp;...', 'formattedUrl': 'https://flutterwave.com/.../the-flutterwave-storefront-just-got-better-with-ou...', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/.../the-<b>flutterwave</b>-storefront-just-got-better-with-ou...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQlWaWE1gqCPZp6jjfmgRwXo2rmJq3rE-KwBmRYZG3YAF6vvijMnlXyDbU&s', 'width': '298', 'height': '169'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2024/03/Store-Update.png', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'The Flutterwave Storefront Just Got Better With Our Latest Enhancements | The Flutterwave Blog', 'og:site_name': 'The Flutterwave Storefront Just Got Better With Our Latest Enhancements | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'The Flutterwave Storefront Just Got Better With Our Latest Enhancements | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'In today’s competitive e-commerce industry, presenting a captivating and modern storefront is crucial to attracting and retaining customers. As usual, we at Flutterwave are committed to empowering businesses with tools for success.\\xa0 We are excited to announce the new updates to your storefront! New Feature Updates Modernised Storefront: Your brand identity is as important to […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2024/03/Store-Update.png', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'In today’s competitive e-commerce industry, presenting a captivating and modern storefront is crucial to attracting and retaining customers. As usual, we at Flutterwave are committed to empowering businesses with tools for success.\\xa0 We are excited to announce the new updates to your storefront! New Feature Updates Modernised Storefront: Your brand identity is as important to […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2024/03/Store-Update.png'}]}}, {'kind': 'customsearch#result', 'title': 'Flutterwave Partners EFCC to Establish Consortium Led Cybercrime ...', 'htmlTitle': '<b>Flutterwave</b> Partners EFCC to Establish Consortium Led Cybercrime ...', 'link': 'https://flutterwave.com/ng/blog/flutterwave-partners-efcc-to-establish-consortium-led-cybercrime-research-center', 'displayLink': 'flutterwave.com', 'snippet': 'Jun 20, 2024 ... The Cybercrime Research Center, to be established at the new EFCC ... Enter your email to get the latest news from the Flutterwave team\\xa0...', 'htmlSnippet': 'Jun 20, 2024 <b>...</b> The Cybercrime Research Center, to be established at the <b>new</b> EFCC ... Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team&nbsp;...', 'formattedUrl': 'https://flutterwave.com/.../flutterwave-partners-efcc-to-establish-consortium...', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/.../<b>flutterwave</b>-partners-efcc-to-establish-consortium...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR3o-tsSKdNVCRp8Xub3ASNWwMSUSvX7kES_P_AJS2OwEpnenpSdyTJ_WUx&s', 'width': '261', 'height': '193'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2024/06/FLW-x-EFCC-Photo-2.jpg', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Flutterwave Partners EFCC to Establish Consortium Led Cybercrime Research Center | The Flutterwave Blog', 'og:site_name': 'Flutterwave Partners EFCC to Establish Consortium Led Cybercrime Research Center | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Flutterwave Partners EFCC to Establish Consortium Led Cybercrime Research Center | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'Lagos, Nigeria — June 20, 2024: Flutterwave, Africa’s leading payments technology company, has committed to partnering with Nigeria’s Economic and Financial Crimes Commission (EFCC) to establish and lead a state-of-the-art Cybercrime Research Center. This strategic initiative aims to intensify the fight against internet crime, enhance the security of business transactions, and provide a sustainable lifeline […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2024/06/FLW-x-EFCC-Photo-2.jpg', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'Lagos, Nigeria — June 20, 2024: Flutterwave, Africa’s leading payments technology company, has committed to partnering with Nigeria’s Economic and Financial Crimes Commission (EFCC) to establish and lead a state-of-the-art Cybercrime Research Center. This strategic initiative aims to intensify the fight against internet crime, enhance the security of business transactions, and provide a sustainable lifeline […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2024/06/FLW-x-EFCC-Photo-2.jpg'}]}}, {'kind': 'customsearch#result', 'title': 'Flutterwave Hires World-Class Executives from Paypal, Stripe, and ...', 'htmlTitle': '<b>Flutterwave</b> Hires World-Class Executives from Paypal, Stripe, and ...', 'link': 'https://flutterwave.com/ng/blog/flutterwave-hires-world-class-executives-from-paypal-stripe-and-western-union-focusing-on-risk-compliance-and-payment-partnerships-to-amplify-growth', 'displayLink': 'flutterwave.com', 'snippet': \"Dec 13, 2023 ... ... last year. Amaresh's 25-year career includes leadership positions ... Enter your email to get the latest news from the Flutterwave team\\xa0...\", 'htmlSnippet': 'Dec 13, 2023 <b>...</b> ... <b>last</b> year. Amaresh&#39;s 25-year career includes leadership positions ... Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team&nbsp;...', 'formattedUrl': 'https://flutterwave.com/.../flutterwave-hires-world-class-executives-from-pa...', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/.../<b>flutterwave</b>-hires-world-class-executives-from-pa...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSEcCm8qII7vJjNKIj1FFRPhfc1AcVoboqdlKErmlZEPAtUauUbjTuqty46&s', 'width': '299', 'height': '168'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2023/12/7c72fc53-6c62-4a64-ac12-9219278e4107.jpg', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Flutterwave Hires World-Class Executives from Paypal, Stripe, and Western Union, Focusing on Risk, Compliance, and Payment Partnerships to Amplify Growth | The Flutterwave Blog', 'og:site_name': 'Flutterwave Hires World-Class Executives from Paypal, Stripe, and Western Union, Focusing on Risk, Compliance, and Payment Partnerships to Amplify Growth | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Flutterwave Hires World-Class Executives from Paypal, Stripe, and Western Union, Focusing on Risk, Compliance, and Payment Partnerships to Amplify Growth | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'San Francisco, December 13, 2023 – Flutterwave, Africa’s leading payments technology company, is excited to announce the appointment of several seasoned leaders from prominent financial services companies, including PayPal, Stripe, American Express, First Data, Western Union, and Square. These appointments reinforce Flutterwave’s dedication to risk and compliance to continue growing sustainably and successfully expanding globally.\\xa0 […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2023/12/7c72fc53-6c62-4a64-ac12-9219278e4107.jpg', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'San Francisco, December 13, 2023 – Flutterwave, Africa’s leading payments technology company, is excited to announce the appointment of several seasoned leaders from prominent financial services companies, including PayPal, Stripe, American Express, First Data, Western Union, and Square. These appointments reinforce Flutterwave’s dedication to risk and compliance to continue growing sustainably and successfully expanding globally.\\xa0 […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2023/12/7c72fc53-6c62-4a64-ac12-9219278e4107.jpg'}]}}, {'kind': 'customsearch#result', 'title': 'Afritickets Gets New Boost with Strategic Partnership with Flutterwave', 'htmlTitle': 'Afritickets Gets <b>New</b> Boost with Strategic Partnership with <b>Flutterwave</b>', 'link': 'https://flutterwave.com/ng/blog/afritickets-gets-new-boost-with-strategic-partnership-with-flutterwave', 'displayLink': 'flutterwave.com', 'snippet': 'Mar 16, 2022 ... The new collaboration will be significant for event organisers in ... Enter your email to get the latest news from the Flutterwave team\\xa0...', 'htmlSnippet': 'Mar 16, 2022 <b>...</b> The <b>new</b> collaboration will be significant for event organisers in ... Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team&nbsp;...', 'formattedUrl': 'https://flutterwave.com/.../afritickets-gets-new-boost-with-strategic-partners...', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/.../afritickets-gets-new-boost-with-strategic-partners...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRuHZ2pU56CkWbCOkYDw4ZyJPqmltApLt3JS8HDLG2sf0suOaVY0dhrRzU&s', 'width': '300', 'height': '168'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2022/03/AfriticketBlog-banner-2.png', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Afritickets Gets New Boost with Strategic Partnership with Flutterwave | The Flutterwave Blog', 'og:site_name': 'Afritickets Gets New Boost with Strategic Partnership with Flutterwave | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Afritickets Gets New Boost with Strategic Partnership with Flutterwave | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': '\\xa0Flutterwave will process payments for all events on Afritickets across the continent.\\xa0\\xa0 Event organisers on Afritickets will have access to eventgoers in over 34 countries.\\xa0 San Francisco, 16th March 2022 Flutterwave, a leading technology company, has partnered with Afritickets – an end-to-end event ticketing platform – to create more revenue opportunities for event organisers in […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2022/03/AfriticketBlog-banner-2.png', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': '\\xa0Flutterwave will process payments for all events on Afritickets across the continent.\\xa0\\xa0 Event organisers on Afritickets will have access to eventgoers in over 34 countries.\\xa0 San Francisco, 16th March 2022 Flutterwave, a leading technology company, has partnered with Afritickets – an end-to-end event ticketing platform – to create more revenue opportunities for event organisers in […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2022/03/AfriticketBlog-banner-2.png'}]}}, {'kind': 'customsearch#result', 'title': 'Create Anywhere, Sell Everywhere with Disha and Flutterwave | The ...', 'htmlTitle': 'Create Anywhere, Sell Everywhere with Disha and <b>Flutterwave</b> | The ...', 'link': 'https://flutterwave.com/ng/blog/create-anywhere-sell-everywhere-with-disha-and-flutterwave?ref=billionaires.africa', 'displayLink': 'flutterwave.com', 'snippet': \"Nov 9, 2021 ... What's more? There is absolutely no need to know how to write a ... Enter your email to get the latest news from the Flutterwave team\\xa0...\", 'htmlSnippet': 'Nov 9, 2021 <b>...</b> <b>What&#39;s</b> more? There is absolutely no need to know how to write a ... Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team&nbsp;...', 'formattedUrl': 'https://flutterwave.com/.../create-anywhere-sell-everywhere-with-disha-and-...', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/.../create-anywhere-sell-everywhere-with-disha-and-...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQwVC8sMCMOeSVns9sTDNvZGoGDCzcr5lkHvz089856JGvS7bwyfSUwTLk&s', 'width': '290', 'height': '174'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2021/11/pasted-image.png', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Create Anywhere, Sell Everywhere with Disha and Flutterwave | The Flutterwave Blog', 'og:site_name': 'Create Anywhere, Sell Everywhere with Disha and Flutterwave | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Create Anywhere, Sell Everywhere with Disha and Flutterwave | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'Being a creator is fun, but not without its hurdles. The lack of a suitable outlet to express creativity, complexities and costs of building a website, accepting payments from customers worldwide—the obstacles are both numerous and tedious. However, we’ve come up with a solution that is about to make the entire process a whole lot […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2021/11/pasted-image.png', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'Being a creator is fun, but not without its hurdles. The lack of a suitable outlet to express creativity, complexities and costs of building a website, accepting payments from customers worldwide—the obstacles are both numerous and tedious. However, we’ve come up with a solution that is about to make the entire process a whole lot […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2021/11/pasted-image.png'}]}}, {'kind': 'customsearch#result', 'title': 'Flutterwave Powers Local Businesses in Ghana Through Pay With ...', 'htmlTitle': '<b>Flutterwave</b> Powers Local Businesses in Ghana Through Pay With ...', 'link': 'https://flutterwave.com/ng/blog/flutterwave-powers-local-businesses-in-ghana-through-pay-with-bank-transfer', 'displayLink': 'flutterwave.com', 'snippet': 'Mar 21, 2025 ... While Mobile Money leads as the preferred payment type for everyday transactions in Ghana, the recent ... Enter your email to get the latest news\\xa0...', 'htmlSnippet': 'Mar 21, 2025 <b>...</b> While Mobile Money leads as the preferred payment type for everyday transactions in Ghana, the <b>recent</b> ... Enter your email to get the <b>latest news</b>&nbsp;...', 'formattedUrl': 'https://flutterwave.com/.../flutterwave-powers-local-businesses-in-ghana-thr...', 'htmlFormattedUrl': 'https://<b>flutterwave</b>.com/.../<b>flutterwave</b>-powers-local-businesses-in-ghana-thr...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQOYpiR6g1hoyPHDaYVsCm6ro5qh4TNO4vowR4EoKt_qva4k_1nCIBoDKon&s', 'width': '299', 'height': '168'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2025/03/GBH_1920x1080.jpg', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Flutterwave Powers Local Businesses in Ghana Through Pay With Bank Transfer | The Flutterwave Blog', 'og:site_name': 'Flutterwave Powers Local Businesses in Ghana Through Pay With Bank Transfer | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Flutterwave Powers Local Businesses in Ghana Through Pay With Bank Transfer | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'Accra, Ghana – 21 March, 2025 – Flutterwave, Africa’s leading payments technology company, has broadened its reach in Ghana through the integration of Pay With Bank Transfer, done in partnership with Affinity Bank.\\xa0 With over 115 million bank transfer payments recorded in Ghana in 2023, this move will ensure that Flutterwave businesses in Ghana can […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2025/03/GBH_1920x1080.jpg', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'Accra, Ghana – 21 March, 2025 – Flutterwave, Africa’s leading payments technology company, has broadened its reach in Ghana through the integration of Pay With Bank Transfer, done in partnership with Affinity Bank.\\xa0 With over 115 million bank transfer payments recorded in Ghana in 2023, this move will ensure that Flutterwave businesses in Ghana can […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2025/03/GBH_1920x1080.jpg'}]}}, {'kind': 'customsearch#result', 'title': \"IWD 2022; It's Time to Break the Bias By Funding More Women-led ...\", 'htmlTitle': 'IWD 2022; It&#39;s Time to Break the Bias By Funding More Women-led ...', 'link': 'https://www.flutterwave.com/ng/blog/flutterwave-international-womens-day-grant', 'displayLink': 'www.flutterwave.com', 'snippet': 'Mar 28, 2022 ... Enter your email to get the latest news from the Flutterwave team, and knowledge you need to build a profitable business. Subscribe. More\\xa0...', 'htmlSnippet': 'Mar 28, 2022 <b>...</b> Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team, and knowledge you need to build a profitable business. Subscribe. More&nbsp;...', 'formattedUrl': 'https://www.flutterwave.com/.../flutterwave-international-womens-day-gran...', 'htmlFormattedUrl': 'https://www.<b>flutterwave</b>.com/.../<b>flutterwave</b>-international-womens-day-gran...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQVjNW6Iu_nxnjJY_cSWEfTMmFbrRmHOJlsSeRhC5SSAvuczuQO_3qUHVhs&s', 'width': '300', 'height': '168'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2022/03/IWD-Email-1.png', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'IWD 2022; It’s Time to Break the Bias By Funding More Women-led Businesses\\xa0￼ | The Flutterwave Blog', 'og:site_name': 'IWD 2022; It’s Time to Break the Bias By Funding More Women-led Businesses\\xa0￼ | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'IWD 2022; It’s Time to Break the Bias By Funding More Women-led Businesses\\xa0￼ | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'What does it mean to break the bias? According to IFC, there’s a $42bn financing gap for women in business across Africa. Women in Africa make up 25% of business owners in the continent; one of the highest rates across the world. Breaking the bias starts from financing more women in business, granting them soft […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2022/03/IWD-Email-1.png', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'What does it mean to break the bias? According to IFC, there’s a $42bn financing gap for women in business across Africa. Women in Africa make up 25% of business owners in the continent; one of the highest rates across the world. Breaking the bias starts from financing more women in business, granting them soft […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2022/03/IWD-Email-1.png'}]}}, {'kind': 'customsearch#result', 'title': 'Flutterwave Secures Two Additional Licenses in Rwanda: Electronic ...', 'htmlTitle': '<b>Flutterwave</b> Secures Two Additional Licenses in Rwanda: Electronic ...', 'link': 'https://www.flutterwave.com/ng/blog/flutterwave-secures-two-additional-licenses-in-rwanda-electronic-money-issuer-remittance-licenses', 'displayLink': 'www.flutterwave.com', 'snippet': 'Mar 15, 2023 ... Enter your email to get the latest news from the Flutterwave team, and knowledge you need to build a profitable business. Subscribe. More\\xa0...', 'htmlSnippet': 'Mar 15, 2023 <b>...</b> Enter your email to get the <b>latest news</b> from the <b>Flutterwave</b> team, and knowledge you need to build a profitable business. Subscribe. More&nbsp;...', 'formattedUrl': 'https://www.flutterwave.com/.../flutterwave-secures-two-additional-licenses-...', 'htmlFormattedUrl': 'https://www.<b>flutterwave</b>.com/.../<b>flutterwave</b>-secures-two-additional-licenses-...', 'pagemap': {'cse_thumbnail': [{'src': 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTamWoBQ2aFQOoBuKtDpIDRyAzlDWzDlVjRCOAtok_HvyUBsKcXDT0Bao8&s', 'width': '310', 'height': '162'}], 'metatags': [{'msapplication-tilecolor': '#1D1D1D', 'og:image': 'https://v12.flutterwave.com/wp-content/uploads/2023/03/Rwanda-license-blog-3-100.jpg', 'og:type': 'website', 'theme-color': '#ffffff', 'twitter:card': 'summary_large_image', 'twitter:title': 'Flutterwave Secures Two Additional Licenses in Rwanda: Electronic Money Issuer & Remittance licenses | The Flutterwave Blog', 'og:site_name': 'Flutterwave Secures Two Additional Licenses in Rwanda: Electronic Money Issuer & Remittance licenses | The Flutterwave Blog', 'twitter:url': 'https://flutterwave.com/', 'apple-mobile-web-app-title': 'flw-web-v3', 'og:title': 'Flutterwave Secures Two Additional Licenses in Rwanda: Electronic Money Issuer & Remittance licenses | The Flutterwave Blog', 'language': 'en', 'twitter:creator': '@theflutterwave', 'og:description': 'Flutterwave’s new Electronic Money Issuer license allows the company to acquire all types of payment instruments in Rwanda.\\xa0 The company’s new remittance solution enables $end by Flutterwave to process inbound and outbound cross-border transfers in Rwanda.\\xa0 These new licenses enable Flutterwave to strengthen its operations in East Africa\\xa0 Kigali, Rwanda 15th March, 2023. Flutterwave, Africa’s […]', 'twitter:image:src': 'https://v12.flutterwave.com/wp-content/uploads/2023/03/Rwanda-license-blog-3-100.jpg', 'twitter:site': '@theflutterwave', 'viewport': 'width=device-width, initial-scale=1, shrink-to-fit=no', 'twitter:description': 'Flutterwave’s new Electronic Money Issuer license allows the company to acquire all types of payment instruments in Rwanda.\\xa0 The company’s new remittance solution enables $end by Flutterwave to process inbound and outbound cross-border transfers in Rwanda.\\xa0 These new licenses enable Flutterwave to strengthen its operations in East Africa\\xa0 Kigali, Rwanda 15th March, 2023. Flutterwave, Africa’s […]', 'og:locale': 'en_US', 'mobile-web-app-capable': 'yes', 'og:url': 'https://flutterwave.com/'}], 'cse_image': [{'src': 'https://v12.flutterwave.com/wp-content/uploads/2023/03/Rwanda-license-blog-3-100.jpg'}]}}]\n"]}], "source": ["print(search_results)"]}, {"cell_type": "code", "execution_count": 23, "id": "3c91a4de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n", "dict_keys(['kind', 'title', 'htmlTitle', 'link', 'displayLink', 'snippet', 'htmlSnippet', 'formattedUrl', 'htmlFormattedUrl', 'pagemap'])\n"]}], "source": ["for i in search_results:\n", "    print(i.keys())"]}, {"cell_type": "code", "execution_count": null, "id": "eeeb4627", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "id": "76b3a888", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\flw-nlp-platform\\.venv\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import faiss\n", "import json\n", "import numpy as np\n", "import os\n", "import logging\n", "from dotenv import load_dotenv\n", "import google.generativeai as genai\n", "from google.api_core import exceptions as google_exceptions"]}, {"cell_type": "code", "execution_count": 25, "id": "7ff28ff8", "metadata": {}, "outputs": [], "source": ["# --- Basic Configuration ---\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 26, "id": "7b8f4573", "metadata": {}, "outputs": [], "source": ["# --- Environment and API Key Setup ---\n", "logger.debug(\"Loading environment variables from .env file.\")\n", "load_dotenv()\n", "GEMINI_API_KEY = os.getenv(\"GEMINI_API_KEY\")"]}, {"cell_type": "code", "execution_count": 27, "id": "020fd245", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-16 17:42:30,218 - INFO - Google Generative AI SDK configured successfully.\n"]}], "source": ["if GEMINI_API_KEY:\n", "    try:\n", "        genai.configure(api_key=GEMINI_API_KEY)\n", "        logger.info(\"Google Generative AI SDK configured successfully.\")\n", "    except Exception as e:\n", "        logger.critical(f\"Failed to configure Google Generative AI SDK: {e}\", exc_info=True)\n", "        # Depending on the desired behavior, you might want to exit or raise an error here\n", "        # For now, it will allow the script to continue but embedding will fail.\n", "else:\n", "    logger.warning(\"GEMINI_API_KEY not found in environment variables.\")\n"]}, {"cell_type": "code", "execution_count": 28, "id": "c9ecc581", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name '__file__' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[28], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# --- Constants ---\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m SCRIPT_DIR \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mdirname(os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mabspath(\u001b[38;5;18;43m__file__\u001b[39;49m))\n\u001b[0;32m      3\u001b[0m FAISS_INDEX_PATH \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(SCRIPT_DIR, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreport_index_gemini.faiss\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      4\u001b[0m REPORT_DATA_STORE_PATH \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(SCRIPT_DIR, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreport_data_gemini.json\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name '__file__' is not defined"]}], "source": ["# --- Constants ---\n", "SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))\n", "FAISS_INDEX_PATH = os.path.join(SCRIPT_DIR, \"report_index_gemini.faiss\")\n", "REPORT_DATA_STORE_PATH = os.path.join(SCRIPT_DIR, \"report_data_gemini.json\")\n", "EMBEDDING_API_MODEL = \"text-embedding-004\" # Gemini model for embeddings"]}, {"cell_type": "code", "execution_count": null, "id": "f44c0849", "metadata": {}, "outputs": [], "source": ["# --- Helper Function for Embeddings ---\n", "def get_embedding_gemini(text_to_embed):\n", "    \"\"\"Generates an embedding for a given text using the Gemini Embedding API.\"\"\"\n", "    if not text_to_embed or not isinstance(text_to_embed, str) or not text_to_embed.strip():\n", "        logger.warning(\"Empty or invalid text provided for embedding. Returning None.\")\n", "        return None\n", "\n", "    if not GEMINI_API_KEY: # Check if API key was loaded and gena<PERSON> configured\n", "        logger.error(\"GEMINI_API_KEY is missing or SDK not configured. Cannot generate embedding.\")\n", "        return None\n", "    \n", "    try:\n", "        # Ensure genai is configured before calling embed_content\n", "        if not genai.API_KEY: # A more direct check if genai was configured\n", "             genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "        logger.debug(f\"Attempting to get embedding for text (first 100 chars): '{str(text_to_embed)[:100]}...'\")\n", "        result = genai.embed_content(\n", "            model=f\"models/{EMBEDDING_API_MODEL}\",\n", "            content=text_to_embed,\n", "            task_type=\"RETRIEVAL_QUERY\" # Use RETRIEVAL_QUERY for user queries\n", "        )\n", "        \n", "        if \"embedding\" in result:\n", "            logger.debug(f\"Successfully generated embedding for text (first 100 chars): '{str(text_to_embed)[:100]}...'\")\n", "            return np.array(result[\"embedding\"])\n", "        else:\n", "            logger.error(f\"Embedding not found in SDK response for text (first 100 chars): '{str(text_to_embed)[:100]}...'. Response: {result}\")\n", "            return None\n", "    except google_exceptions.GoogleAPIError as e:\n", "        logger.error(f\"Google API Error calling Gemini Embedding API for text (first 100 chars): '{str(text_to_embed)[:100]}...'. Error: {e}\", exc_info=True)\n", "        return None\n", "    except AttributeError: # This can happen if genai was not configured due to missing API key initially\n", "        logger.error(\"Google Generative AI SDK (genai) was not properly configured, likely due to a missing API key. Cannot generate embedding.\")\n", "        return None\n", "    except Exception as e:\n", "        logger.error(f\"An unexpected error occurred in get_embedding_gemini for text '{str(text_to_embed)[:100]}...': {e}\", exc_info=True)\n", "        return None\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f6b3a912", "metadata": {}, "outputs": [], "source": ["new_df = df[['name', 'Metrics Captured']]"]}, {"cell_type": "code", "execution_count": 4, "id": "76a4fd5e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Documents\\flw-nlp-platform\\.venv\\lib\\site-packages\\pandas\\core\\frame.py:4906: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  return super().drop(\n"]}], "source": ["new_df.drop(new_df[new_df['Metrics Captured'] == 'Unavailable'].index, inplace=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "864b3639", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "Metrics Captured", "rawType": "object", "type": "string"}], "ref": "7d919861-d892-4180-a3b1-************", "rows": [["10", "Transactions", "All Relevent transactions fields."], ["12", "Merchant Details", "Active Merchants, Account ID (Unique identifier for each merchant), Country, Email, Merchant Name, Merchants In Review, Merchants Rejected, Phone Number, Risk Rating (High, Medium, Low), Sector (Industry or business sector of the merchant), Sign-Up Date, Total Merchant\n\n\n\n\n\n\n"], ["17", "Wallet Report (Assurance)", "Wallet balance per merchant, per wallet/currency\nCumulative wallet balance per currency\nCumulative wallet balance by product"], ["23", "Merchant Details", "Active Merchants, Account ID (Unique identifier for each merchant), Country, Email, Merchant Name, Merchants In Review, Merchants Rejected, Phone Number, Risk Rating (High, Medium, Low), Sector (Industry or business sector of the merchant), Sign-Up Date, Total Merchant\n\n\n\n\n\n\n"], ["43", "Swap Time Drills", "TPV, TPV, Revenue\n\nTransacting customers\n\nError type"], ["45", "Working Capital Report", "TPV in local currency and USD"], ["55", "Swap Dashboard", "TPV, TPC, Revenue\n\nRevenue\n\nCurrency Corridors\n\nSuccess Rate"], ["57", "Core vs Products Recon", "Transaction details including Flwref, txid, currency, amount, merchant details, provider, etc."], ["66", "Chargeback Report", "Chargeback volume in USD\nChargeback count\nChargeback ratio"], ["72", "HAPPYFOX DASHBOARD", "Average Resolution Time, Closed Tickets (Same Day), Overall Closed Tickets, Pending Tickets, Ticket Closure by Agent, Ticket Inflow by Category, Ticket Source, Total Ticket Count by Agent, Total Tickets Received."], ["75", "HAPPYFOX DASHBOARD", "Average Resolution Time, Closed Tickets (Same Day), Overall Closed Tickets, Pending Tickets, Ticket Closure by Agent, Ticket Inflow by Category, Ticket Source, Total Ticket Count by Agent, Total Tickets Received."], ["76", "BIN Report", "Daily TPC, Daily TPV, Monthly Collections Trend, Spike Percentage (Spike %), Total Number of Merchants (#Merchants), Total Processed Count (TPC), Total Processed Value (TPV)"], ["77", "Payout and Wallet Funding", "$TPV, $TPV, Currency, First Payout View, First payout date, Merchant, MerchantID, SignUp date, TPC, TPC, Txn_Amount"], ["82", "PAN Failed Transactions", "Charge Message: The message or code related to the transaction charge attempt\nFailure Reason: The reason or code indicating why the transaction failed\n%MakeUp: Percentage of failed transactions in relation to all transactions for the merchant\nMerchant: The name of the merchant involved in the failed transaction\nMerchant Level: The level or tier of the merchant based on specific criteria\nPAN: The Primary Account Number (masked) associated with the transaction\nTPC: Transaction Processing Code, used to categorize the transaction\nTotal Failed Txns: The total number of failed transactions for the merchant"], ["205", "All Transactions", "Count of Successful Auth Transactions, Merchants Daily TPC and TPV in the Last 90 Days, TPC (Transaction Count), TPC by Cardscheme, TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View), TPV (Transaction Value in USD)"], ["210", "Provider Cost Report", "1. COS\n2. REV\n3. TPV\n4. TPC"], ["234", "All Transactions", "Count of Successful Auth Transactions, Merchants Daily TPC and TPV in the Last 90 Days, TPC (Transaction Count), TPC by Cardscheme, TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View), TPV (Transaction Value in USD)"], ["240", "PAYOUT TRANSACTION ANALYSIS", "NGN Rave payout analysis, Pay With Bank Transfer (PWBT) funding per Merchant YTD.\n\n\n\n\n\n\n"], ["242", "Chargeback Report", "Chargeback volume in USD\nChargeback count\nChargeback ratio"], ["243", "Compliance KRI", "Actual daily transactions and value (Budgeted collection spike), Budgeted Payout Spike, Daily Auth report values, Daily Auth Spike report, Monthly TPC Spike, Monthly TPV Spike – Collections"], ["245", "Payout Collections Report", "Revenue\nSuccess Rate\nTPC\nTPV"], ["248", "CEO Report", "TPC, TPV, REV, \nActive Merchants/Customers, \nTop Countries"], ["258", "Settlement Spike Monitor", "%Change: %change for the time series, Current date ($TPV), Date, Merchant ID, Merchant Name, Previous date ($TPV), Previous Week Net: Net settlement amount, SignUp date, WoWNet."], ["263", "Refund Dashboard", "#Customers: Number of customers, #Merchants: Number of Merchants, Compliance status, Country, Customer email, Flwref, MID: Merchant ID, Merchant, Refund Amount, Refund Amount ($Refund Amount): The total amount refunded for the selected date range, Refund date, Total Payment Count (TPC)."], ["272", "Enterprise Virtual Cards Report", "Number of cards issued\n\nTPV, TPC, Revenue from issued cards"], ["273", "Disha Report", "Users performance on dish app\n\nRevenue from disha app"], ["278", "F4B Weekly Report", "collections, payout, CAGR, Signup performance for F4B merchants"], ["279", "Send Dashboard", "TPC, TPV, Revenue\n\nSign Up\n\nTransacting Customers \n\nFX Revenue"], ["281", "Wallet Report (Assurance)", "Wallet balance per merchant, per wallet/currency\nCumulative wallet balance per currency\nCumulative wallet balance by product"], ["282", "Partners Performance Report", "Revenue\nSuccess Rate\nTPC\nTPV"], ["284", "Daily TPV Monitor", "TPC, TPV in NGN and all other currencies converted to USD"], ["289", "Net Position Dashboard", "TPV in local currency and USD"], ["293", "F4B Virtual Cards", "DoD%: %change of card count of t day and t-1 day, Merchant, Number cards per merchant & customer, Number of cards, PrevDay: Count of cards for previous day."], ["294", "Consolidated RevOps Report", "TPC, TPV, Revenue,\nDoD, WoW, MoM performance\nRun rate\nTarget achievement"], ["297", "Fraud and chargeback performance report", "$CB Amount, $Sales, CB ratio, Chargeback by acquirer, Chargeback by country and cardscheme, Chargeback by MCC, Chargeback by Merchant, Chargeback by paymenttype, Fraud CB ratio, Fraud Chargeback by MCC, Merchant, Acquirer, Sales count, Threshold definitions.\n\n\n\n\n\n\n"], ["299", "Low Customer Diversity Monitoring Report", "Merchant, Total_tpc: Total TPC for the week in review, $Total_TPV: Total TPV for the week in review, %Contribution of the customer(sender) to the merchant’s transaction."], ["302", "Contact Centre Dashboard", "Average Close Time\r\nAverage first response time\r\nClosure Rate\r\nCSAT% (Customer Satisfaction Percentage)\r\nFCR% (First Contact Resolution)\r\nTicket Volumes (closed and open)"], ["303", "Contact Centre Dashboard", "Average Close Time\r\nAverage first response time\r\nClosure Rate\r\nCSAT% (Customer Satisfaction Percentage)\r\nFCR% (First Contact Resolution)\r\nTicket Volumes (closed and open)"], ["328", "New Onboarding Dashboard", "Track the merchant journey (funnel) from signup to when they carry out their first transaction."], ["329", "Variance dashboard", "LTD (Last Transaction Date)\nTPC\nTPV\nWeek Period (Thursday to Wednesday)"], ["332", "Global Partners Report", "Success Rate\nTPC\nTPV"], ["333", "Dormant Account Monitoring Report", "Compliance Status, Country, Email, MCC (Merchant Category Code), MCC Description, Merchant Name, MID (Merchant ID), Prev_tra_date (Previous Transaction Date), Recent_tx_date (Recent Transaction Date), Sign Up Date."], ["337", "Success Rate Monitor", "Count Rate\nFailure Rate\nRevenue\nSuccess Rate\nTPC (Total Processing Count)\nTPV (Total Processing Value)\nTransacting Merchants\nValue Rate"], ["338", "Send Time Drills", "TPV, TPV, Revenue\n\nTransacting customers\n\nError type\n\nSuccess rate"], ["346", "POS Spike Monitor", "Current Day Volume (Current Day Vol), Daily Spike(%), Previous Day Volume (Prev Day Vol), TPC, Transaction date (tx_date)."], ["347", "Sales Enablement", "Revenue\r\nSuccess Rate\nTime to Basic Value\nTime to Stable Value\nTPC\nTPV"], ["356", "All Transactions", "Count of Successful Auth Transactions, Merchants Daily TPC and TPV in the Last 90 Days, TPC (Transaction Count), TPC by Cardscheme, TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View), TPV (Transaction Value in USD)"], ["360", "Send US Customers Dashboard", "TPV\n\nTPC\n\nRevenue\n\nTransacting Customers"], ["364", "EA Merchants Monitor", "Merchant details\nTransactions summary"], ["365", "Intercom Conversation History", "Closed Date\r\nCreatedAt\r\nDetails\r\nEmail\r\nSubject\r\nTicket ID"]], "shape": {"columns": 2, "rows": 54}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>Metrics Captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Transactions</td>\n", "      <td>All Relevent transactions fields.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Merchant Details</td>\n", "      <td>Active Merchants, Account ID (Unique identifie...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Wallet Report (Assurance)</td>\n", "      <td>Wallet balance per merchant, per wallet/curren...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Merchant Details</td>\n", "      <td>Active Merchants, Account ID (Unique identifie...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>Swap Time Drills</td>\n", "      <td>TPV, TPV, Revenue\\n\\nTransacting customers\\n\\n...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>Working Capital Report</td>\n", "      <td>TPV in local currency and USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>Swap Dashboard</td>\n", "      <td>TPV, TPC, Revenue\\n\\nRevenue\\n\\nCurrency Corri...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>Core vs Products Recon</td>\n", "      <td>Transaction details including Flwref, txid, cu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>Chargeback Report</td>\n", "      <td>Chargeback volume in USD\\nChargeback count\\nCh...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>HAPPYFOX DASHBOARD</td>\n", "      <td>Average Resolution Time, Closed Tickets (Same ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>HAPPYFOX DASHBOARD</td>\n", "      <td>Average Resolution Time, Closed Tickets (Same ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>BIN Report</td>\n", "      <td>Daily TPC, Daily TPV, Monthly Collections Tren...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>Payout and Wallet Funding</td>\n", "      <td>$TPV, $TPV, Currency, First Payout View, First...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>PAN Failed Transactions</td>\n", "      <td>Charge Message: The message or code related to...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>All Transactions</td>\n", "      <td>Count of Successful Auth Transactions, Merchan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>Provider Cost Report</td>\n", "      <td>1. COS\\n2. REV\\n3. TPV\\n4. TPC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>All Transactions</td>\n", "      <td>Count of Successful Auth Transactions, Merchan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>PAYOUT TRANSACTION ANALYSIS</td>\n", "      <td>NGN Rave payout analysis, Pay With Bank Transf...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>Chargeback Report</td>\n", "      <td>Chargeback volume in USD\\nChargeback count\\nCh...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>Compliance KRI</td>\n", "      <td>Actual daily transactions and value (Budgeted ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>245</th>\n", "      <td>Payout Collections Report</td>\n", "      <td>Revenue\\nSuccess Rate\\nTPC\\nTPV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>CEO Report</td>\n", "      <td>TPC, TPV, REV, \\nActive Merchants/Customers, \\...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258</th>\n", "      <td>Settlement Spike Monitor</td>\n", "      <td>%Change: %change for the time series, Current ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>263</th>\n", "      <td>Refund Dashboard</td>\n", "      <td>#Customers: Number of customers, #Merchants: N...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>Enterprise Virtual Cards Report</td>\n", "      <td>Number of cards issued\\n\\nTPV, TPC, Revenue fr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>Disha Report</td>\n", "      <td>Users performance on dish app\\n\\nRevenue from ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>278</th>\n", "      <td>F4B Weekly Report</td>\n", "      <td>collections, payout, CAGR, Signup performance ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>279</th>\n", "      <td>Send Dashboard</td>\n", "      <td>TPC, TPV, Revenue\\n\\nSign Up\\n\\nTransacting Cu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>281</th>\n", "      <td>Wallet Report (Assurance)</td>\n", "      <td>Wallet balance per merchant, per wallet/curren...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>282</th>\n", "      <td>Partners Performance Report</td>\n", "      <td>Revenue\\nSuccess Rate\\nTPC\\nTPV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>284</th>\n", "      <td>Daily TPV Monitor</td>\n", "      <td>TPC, TPV in NGN and all other currencies conve...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>Net Position Dashboard</td>\n", "      <td>TPV in local currency and USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>F4B Virtual Cards</td>\n", "      <td>DoD%: %change of card count of t day and t-1 d...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>294</th>\n", "      <td>Consolidated RevOps Report</td>\n", "      <td>TPC, TPV, Revenue,\\nDoD, WoW, MoM performance\\...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297</th>\n", "      <td>Fraud and chargeback performance report</td>\n", "      <td>$CB Amount, $Sales, CB ratio, Chargeback by ac...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>Low Customer Diversity Monitoring Report</td>\n", "      <td>Merchant, Total_tpc: Total TPC for the week in...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>Contact Centre Dashboard</td>\n", "      <td>Average Close Time\\r\\nAverage first response t...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>Contact Centre Dashboard</td>\n", "      <td>Average Close Time\\r\\nAverage first response t...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>328</th>\n", "      <td>New Onboarding Dashboard</td>\n", "      <td>Track the merchant journey (funnel) from signu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>329</th>\n", "      <td>Variance dashboard</td>\n", "      <td>LTD (Last Transaction Date)\\nTPC\\nTPV\\nWeek Pe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>Global Partners Report</td>\n", "      <td>Success Rate\\nTPC\\nTPV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>Dormant Account Monitoring Report</td>\n", "      <td>Compliance Status, Country, Email, MCC (Mercha...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>337</th>\n", "      <td>Success Rate Monitor</td>\n", "      <td>Count Rate\\nFailure Rate\\nRevenue\\nSuccess Rat...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>338</th>\n", "      <td>Send Time Drills</td>\n", "      <td>TPV, TPV, Revenue\\n\\nTransacting customers\\n\\n...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>346</th>\n", "      <td>POS Spike Monitor</td>\n", "      <td>Current Day Volume (Current Day Vol), Daily Sp...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>347</th>\n", "      <td>Sales Enablement</td>\n", "      <td>Revenue\\r\\nSuccess Rate\\nTime to Basic Value\\n...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>356</th>\n", "      <td>All Transactions</td>\n", "      <td>Count of Successful Auth Transactions, Merchan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>360</th>\n", "      <td>Send US Customers Dashboard</td>\n", "      <td>TPV\\n\\nTPC\\n\\nRevenue\\n\\nTransacting Customers</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>EA Merchants Monitor</td>\n", "      <td>Merchant details\\nTransactions summary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>365</th>\n", "      <td>Intercom Conversation History</td>\n", "      <td>Closed Date\\r\\nCreatedAt\\r\\nDetails\\r\\nEmail\\r...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>366</th>\n", "      <td>Credit Risk Underwriting Dashboard</td>\n", "      <td>Chargeback details, Merchant chargeback summar...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>372</th>\n", "      <td>Seller Risk</td>\n", "      <td>CB Count - The count of chargebacks\\r\\nCB Valu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>378</th>\n", "      <td>Working Capital Report</td>\n", "      <td>TPV in local currency and USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>389</th>\n", "      <td>Send Dashboard</td>\n", "      <td>TPC, TPV, Revenue\\n\\nSign Up\\n\\nTransacting Cu...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                         name  \\\n", "10                               Transactions   \n", "12                           Merchant Details   \n", "17                  Wallet Report (Assurance)   \n", "23                           Merchant Details   \n", "43                           Swap Time Drills   \n", "45                     Working Capital Report   \n", "55                             Swap Dashboard   \n", "57                     Core vs Products Recon   \n", "66                          Chargeback Report   \n", "72                         HAPPYFOX DASHBOARD   \n", "75                         HAPPYFOX DASHBOARD   \n", "76                                 BIN Report   \n", "77                  Payout and Wallet Funding   \n", "82                    PAN Failed Transactions   \n", "205                          All Transactions   \n", "210                      Provider Cost Report   \n", "234                          All Transactions   \n", "240               PAYOUT TRANSACTION ANALYSIS   \n", "242                         Chargeback Report   \n", "243                            Compliance KRI   \n", "245                 Payout Collections Report   \n", "248                                CEO Report   \n", "258                  Settlement Spike Monitor   \n", "263                          Refund Dashboard   \n", "272           Enterprise Virtual Cards Report   \n", "273                              Disha Report   \n", "278                         F4B Weekly Report   \n", "279                            Send Dashboard   \n", "281                 Wallet Report (Assurance)   \n", "282               Partners Performance Report   \n", "284                         Daily TPV Monitor   \n", "289                    Net Position Dashboard   \n", "293                         F4B Virtual Cards   \n", "294                Consolidated RevOps Report   \n", "297   Fraud and chargeback performance report   \n", "299  Low Customer Diversity Monitoring Report   \n", "302                  Contact Centre Dashboard   \n", "303                  Contact Centre Dashboard   \n", "328                  New Onboarding Dashboard   \n", "329                        Variance dashboard   \n", "332                    Global Partners Report   \n", "333         Dormant Account Monitoring Report   \n", "337                      Success Rate Monitor   \n", "338                          Send Time Drills   \n", "346                         POS Spike Monitor   \n", "347                          Sales Enablement   \n", "356                          All Transactions   \n", "360               Send US Customers Dashboard   \n", "364                      EA Merchants Monitor   \n", "365             Intercom Conversation History   \n", "366        Credit Risk Underwriting Dashboard   \n", "372                               Seller Risk   \n", "378                    Working Capital Report   \n", "389                            Send Dashboard   \n", "\n", "                                      Metrics Captured  \n", "10                   All Relevent transactions fields.  \n", "12   Active Merchants, Account ID (Unique identifie...  \n", "17   Wallet balance per merchant, per wallet/curren...  \n", "23   Active Merchants, Account ID (Unique identifie...  \n", "43   TPV, TPV, Revenue\\n\\nTransacting customers\\n\\n...  \n", "45                       TPV in local currency and USD  \n", "55   TPV, TPC, Revenue\\n\\nRevenue\\n\\nCurrency Corri...  \n", "57   Transaction details including Flwref, txid, cu...  \n", "66   Chargeback volume in USD\\nChargeback count\\nCh...  \n", "72   Average Resolution Time, Closed Tickets (Same ...  \n", "75   Average Resolution Time, Closed Tickets (Same ...  \n", "76   Daily TPC, Daily TPV, Monthly Collections Tren...  \n", "77   $TPV, $TPV, Currency, First Payout View, First...  \n", "82   Charge Message: The message or code related to...  \n", "205  Count of Successful Auth Transactions, Merchan...  \n", "210                     1. COS\\n2. REV\\n3. TPV\\n4. TPC  \n", "234  Count of Successful Auth Transactions, Merchan...  \n", "240  NGN Rave payout analysis, Pay With Bank Transf...  \n", "242  Chargeback volume in USD\\nChargeback count\\nCh...  \n", "243  Actual daily transactions and value (Budgeted ...  \n", "245                    Revenue\\nSuccess Rate\\nTPC\\nTPV  \n", "248  TPC, TPV, REV, \\nActive Merchants/Customers, \\...  \n", "258  %Change: %change for the time series, Current ...  \n", "263  #Customers: Number of customers, #Merchants: N...  \n", "272  Number of cards issued\\n\\nTPV, TPC, Revenue fr...  \n", "273  Users performance on dish app\\n\\nRevenue from ...  \n", "278  collections, payout, CAGR, Signup performance ...  \n", "279  TPC, TPV, Revenue\\n\\nSign Up\\n\\nTransacting Cu...  \n", "281  Wallet balance per merchant, per wallet/curren...  \n", "282                    Revenue\\nSuccess Rate\\nTPC\\nTPV  \n", "284  TPC, TPV in NGN and all other currencies conve...  \n", "289                      TPV in local currency and USD  \n", "293  DoD%: %change of card count of t day and t-1 d...  \n", "294  TPC, TPV, Revenue,\\nDoD, WoW, MoM performance\\...  \n", "297  $CB Amount, $Sales, CB ratio, Chargeback by ac...  \n", "299  Merchant, Total_tpc: Total TPC for the week in...  \n", "302  Average Close Time\\r\\nAverage first response t...  \n", "303  Average Close Time\\r\\nAverage first response t...  \n", "328  Track the merchant journey (funnel) from signu...  \n", "329  LTD (Last Transaction Date)\\nTPC\\nTPV\\nWeek Pe...  \n", "332                             Success Rate\\nTPC\\nTPV  \n", "333  Compliance Status, Country, Email, MCC (Mercha...  \n", "337  Count Rate\\nFailure Rate\\nRevenue\\nSuccess Rat...  \n", "338  TPV, TPV, Revenue\\n\\nTransacting customers\\n\\n...  \n", "346  Current Day Volume (Current Day Vol), Daily Sp...  \n", "347  Revenue\\r\\nSuccess Rate\\nTime to Basic Value\\n...  \n", "356  Count of Successful Auth Transactions, Merchan...  \n", "360     TPV\\n\\nTPC\\n\\nRevenue\\n\\nTransacting Customers  \n", "364             Merchant details\\nTransactions summary  \n", "365  Closed Date\\r\\nCreatedAt\\r\\nDetails\\r\\nEmail\\r...  \n", "366  Chargeback details, Merchant chargeback summar...  \n", "372  CB Count - The count of chargebacks\\r\\nCB Valu...  \n", "378                      TPV in local currency and USD  \n", "389  TPC, TPV, Revenue\\n\\nSign Up\\n\\nTransacting Cu...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["new_df"]}, {"cell_type": "code", "execution_count": 6, "id": "19e726f8", "metadata": {}, "outputs": [], "source": ["new_df_top_20 = new_df.head(20)"]}, {"cell_type": "code", "execution_count": 10, "id": "7fbaba11", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os"]}, {"cell_type": "code", "execution_count": 12, "id": "207eb312", "metadata": {}, "outputs": [{"data": {"text/plain": ["'This dataset appears to be a collection of business intelligence reports, likely from a financial technology (fintech) company.  Each row represents a different report or data source, detailing various metrics related to transactions, merchants, and system performance.\\n\\nThe unique metrics captured across the top 20 entries (rows 10, 12, 17, 23, 43, 45, 55, 57, 66, 72, 75, 76, 77, 82, 205, 210, 234, 240, 242, 243) include:\\n\\n* All Relevent transactions fields\\n* Active Merchants\\n* Account ID\\n* Country\\n* Email\\n* Merchant Name\\n* Merchants In Review\\n* Merchants Rejected\\n* Phone Number\\n* Risk Rating\\n* Sector\\n* Sign-Up Date\\n* Total Merchant\\n* Wallet balance per merchant, per wallet/currency\\n* Cumulative wallet balance per currency\\n* Cumulative wallet balance by product\\n* TPV\\n* Revenue\\n* Transacting customers\\n* Error type\\n* TPV in local currency and USD\\n* TPC\\n* Currency Corridors\\n* Success Rate\\n* Transaction details (Flwref, txid, currency, amount, merchant details, provider, etc.)\\n* Chargeback volume in USD\\n* Chargeback count\\n* Chargeback ratio\\n* Average Resolution Time\\n* Closed Tickets (Same Day)\\n* Overall Closed Tickets\\n* Pending Tickets\\n* Ticket Closure by Agent\\n* Ticket Inflow by Category\\n* Ticket Source\\n* Total Ticket Count by Agent\\n* Total Tickets Received\\n* Daily TPC\\n* Daily TPV\\n* Monthly Collections Trend\\n* Spike Percentage\\n* Total Number of Merchants\\n* Total Processed Count (TPC)\\n* Total Processed Value (TPV)\\n* $TPV\\n* Currency\\n* First Payout View\\n* First payout date\\n* MerchantID\\n* SignUp date\\n* Txn_Amount\\n* Charge Message\\n* Failure Reason\\n* %MakeUp\\n* Merchant Level\\n* PAN\\n* Transaction Processing Code (TPC)\\n* Total Failed Txns\\n* Count of Successful Auth Transactions\\n* Merchants Daily TPC and TPV in the Last 90 Days\\n* TPC by Cardscheme\\n* TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View)\\n* COS\\n* REV\\n* NGN Rave payout analysis\\n* Pay With Bank Transfer (PWBT) funding per Merchant YTD\\n* Actual daily transactions and value (Budgeted collection spike)\\n* Budgeted Payout Spike\\n* Daily Auth report values\\n* Daily Auth Spike report\\n* Monthly TPC Spike\\n* Monthly TPV Spike – Collections\\n\\n\\nNote that some metrics appear multiple times under different report names, suggesting potential redundancy or different granularities of the same underlying data.  Also, \"All relevant transactions fields\" and \"Transaction details including...\" are too general to be specific metrics.\\n'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# prompt: use genai to generate a new description and metrics captured from new_df_top_20\n", "\n", "import google.generativeai as genai\n", "\n", "load_dotenv()\n", "GEMINI_API_KEY = os.getenv(\"GEMINI_API_KEY\")\n", "genai.configure(api_key=GEMINI_API_KEY)\n", "\n", "# Create the model\n", "# See https://ai.google.dev/api/python/google/generativeai/GenerativeModel\n", "generation_config = {\n", "  \"temperature\": 0.9,\n", "  \"top_p\": 1,\n", "  \"top_k\": 1,\n", "  \"max_output_tokens\": 2048,\n", "}\n", "\n", "\n", "model = genai.GenerativeModel(model_name=\"gemini-1.5-flash-latest\",\n", "                              generation_config=generation_config)\n", "\n", "prompt_parts = [\n", "  f\"\"\"Given the following pandas DataFrame, where the 'name' column represents\n", "  different entities and the 'Metrics Captured' column lists the metrics\n", "  associated with each entity, provide a brief description of the type of data\n", "  present and list the unique metrics captured across the top 20 entries.\n", "\n", "  DataFrame:\n", "  {new_df_top_20.to_string()}\n", "  \"\"\",\n", "]\n", "\n", "response = model.generate_content(prompt_parts)\n", "response.text"]}, {"cell_type": "code", "execution_count": 13, "id": "8d0651e6", "metadata": {}, "outputs": [], "source": ["def generate_description(row):\n", "  \"\"\"Generates a detailed description based on 'name' and 'Metrics Captured'.\"\"\"\n", "  prompt = f\"Generate a detailed description for a report based on its name '{row['name']}' and the following metrics captured: '{row['Metrics Captured']}'. Focus on what the report likely contains and its purpose given the name and metrics. Make it sound like a professional description.\"\n", "  # Use a valid model name based on the list of available models.\n", "  # As an example, using a commonly available model like 'gemini-1.5-flash-latest'\n", "  model = genai.GenerativeModel('gemini-1.5-flash-latest')\n", "  # Use stream=True to stream the output\n", "  response = model.generate_content(prompt, stream=True)\n", "  # Iterate through the streamed response and print it\n", "  description = \"\"\n", "  for chunk in response:\n", "    description += chunk.text\n", "    # Optional: Add a small delay to see the streaming effect\n", "    # time.sleep(0.1)\n", "  return description"]}, {"cell_type": "code", "execution_count": 14, "id": "2fcdb616", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28552\\4117105450.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  new_df_top_20['description'] = new_df_top_20.apply(generate_description, axis=1)\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "Metrics Captured", "rawType": "object", "type": "string"}, {"name": "description", "rawType": "object", "type": "string"}], "ref": "71bc858d-17f1-437c-9959-8fd6c1d0d332", "rows": [["10", "Transactions", "All Relevent transactions fields.", "The \"Transactions\" report provides a comprehensive overview of all relevant transactional activity within the specified timeframe.  Leveraging the capture of \"All Relevant Transaction Fields,\" this report offers a granular level of detail for each transaction, enabling in-depth analysis and robust decision-making.  The report likely includes, but is not limited to, the following information: transaction date and time, unique transaction ID, transaction type (e.g., sale, refund, adjustment), customer identification details (potentially anonymized or aggregated depending on privacy considerations), product or service details including quantity and price, payment method employed, associated fees or discounts, transaction status (e.g., completed, pending, failed), and any relevant notes or internal comments.\n\nThe purpose of this report is multifaceted.  It serves as a critical tool for:\n\n* **Financial Reporting and Reconciliation:** Accurate tracking of all financial transactions allows for precise financial reporting, reconciliation with accounting systems, and detection of discrepancies.\n* **Performance Monitoring:** Analysis of transaction data enables the identification of sales trends, peak periods, popular products or services, and potential areas for improvement in operational efficiency.\n* **Fraud Detection and Prevention:** By scrutinizing transaction details, anomalies and potentially fraudulent activities can be identified and investigated, leading to improved security measures.\n* **Customer Relationship Management (CRM):**  Understanding customer purchasing behavior through transaction data allows for targeted marketing campaigns, improved customer service, and personalized experiences.\n* **Inventory Management:** Tracking product movement through transactions allows for effective inventory control, forecasting demand, and minimizing stockouts or overstocking.\n\nThe \"Transactions\" report’s adaptability to various filtering and sorting options (likely included) further enhances its utility, enabling users to focus on specific aspects of transactional activity as needed.  The report is expected to be a key resource for financial analysts, business managers, and customer service teams seeking a detailed understanding of the organization's transactional landscape.\n"], ["12", "Merchant Details", "Active Merchants, Account ID (Unique identifier for each merchant), Country, Email, Merchant Name, Merchants In Review, Merchants Rejected, Phone Number, Risk Rating (High, Medium, Low), Sector (Industry or business sector of the merchant), Sign-Up Date, Total Merchant\n\n\n\n\n\n\n", "## Merchant Details Report: A Comprehensive Overview of Active and Prospective Merchants\n\nThe \"Merchant Details\" report provides a comprehensive and granular view of all merchants within the system, encompassing both active and those currently under review or rejected.  This report serves as a crucial tool for monitoring merchant health, identifying potential risks, and understanding the overall composition of the merchant base.\n\n**Report Content and Structure:**\n\nThe report meticulously details each merchant using the following key metrics:\n\n* **Active Merchants:** A summary count of currently active and approved merchants.\n* **Account ID:** A unique, numerical identifier assigned to each merchant for easy tracking and referencing.\n* **Country:** The country of origin or primary operation for each merchant.\n* **Email:** The primary contact email address associated with the merchant account.\n* **Merchant Name:** The official name under which the merchant operates.\n* **Merchants In Review:** A count and detailed list of merchants currently undergoing the onboarding and verification process.\n* **Merchants Rejected:** A count and detailed list of merchants whose applications have been denied, along with reasons for rejection (where applicable).\n* **Phone Number:** The primary contact phone number associated with the merchant account.\n* **Risk Rating:** A categorical assessment (High, Medium, Low) reflecting the perceived level of risk associated with each merchant based on various factors, potentially including financial stability, compliance history, and transactional patterns.\n* **Sector:** The industry or business sector in which each merchant operates, enabling segmentation and analysis by industry verticals.\n* **Sign-Up Date:** The date on which each merchant initially registered or applied for an account.\n* **Total Merchants:** A total count of all merchants, including active, in review, and rejected merchants.\n\n\n**Report Purpose and Use Cases:**\n\nThis report facilitates several crucial business functions:\n\n* **Risk Management:** Identify high-risk merchants and proactively mitigate potential losses.\n* **Onboarding Efficiency:** Track the progress of merchant applications and identify bottlenecks in the onboarding process.\n* **Business Intelligence:** Analyze merchant demographics, sector distribution, and growth trends to inform strategic decision-making.\n* **Compliance and Regulatory Reporting:** Provide necessary data for compliance audits and regulatory reporting.\n* **Customer Support:** Quickly access comprehensive merchant details to efficiently resolve customer inquiries or disputes.\n\nThe \"Merchant Details\" report is designed for accessibility and clarity, presenting data in a structured format suitable for both operational monitoring and strategic analysis.  Its comprehensive nature allows for a deep understanding of the merchant ecosystem, ultimately contributing to enhanced risk management, improved operational efficiency, and informed business strategies.\n"], ["17", "Wallet Report (Assurance)", "Wallet balance per merchant, per wallet/currency\nCumulative wallet balance per currency\nCumulative wallet balance by product", "## Wallet Report (Assurance): A Detailed Description\n\nThe Wallet Report (Assurance) provides a comprehensive overview of the financial health and security of the wallet system, focusing on assurance of fund integrity and accuracy.  The report meticulously analyzes wallet balances across various dimensions, offering key insights into the distribution of funds and potential discrepancies.\n\nThe report's core functionality centers around three key metrics:\n\n**1. Wallet Balance per Merchant, per Wallet/Currency:** This section provides a granular view of the funds held within each individual wallet, broken down by both the merchant associated with the wallet and the specific currency used.  This level of detail allows for precise tracking of funds at the most fundamental level, enabling rapid identification of any anomalies or inconsistencies.  The data includes the current balance for each wallet, facilitating immediate assessment of merchant-specific financial positions.\n\n**2. Cumulative Wallet Balance per Currency:** This metric aggregates wallet balances across all merchants, providing a holistic view of the total funds held in the system for each currency. This consolidated view is crucial for understanding the overall liquidity position within each currency, informing strategic financial planning and risk management decisions.  The cumulative balance helps to identify potential currency-specific risks or opportunities.\n\n**3. Cumulative Wallet Balance by Product:** This section further aggregates wallet balances, this time grouping them based on the product associated with the wallet. This provides insight into the financial performance and usage patterns of different products offered within the system. This analysis helps identify top-performing products and areas where optimization may be needed, informing product development strategies and resource allocation.\n\n**Purpose and Use Cases:**\n\nThe primary purpose of the Wallet Report (Assurance) is to provide a robust and reliable mechanism for auditing and verifying the accuracy and security of the entire wallet system.  Its granular data allows for:\n\n* **Fraud Detection:**  Early identification of unusual wallet activity or discrepancies.\n* **Reconciliation:**  Matching internal records with actual wallet balances to ensure data integrity.\n* **Financial Reporting:**  Providing accurate financial information for regulatory compliance and internal reporting.\n* **Risk Management:**  Identifying potential vulnerabilities and implementing preventative measures.\n* **Product Performance Analysis:**  Evaluating the financial success of different products and services.\n* **Capacity Planning:**  Forecasting future resource needs based on observed trends.\n\nIn conclusion, the Wallet Report (Assurance) is a critical tool for maintaining the integrity and security of the wallet system.  Its comprehensive data and detailed analysis empower stakeholders to make informed decisions, mitigate risks, and ensure the long-term financial health of the system.\n"], ["23", "Merchant Details", "Active Merchants, Account ID (Unique identifier for each merchant), Country, Email, Merchant Name, Merchants In Review, Merchants Rejected, Phone Number, Risk Rating (High, Medium, Low), Sector (Industry or business sector of the merchant), Sign-Up Date, Total Merchant\n\n\n\n\n\n\n", "## Merchant Details Report: A Comprehensive Overview of Active and Prospective Merchants\n\nThe \"Merchant Details\" report provides a comprehensive and granular view of all merchants within the system, encompassing both active and those currently under review or rejected.  This report serves as a crucial tool for monitoring merchant activity, assessing risk, and informing strategic business decisions.\n\n**Report Content:** The report details individual merchant attributes, offering a detailed profile for each entry.  Key data points include:\n\n* **Active Merchants:** A count of currently active and operational merchants.\n* **Account ID:** A unique, alphanumeric identifier assigned to each merchant for unambiguous identification across all systems.\n* **Country:** The country of operation or registration for each merchant.\n* **Email:** The primary email address associated with the merchant account.\n* **Merchant Name:** The official legal or trading name of the merchant.\n* **Merchants In Review:** A count of merchants currently undergoing the onboarding or vetting process. This allows for real-time monitoring of the application pipeline.\n* **Merchants Rejected:** A count of merchants whose applications have been rejected, along with the potential for drill-down to understand rejection reasons (this would require a secondary report or detailed view).\n* **Phone Number:** The primary contact phone number for each merchant.\n* **Risk Rating:** A categorical risk assessment (High, Medium, Low) assigned to each merchant based on internal risk scoring models.  This is critical for prioritizing resources and mitigating potential financial or reputational risks.\n* **Sector:** The industry or business sector to which the merchant belongs, facilitating segmentation analysis and targeted initiatives.\n* **Sign-Up Date:** The date and time the merchant account was created.  This allows for cohort analysis and tracking of merchant lifecycle metrics.\n* **Total Merchants:**  The overall count of all merchants registered in the system, irrespective of their current status (active, in review, or rejected).\n\n**Report Purpose & Use Cases:** This report is invaluable for various stakeholders, facilitating:\n\n* **Risk Management:**  Identification of high-risk merchants, enabling proactive mitigation strategies.\n* **Business Intelligence:**  Analysis of merchant demographics, industry distribution, and growth trends.\n* **Onboarding Efficiency:** Tracking the progress of merchant applications and identifying bottlenecks in the onboarding process.\n* **Compliance and Regulatory Reporting:** Providing necessary data for compliance audits and regulatory reporting requirements.\n* **Customer Support:** Quick access to comprehensive merchant details for efficient resolution of issues and inquiries.\n\n\nThe \"Merchant Details\" report is designed for flexibility, allowing users to filter and sort data based on various parameters, providing a tailored view for specific analytical needs.  This ensures relevant information is readily accessible to support data-driven decision-making across the organization.\n"], ["43", "Swap Time Drills", "TPV, TPV, Revenue\n\nTransacting customers\n\nError type", "**Report Description: Swap Time Drills**\n\nThis report analyzes the efficiency and error rates associated with \"swap time\" operations, likely within a trading or transactional system.  The core objective is to identify bottlenecks and error sources impacting the speed and profitability of these operations.  The report leverages three key performance indicators (KPIs) and a qualitative metric to achieve this goal:\n\n* **TPV (Transaction Processing Volume):**  The report utilizes two instances of TPV.  The first likely represents the overall TPV during the observed period, providing a baseline volume of transactions.  The second instance of TPV likely represents the volume of transactions specifically completed within the \"swap time\" window, highlighting the throughput achievable during this critical timeframe.  Comparing these two TPV metrics helps assess the proportion of transactions successfully processed within the designated swap time.\n\n* **Revenue:** This metric quantifies the financial impact of the swap time operations. Analyzing revenue alongside TPV allows for the calculation of revenue per transaction and the identification of any correlation between transaction speed/volume and revenue generation.  Significant deviations may indicate areas for optimization or potential issues influencing profitability.\n\n* **Transacting Customers:** This metric tracks the number of unique customers involved in swap time transactions.  Analyzing this alongside TPV helps determine the average number of transactions per customer, potentially revealing patterns in customer behavior and transaction frequency.\n\n* **Error Type:** This qualitative metric categorizes the different types of errors encountered during swap time operations.  Detailed error categorization allows for pinpointing the root causes of delays and failures, such as system glitches, procedural errors, or data inconsistencies.  This allows for targeted improvement initiatives and resource allocation.\n\n**Report Purpose:**\n\nThe Swap Time Drills report serves to:\n\n* **Benchmark Swap Time Efficiency:**  Assess the effectiveness of current processes in executing swap time transactions, identifying areas for improvement in speed and volume.\n* **Identify Profitability Drivers:** Determine the relationship between transaction speed, volume, and revenue generation, highlighting areas of greatest impact on profitability.\n* **Analyze Error Trends:**  Uncover patterns in error types and their frequencies, enabling the proactive mitigation of future errors and system failures.\n* **Inform Process Optimization:**  Provide data-driven insights to inform changes to processes, systems, or training to enhance overall efficiency and reduce error rates.\n* **Improve Customer Experience:**  Indirectly improve the customer experience by minimizing delays and ensuring smoother, more reliable transactions.\n\nIn summary, this report is a crucial tool for identifying and resolving bottlenecks within swap time operations, ultimately leading to improved operational efficiency, increased profitability, and enhanced customer satisfaction.\n"], ["45", "Working Capital Report", "TPV in local currency and USD", "The Working Capital Report provides a comprehensive overview of the company's short-term liquidity and operational efficiency, focusing on the relationship between current assets and current liabilities.  A key element of this report is its analysis of the Total Processing Value (TPV), presented in both the local currency and USD, offering a multifaceted view of financial performance.\n\nThe report will detail the TPV trends over a specified period (e.g., monthly, quarterly, annually), highlighting growth or decline and any significant fluctuations.  This analysis will consider seasonality and other relevant external factors impacting TPV.  The conversion to USD allows for easier international comparison and assessment of foreign exchange rate impacts on the overall financial health.\n\nBeyond the raw TPV figures, the report will likely delve into the following aspects impacting working capital:\n\n* **Receivables Management:**  Analysis of outstanding invoices, days sales outstanding (DSO), and the effectiveness of collections strategies, relating these metrics to TPV fluctuations.\n* **Inventory Management:**  Examination of inventory levels, days inventory outstanding (DIO), and potential issues like obsolescence or excess stock, linked to the generated TPV.  This section may also analyze the relationship between inventory turnover and TPV.\n* **Payables Management:**  Assessment of outstanding payments to suppliers, days payable outstanding (DPO), and strategies for optimizing payment terms, influencing working capital requirements in relation to TPV.\n* **Cash Flow Projections:**  Based on the TPV analysis and working capital components, the report will likely include projected cash flows, highlighting potential shortfalls or surpluses and recommending necessary actions.\n* **Working Capital Ratios:**  Key ratios such as the current ratio, quick ratio, and cash ratio will be calculated and analyzed, offering a concise summary of the company's short-term liquidity position in relation to its TPV performance.\n* **Key Performance Indicators (KPIs):**  The report will present relevant KPIs, clearly demonstrating the efficiency of working capital management and its impact on the overall business profitability as measured by the TPV.\n\nThe ultimate purpose of the Working Capital Report is to provide management with actionable insights into the company's liquidity position, enabling informed decision-making regarding financing needs, operational improvements, and strategies to optimize working capital efficiency and maximize profitability as reflected in the TPV data.  This report serves as a crucial tool for financial planning, forecasting, and risk management.\n"], ["55", "Swap Dashboard", "TPV, TPC, Revenue\n\nRevenue\n\nCurrency Corridors\n\nSuccess Rate", "The Swap Dashboard report provides a comprehensive overview of the performance and health of swap trading activities.  It leverages key performance indicators (KPIs) to offer actionable insights into trading efficiency, profitability, and operational success. The report focuses on providing a clear and concise visualization of daily/period trading performance across various currency corridors.\n\n**Key Metrics and Their Interpretations:**\n\n* **Total Processing Volume (TPV):** This metric indicates the total value of swaps processed within the specified reporting period.  A high TPV suggests strong trading activity, while a low TPV may indicate subdued market conditions or operational issues.  The report will likely display TPV trends over time, allowing for the identification of growth patterns or potential downturns.\n\n* **Total Processing Count (TPC):**  The TPC metric represents the total number of swap transactions processed during the reporting period. This complements TPV by providing context regarding the frequency of trades, independent of their individual value.  A high TPC with a relatively low TPV might suggest a higher volume of smaller transactions.\n\n* **Revenue:** This is a crucial metric reflecting the total revenue generated from swap trading activities. The report will break down revenue by currency corridor, allowing for the identification of high-performing and underperforming areas.  Analysis of revenue trends in relation to TPV and TPC will help determine pricing effectiveness and trading profitability.\n\n* **Currency Corridors:** The report will segment data by currency pairs (e.g., EUR/USD, USD/JPY), providing a detailed performance analysis for each corridor. This breakdown allows for the identification of profitable and less profitable trading pairs, enabling strategic resource allocation and risk management.\n\n* **Success Rate:** This metric captures the percentage of swap transactions successfully completed without errors or exceptions.  A low success rate highlights potential operational bottlenecks or system issues requiring immediate attention.  The report will likely identify the root causes of failures to facilitate process improvements.\n\n**Report Purpose and Usage:**\n\nThe Swap Dashboard report serves as a critical tool for monitoring and optimizing swap trading operations.  It empowers stakeholders, including traders, management, and risk management teams, to:\n\n* **Track Key Performance:** Gain real-time visibility into trading activity and profitability.\n* **Identify Profitable Opportunities:** Analyze performance across currency corridors to identify high-yield opportunities and potential areas for growth.\n* **Detect and Resolve Operational Issues:** Monitor success rates to quickly pinpoint and address operational bottlenecks, minimizing trading disruptions and maximizing efficiency.\n* **Inform Strategic Decision Making:** Leverage data-driven insights to inform pricing strategies, resource allocation, and risk management practices.\n* **Enhance Regulatory Compliance:**  Provide a comprehensive audit trail of trading activity for regulatory reporting purposes.\n\n\nIn summary, the Swap Dashboard report provides a holistic view of swap trading performance, facilitating informed decision-making and contributing to overall operational efficiency and profitability.\n"], ["57", "Core vs Products Recon", "Transaction details including Flwref, txid, currency, amount, merchant details, provider, etc.", "## Report Description: Core vs Products Recon\n\nThis report, titled \"Core vs Products Recon,\" provides a detailed reconciliation of financial transactions processed through the core system against those recorded within individual product systems.  Its primary purpose is to identify discrepancies and ensure data integrity between these two crucial data sources.  The reconciliation process meticulously compares transaction records, highlighting any differences that may indicate errors, inconsistencies, or potential fraud.\n\nThe report leverages comprehensive transaction details to facilitate this comparison.  For each transaction, the following key metrics are included:\n\n* **Flwref:** A unique internal flow reference number, providing a consistent identifier across systems.\n* **txid:** The transaction ID from the originating system (either core or product). This allows for precise tracing of individual transactions.\n* **Currency:** The currency of the transaction.\n* **Amount:** The transaction value in the respective currency.\n* **Merchant Details:**  Comprehensive merchant information, including name, ID, and potentially location, to facilitate accurate identification and tracking of payments.\n* **Provider:** The payment provider involved in the transaction (e.g., Visa, Mastercard, PayPal).\n\n\nBy comparing these metrics across the core and product systems, the report effectively pinpoints transactions present in one system but missing in the other (i.e., missing transactions), transactions with differing amounts, or transactions with mismatched details. This allows for efficient detection of:\n\n* **Data Entry Errors:** Identifying inconsistencies arising from manual data entry processes in either the core or product systems.\n* **System Glitches:** Highlighting potential bugs or malfunctions within either system impacting transaction recording.\n* **Fraudulent Activity:** Flagging potentially fraudulent transactions through discrepancies in amounts or details.\n* **Reconciliation Issues:** Identifying and quantifying the overall reconciliation discrepancies between core and product systems.\n\n\nThe report is designed to be a critical tool for financial auditing, system maintenance, and fraud prevention.  It empowers finance and operations teams to proactively identify and resolve data discrepancies, ensuring the accuracy and reliability of financial reporting and maintaining trust in the integrity of the payment systems.  The detailed transaction information enables swift investigation and remediation of any identified issues.\n"], ["66", "Chargeback Report", "Chargeback volume in USD\nChargeback count\nChargeback ratio", "The Chargeback Report provides a comprehensive analysis of chargeback activity, offering crucial insights into the financial impact and operational efficiency of payment processing.  The report utilizes three key performance indicators (KPIs) to deliver a clear and concise overview:\n\n* **Chargeback Volume (USD):** This metric quantifies the total monetary value lost due to chargebacks within a specified period (e.g., daily, weekly, monthly, quarterly).  It provides a direct measure of the financial burden imposed by chargebacks on the business.\n\n* **Chargeback Count:** This metric represents the total number of chargebacks received during the selected timeframe.  While the USD volume reflects the financial impact, the count provides context regarding the frequency of chargeback incidents, potentially indicating underlying issues with transaction processing or customer satisfaction.\n\n* **Chargeback Ratio:** This crucial KPI expresses the proportion of chargebacks relative to the total number of transactions processed.  Calculated as (Chargeback Count / Total Transaction Count) * 100%, the chargeback ratio provides a normalized representation of chargeback frequency, enabling comparisons across different periods and facilitating trend analysis. A lower ratio indicates improved performance in minimizing chargebacks.\n\nThe report's purpose is to identify trends, pinpoint potential areas of vulnerability, and inform strategies to mitigate future chargeback occurrences.  It serves as a valuable tool for:\n\n* **Financial Planning & Forecasting:** Accurately predicting and budgeting for chargeback losses.\n* **Risk Management:** Identifying high-risk transaction types or customer segments contributing disproportionately to chargeback volume.\n* **Operational Improvement:**  Pinpointing weaknesses in transaction processing, fraud prevention measures, or customer service that contribute to chargebacks.\n* **Compliance & Regulatory Reporting:** Ensuring adherence to industry regulations and providing necessary data for audits.\n\nThe Chargeback Report typically includes visualizations such as charts and graphs illustrating trends in chargeback volume and ratio over time, allowing for a quick and intuitive understanding of the data.  Detailed breakdowns by various attributes (e.g., transaction type, reason code, customer segment) may also be included to provide a granular view of chargeback causes and facilitate targeted interventions.  Ultimately, this report empowers businesses to proactively manage chargeback risk, reduce financial losses, and improve overall operational efficiency.\n"], ["72", "HAPPYFOX DASHBOARD", "Average Resolution Time, Closed Tickets (Same Day), Overall Closed Tickets, Pending Tickets, Ticket Closure by Agent, Ticket Inflow by Category, Ticket Source, Total Ticket Count by Agent, Total Tickets Received.", "## HAPPYFOX DASHBOARD Report: A Comprehensive Overview of Support Performance\n\nThe HAPPYFOX DASHBOARD report provides a concise yet comprehensive overview of key performance indicators (KPIs) related to ticket management and agent productivity within the HAPPYFOX support system.  Designed for management and support team leaders, this report offers actionable insights into service efficiency and agent performance, facilitating data-driven decision-making and continuous improvement.\n\nThe report's core function is to present a holistic view of the current state of support operations through a clearly structured presentation of critical metrics.  Key data points included are:\n\n**I. Ticket Resolution & Volume:**\n\n* **Average Resolution Time:**  Provides a clear indication of the average time taken to resolve tickets, highlighting areas where improvements in efficiency may be needed. Trends in this metric over time can be used to identify bottlenecks and measure the effectiveness of implemented changes.\n* **Closed Tickets (Same Day):** This metric showcases the team's effectiveness in resolving tickets within the same day of receipt, demonstrating responsiveness and efficiency in addressing urgent issues.  It serves as a key indicator of customer satisfaction.\n* **Overall Closed Tickets:**  Displays the total number of tickets successfully closed during the selected reporting period, providing a general measure of overall support workload and completion rate.\n* **Pending Tickets:**  Indicates the number of unresolved tickets currently awaiting action, highlighting potential backlog issues and areas requiring immediate attention.  This metric helps prioritize workload and allocate resources effectively.\n* **Total Tickets Received:** Presents the total number of tickets received during the defined reporting period, offering a measure of the overall support demand and potential fluctuations in workload.\n\n\n**II. Agent Performance & Ticket Categorization:**\n\n* **Ticket Closure by Agent:** This breakdown details the number of tickets closed by each individual agent, enabling performance comparisons and identification of top performers and areas needing additional training or support.\n* **Total Ticket Count by Agent:**  A supplementary metric to \"Ticket Closure by Agent,\"  this shows the overall volume of tickets handled by each agent, providing context to their closure rate.\n* **Ticket Inflow by Category:** This metric categorizes incoming tickets by type (e.g., billing, technical, account related), offering insights into prevalent issues and potential areas for proactive improvement and process optimization.  This assists in resource allocation and prioritization of training.\n* **Ticket Source:** This data point identifies the origin of tickets (e.g., email, web form, phone), revealing preferred customer contact channels and informing strategies for improving support accessibility and responsiveness across channels.\n\n\nIn summary, the HAPPYFOX DASHBOARD report provides a powerful tool for monitoring and optimizing support performance.  By presenting a clear picture of ticket volume, resolution times, agent performance, and ticket categorization, it empowers management to identify areas for improvement, make informed decisions about resource allocation, and ultimately enhance customer satisfaction and operational efficiency.\n"], ["75", "HAPPYFOX DASHBOARD", "Average Resolution Time, Closed Tickets (Same Day), Overall Closed Tickets, Pending Tickets, Ticket Closure by Agent, Ticket Inflow by Category, Ticket Source, Total Ticket Count by Agent, Total Tickets Received.", "## HAPPYFOX DASHBOARD: A Comprehensive Performance Report\n\nThe HAPPYFOX DASHBOARD report provides a holistic overview of customer support performance, focusing on key metrics to identify trends, bottlenecks, and areas for improvement.  Designed for managerial oversight and agent performance evaluation, this report leverages real-time data to offer an easily digestible snapshot of the current support landscape.\n\n**Report Content and Structure:**\n\nThe report utilizes a combination of graphical representations and tabular data to effectively communicate its findings. Key sections include:\n\n* **Overall Ticket Volume & Velocity:** This section displays the `Total Tickets Received` and provides context through visualization of the `Ticket Inflow by Category` (e.g., billing, technical, account) and `Ticket Source` (e.g., email, phone, chat).  This allows for identification of high-volume categories and sources, informing resource allocation and potential process improvements.\n\n* **Agent Performance:** Individual agent contributions are analyzed via `Total Ticket Count by Agent` and `Ticket Closure by Agent`.  This granular level of detail facilitates performance monitoring, identification of high-performing agents, and targeted training initiatives for agents requiring further support.  The efficiency of each agent is further contextualized by the `Average Resolution Time`.\n\n* **Ticket Status & Resolution:** The report provides a clear indication of the current health of the support system, highlighting `Pending Tickets`, `Closed Tickets (Same Day)`, and `Overall Closed Tickets`.  The proportion of same-day closures serves as a key performance indicator for responsiveness and efficiency.\n\n* **Key Performance Indicators (KPIs):**  The report synthesizes the raw data into key actionable KPIs such as average resolution time, percentage of same-day ticket closure, and overall ticket closure rate.  These metrics are crucial for tracking progress towards support goals and identifying areas requiring attention.\n\n**Purpose and Intended Audience:**\n\nThe HAPPYFOX DASHBOARD report serves multiple purposes:\n\n* **Real-time Monitoring:**  Provides up-to-the-minute visibility into the support team's performance and workload.\n* **Performance Evaluation:** Enables managers to assess individual agent performance and identify areas for improvement.\n* **Resource Allocation:**  Informs strategic decision-making related to staffing, training, and process optimization.\n* **Trend Analysis:**  Highlights emerging trends in ticket volume, resolution times, and common issue categories.\n* **Proactive Problem Solving:**  Facilitates early detection of potential bottlenecks and service disruptions.\n\n\nThe report is intended for use by support managers, team leads, and other stakeholders responsible for overseeing and improving customer support operations.  Its concise and visually appealing presentation ensures that key insights are readily accessible and easily understood.\n"], ["76", "BIN Report", "Daily TPC, Daily TPV, Monthly Collections Trend, Spike Percentage (Spike %), Total Number of Merchants (#Merchants), Total Processed Count (TPC), Total Processed Value (TPV)", "## BIN Report: Performance Overview and Merchant Activity Analysis\n\nThe BIN Report provides a comprehensive overview of transaction processing performance and merchant activity within a specified reporting period.  Its primary purpose is to offer key insights into the health and growth of the business, specifically focusing on transaction volume, value, and associated trends.  The report leverages granular data to identify areas of strength and potential concerns, facilitating informed decision-making and proactive risk management.\n\n**Key Metrics and Analysis:**\n\nThe report meticulously analyzes the following key performance indicators (KPIs):\n\n* **Daily TPC (Transactions Per Count):** This metric displays the daily volume of processed transactions, providing a granular view of daily operational activity and fluctuations.  The report likely visualizes this data using line graphs to highlight trends and identify potential outliers.\n\n* **Daily TPV (Transaction Processing Value):** This metric presents the total monetary value of daily transactions, offering crucial insight into the revenue generated each day.  Similar to Daily TPC, this data is likely presented graphically to showcase daily performance and trends.\n\n* **Monthly Collections Trend:** This section analyzes the pattern of collected payments over time, offering a critical perspective on the efficiency of the payment processing system and potential delays or discrepancies.  This could include line graphs, bar charts, or tables illustrating monthly collections against targets.\n\n* **Spike Percentage (Spike %):** This metric pinpoints significant increases or decreases in daily TPC or TPV, highlighting potential anomalies that require further investigation. The report will likely identify these spikes and suggest potential causes, such as promotional campaigns or system glitches.\n\n* **Total Number of Merchants (#Merchants):** This metric provides a clear understanding of the overall merchant base, offering context to the overall transaction volumes and values.  Trends in merchant acquisition and attrition may also be discussed.\n\n* **Total Processed Count (TPC):** This metric represents the cumulative number of transactions processed over the entire reporting period, offering a high-level view of overall transaction volume.\n\n* **Total Processed Value (TPV):** This metric represents the total monetary value of all transactions processed during the reporting period, providing a crucial overview of total revenue generated.\n\n**Report Purpose and Usage:**\n\nThe BIN Report serves multiple crucial purposes within the organization:\n\n* **Performance Monitoring:**  It provides a daily and monthly view of transaction processing efficiency and revenue generation.\n* **Risk Management:**  Identification of spikes and anomalies allows for proactive mitigation of potential risks and fraud detection.\n* **Business Growth Analysis:**  Tracking key metrics helps assess business growth, identify trends, and inform strategic planning.\n* **Merchant Management:**  Understanding merchant activity informs strategies for customer acquisition, retention, and support.\n* **Financial Reporting:**  The report provides crucial data for financial analysis and reporting.\n\n\nThe report's detailed analysis and visual representations of key metrics empower stakeholders to make data-driven decisions, optimize operational efficiency, and drive sustainable business growth.  Regular review of the BIN Report is essential for maintaining a healthy and thriving payment processing ecosystem.\n"], ["77", "Payout and Wallet Funding", "$TPV, $TPV, Currency, First Payout View, First payout date, Merchant, MerchantID, SignUp date, TPC, TPC, Txn_Amount", "This report, titled \"Payout and Wallet Funding,\" provides a comprehensive analysis of merchant payouts and wallet funding activity. It offers a granular view of financial transactions, enabling detailed tracking of funds processed and disbursed to merchants.  The report's primary purpose is to reconcile transaction volumes with actual payout amounts, identifying potential discrepancies and ensuring accurate financial reporting.\n\nThe report captures key metrics to achieve this goal:\n\n* **$TPV (Transaction Processing Value):**  The report likely includes two instances of this metric. This may represent a distinction between the total processing value before and after any fees or deductions, allowing for a clear understanding of the net value processed for each merchant.\n\n* **Currency:**  Indicates the currency in which each transaction and payout was processed, facilitating multi-currency analysis and reconciliation.\n\n* **First Payout View:** This field likely denotes the date and time when the merchant first viewed their payout information within the system.  This is useful for monitoring user experience and engagement with payout notifications.\n\n* **First Payout Date:**  The date on which the first payout was initiated for each merchant. This provides a crucial benchmark for assessing the timeliness of payouts.\n\n* **Merchant:** The name of the merchant receiving the payout.\n\n* **MerchantID:** The unique identifier for each merchant, enabling efficient data filtering and analysis.\n\n* **Sign-Up Date:** The date when the merchant registered on the platform. This metric assists in analyzing payout behavior relative to the merchant's tenure.\n\n* **TPC (Transactions Per Customer):**  Similar to TPV, the duplication suggests a potential distinction, perhaps between successful and failed transactions, or a breakdown by transaction type.\n\n* **Txn_Amount:** The individual transaction amount for each processed transaction.  This granular data allows for detailed transaction-level analysis.\n\nIn summary, the \"Payout and Wallet Funding\" report offers a robust and detailed overview of the financial health of the merchant network.  Its comprehensive metrics facilitate identification of trends in transaction volumes, payout processing times, potential errors, and overall financial performance, providing invaluable insights for business decision-making and financial reconciliation.\n"], ["82", "PAN Failed Transactions", "Charge Message: The message or code related to the transaction charge attempt\nFailure Reason: The reason or code indicating why the transaction failed\n%MakeUp: Percentage of failed transactions in relation to all transactions for the merchant\nMerchant: The name of the merchant involved in the failed transaction\nMerchant Level: The level or tier of the merchant based on specific criteria\nPAN: The Primary Account Number (masked) associated with the transaction\nTPC: Transaction Processing Code, used to categorize the transaction\nTotal Failed Txns: The total number of failed transactions for the merchant", "The \"PAN Failed Transactions\" report provides a comprehensive analysis of failed payment transactions, focusing on identifying trends, root causes, and potential areas for improvement in payment processing efficiency for participating merchants.  The report leverages granular data points to pinpoint the reasons behind payment failures and their impact on individual merchants.\n\n**Report Contents:**\n\nThe report details each failed transaction, presenting the following key metrics for each instance:\n\n* **Merchant:** The name of the merchant experiencing the transaction failure.\n* **Merchant Level:**  Categorizes the merchant based on predefined criteria (e.g., transaction volume, tenure, risk profile), allowing for stratified analysis and targeted interventions.\n* **PAN (Masked):** The masked Primary Account Number associated with the failed transaction, enabling investigation into specific cardholder issues without compromising sensitive data.\n* **TPC (Transaction Processing Code):**  Provides a standardized classification of the transaction type, facilitating the identification of failure patterns linked to specific transaction categories (e.g., purchases, refunds, etc.).\n* **Charge Message:**  A detailed description or code capturing the specific message received during the attempted charge, offering insights into the nature of the processing error (e.g., insufficient funds, declined by issuer, invalid card number).\n* **Failure Reason:**  A specific code or description detailing the underlying reason for transaction failure, providing crucial information for diagnostic purposes and remediation strategies (e.g., network error, authorization failure, security violation).\n* **Total Failed Txns:** The aggregate number of failed transactions recorded for each merchant during the reporting period.\n* **%MakeUp:**  Calculates the percentage of failed transactions relative to the total number of transactions processed by each merchant, providing a crucial metric for assessing the severity of the payment processing issues for individual merchants.\n\n**Report Purpose:**\n\nThe primary purpose of this report is to identify and analyze the causes of payment processing failures. By presenting a detailed breakdown of failed transactions, including the associated merchant, transaction type, and specific error codes, the report enables:\n\n* **Improved Merchant Support:**  Facilitates proactive outreach to merchants experiencing high failure rates, offering tailored support and troubleshooting assistance.\n* **Proactive Risk Management:**  Highlights potential systemic issues affecting payment processing, enabling timely intervention to prevent further failures and mitigate financial losses.\n* **Enhanced Payment Processing Efficiency:**  By pinpointing recurring failure patterns, the report aids in identifying and resolving underlying technical or procedural issues within the payment processing infrastructure.\n* **Data-Driven Decision Making:**  Provides quantifiable data to support informed decision-making regarding improvements to payment processing systems and merchant support strategies.\n\nThis report is a critical tool for enhancing payment processing reliability, improving the overall customer experience, and maximizing revenue for both merchants and the payment processing system.\n"], ["205", "All Transactions", "Count of Successful Auth Transactions, Merchants Daily TPC and TPV in the Last 90 Days, TPC (Transaction Count), TPC by Cardscheme, TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View), TPV (Transaction Value in USD)", "The \"All Transactions\" report provides a comprehensive overview of all transactional activity over the past 90 days, offering granular insights into both volume and value.  Its purpose is to deliver a holistic understanding of payment processing performance, enabling informed decision-making regarding operational efficiency, risk management, and business growth strategies.\n\nThe report meticulously details the following key performance indicators (KPIs):\n\n* **Count of Successful Auth Transactions:** This metric provides a fundamental measure of successful payment authorizations, indicating the overall efficiency of the payment processing system.  It acts as a primary indicator of system health and operational reliability.\n\n* **Merchants Daily TPC and TPV in the Last 90 Days:**  This section offers a daily breakdown of Transaction Processing Count (TPC) and Transaction Processing Value (TPV) for each merchant within the reporting period.  This granular level of detail allows for the identification of top-performing and underperforming merchants, facilitating targeted interventions and strategic resource allocation.\n\n* **TPC (Transaction Count):** This metric represents the total number of transactions processed over the 90-day period, providing a high-level view of overall transactional activity.\n\n* **TPC by Cardscheme:** This breakdown analyzes transaction counts by individual card schemes (e.g., Visa, Mastercard, American Express), allowing for the identification of trends and potential issues specific to each card network.  This data is crucial for optimizing processing strategies and negotiating favorable terms with payment processors.\n\n* **TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View):** This section presents a dynamic view of transaction trends across different time granularities (monthly, weekly, and daily).  The visualization of these trends allows for the identification of seasonal patterns, growth trajectories, and potential anomalies or fluctuations requiring further investigation.  This is crucial for forecasting, resource planning, and proactive risk mitigation.\n\n* **TPV (Transaction Value in USD):** This metric represents the total value of all transactions processed in USD, offering a critical measure of revenue generation and business performance.\n\nIn summary, the \"All Transactions\" report serves as a central repository for comprehensive transactional data, providing stakeholders with a clear and concise picture of payment processing performance, enabling data-driven decisions to optimize operational efficiency, manage risk effectively, and drive revenue growth.  The report's multi-faceted approach, utilizing both aggregate and granular data points across multiple timeframes, allows for a holistic understanding of the transactional landscape.\n"], ["210", "Provider Cost Report", "1. COS\n2. REV\n3. TPV\n4. TPC", "**Provider Cost Report: A Detailed Description**\n\nThis report, titled \"Provider Cost Report,\" provides a comprehensive analysis of provider performance by examining the relationship between costs incurred and revenue generated.  The report leverages key performance indicators (KPIs) to offer insights into profitability and operational efficiency.  Specifically, the report utilizes the following metrics:\n\n* **Cost of Service (COS):** This metric represents the total direct and indirect costs associated with delivering services.  The report will detail the breakdown of COS, potentially including categories such as labor costs, supplies, overhead, and other relevant expenses.  Analyzing COS allows for identification of areas where cost optimization strategies might be implemented.\n\n* **Revenue (REV):**  This metric captures the total revenue generated from the provision of services during the specified reporting period.  The report will present revenue data in a manner that allows for comparisons across providers, service lines, or time periods.\n\n* **Total Provider Volume (TPV):** This metric represents the total volume of services provided by each provider.  This could encompass metrics such as number of procedures performed, patient visits, or other relevant units of service, depending on the context of the providers and services.  TPV allows for the evaluation of provider productivity and the assessment of revenue generation relative to service volume.\n\n* **Total Provider Cost (TPC):**  This metric represents the total cost attributed to each provider.  This is likely a summation of all costs directly and indirectly related to the provider's activities, including those encompassed within the COS metric.  TPC, when analyzed alongside TPV and REV, provides a critical insight into the profitability of individual providers or provider groups.\n\n**Purpose and Use:**\n\nThe primary purpose of this report is to facilitate data-driven decision-making related to provider cost management and operational efficiency.  By analyzing the interplay between COS, REV, TPV, and TPC, stakeholders can identify:\n\n* **High-cost providers:**  The report will highlight providers with disproportionately high costs relative to their revenue and volume.\n* **Underperforming services:**  Services with low revenue and high costs can be identified for potential restructuring or discontinuation.\n* **Opportunities for cost reduction:**  The detailed breakdown of COS allows for targeted cost reduction strategies.\n* **Provider productivity:**  TPV enables the assessment of provider efficiency and output.\n* **Profitability analysis:** Comparing TPC to REV provides a clear picture of the profitability of each provider and service line.\n\nThe \"Provider Cost Report\" is intended for use by management, finance teams, and other relevant stakeholders involved in optimizing provider performance and resource allocation.  The insights generated from this report contribute significantly to strategic planning, resource allocation, and ultimately, improved financial performance.\n"], ["234", "All Transactions", "Count of Successful Auth Transactions, Merchants Daily TPC and TPV in the Last 90 Days, TPC (Transaction Count), TPC by Cardscheme, TPC and TPV Transaction Trend (Monthly, Weekly, and Daily View), TPV (Transaction Value in USD)", "The \"All Transactions\" report provides a comprehensive overview of all transaction activity processed over the past 90 days, offering granular insights into transaction volume, value, and performance trends.  The report's primary purpose is to provide a holistic view of business performance, enabling informed decision-making related to sales, risk management, and operational efficiency.\n\nThe report meticulously tracks several key performance indicators (KPIs):\n\n* **Transaction Volume:** The report begins with the fundamental metric of `Count of Successful Auth Transactions`, providing a total count of successfully authorized transactions within the specified timeframe.  This serves as a baseline measure of overall business activity.\n\n* **Merchant Performance:**  Daily `Merchants Daily TPC (Transactions Per Card)` and `TPV (Transaction Value)` are presented, allowing for the granular analysis of individual merchant performance.  This enables the identification of top-performing merchants, as well as those requiring further investigation or support.\n\n* **Transaction Performance Analysis:** The report includes a detailed breakdown of `TPC (Transaction Count)`, offering a clear picture of the overall transaction volume.  Further segmentation by `TPC by Cardscheme` allows for the identification of trends and potential issues associated with specific card networks (e.g., Visa, Mastercard, American Express).\n\n* **Trend Analysis:** A key feature of the report is its robust trend analysis capabilities.  The `TPC and TPV Transaction Trend` is visualized across three distinct timeframes: monthly, weekly, and daily. This multifaceted approach allows for the detection of both long-term trends and short-term fluctuations, highlighting potential seasonal effects, promotional impacts, or anomalous activities.\n\n* **Monetary Value:**  `TPV (Transaction Value in USD)` provides the total value of all transactions in USD, offering a crucial measure of overall revenue generated. This metric, in conjunction with TPC, allows for the calculation of Average Transaction Value (ATV), providing additional insights into customer spending habits.\n\nIn summary, the \"All Transactions\" report delivers a powerful and versatile tool for comprehensive transaction analysis. Its detailed metrics and visualization capabilities equip stakeholders with the data needed to monitor business performance, identify opportunities for growth, manage risk effectively, and proactively address any emerging operational challenges.\n"], ["240", "PAYOUT TRANSACTION ANALYSIS", "NGN Rave payout analysis, Pay With Bank Transfer (PWBT) funding per Merchant YTD.\n\n\n\n\n\n\n", "**Report Title: PAYOUT TRANSACTION ANALYSIS**\n\n**Report Description:**\n\nThis report provides a comprehensive analysis of payout transactions, focusing specifically on performance metrics related to Nigerian Naira (NGN) Rave payouts and Pay With Bank Transfer (PWBT) funding for merchants on a year-to-date (YTD) basis.  The analysis aims to provide key insights into the efficiency, cost-effectiveness, and overall health of the payout processes.\n\nThe report will delve into the following key areas:\n\n* **NGN Rave Payout Analysis:** This section will present a detailed examination of all NGN-denominated transactions processed through the Rave payout platform. Key performance indicators (KPIs) analyzed will include:\n    * **Transaction Volume:** Total number of payouts processed via Rave.\n    * **Total Value Processed:** Aggregate sum of all NGN payouts.\n    * **Average Transaction Value:** Average value of individual Rave payouts.\n    * **Success Rate:** Percentage of Rave payouts successfully completed.\n    * **Failure Rate & Reasons:**  Identification and categorization of payout failures, including root causes for analysis and remediation efforts.\n    * **Processing Time:** Average time taken to process Rave payouts.\n    * **Cost Analysis:** Examination of fees and charges associated with Rave payouts.\n\n* **Pay With Bank Transfer (PWBT) Funding per Merchant YTD:** This segment will provide a merchant-level analysis of PWBT funding, offering granular insights into each merchant's utilization of this payment method. KPIs explored will include:\n    * **Merchant-Specific Transaction Volume:** Number of PWBT transactions per merchant.\n    * **Total Funding per Merchant:** Total NGN amount funded via PWBT for each merchant.\n    * **Average Transaction Value per Merchant:** Average value of PWBT transactions per merchant.\n    * **Funding Success Rate per Merchant:** Percentage of successful PWBT funding attempts for each merchant.\n    * **Trend Analysis:** Examination of PWBT funding patterns throughout the year for each merchant.  This will include identifying growth, decline, or seasonality.\n\n**Report Purpose:**\n\nThe primary purpose of this report is to provide stakeholders with actionable intelligence on the performance of the payout systems.  By analyzing the data presented, management can:\n\n* **Identify areas for improvement:** Pinpoint bottlenecks and inefficiencies in the Rave and PWBT processes.\n* **Optimize operational efficiency:**  Develop strategies to reduce processing times and costs.\n* **Enhance merchant satisfaction:**  Address issues impacting the timely and successful completion of payouts for merchants.\n* **Support strategic decision-making:** Inform decisions regarding future investments in payout infrastructure and processes.\n* **Monitor financial performance:** Track the financial impact of payout methods on overall business operations.\n\n\nThe report will conclude with a summary of key findings, recommendations for improvement, and a roadmap for future action.  Data will be presented using tables, charts, and graphs to ensure clarity and facilitate easy understanding.\n"], ["242", "Chargeback Report", "Chargeback volume in USD\nChargeback count\nChargeback ratio", "The Chargeback Report provides a comprehensive analysis of chargeback activity, offering key insights into the financial impact and frequency of disputed transactions.  This report leverages three core metrics to provide a clear understanding of chargeback performance:\n\n* **Chargeback Volume (USD):** This metric quantifies the total monetary value of all chargebacks processed within a specified timeframe (e.g., daily, weekly, monthly, quarterly). It represents the direct financial loss incurred due to disputed transactions.\n\n* **Chargeback Count:** This metric details the total number of individual chargeback incidents registered during the reporting period.  It provides a measure of the frequency of chargeback disputes, indicating the volume of customer complaints or fraudulent activity.\n\n* **Chargeback Ratio:** This crucial metric calculates the percentage of chargebacks relative to the total number of transactions processed.  Expressed as a ratio (e.g., X chargebacks per 1000 transactions), it serves as a key performance indicator (KPI) reflecting the effectiveness of fraud prevention and customer service strategies.  A declining chargeback ratio indicates improved performance in mitigating chargeback risk.\n\nThe report's purpose is to facilitate proactive risk management and operational efficiency by:\n\n* **Identifying trends:** Analyzing chargeback volume, count, and ratio over time reveals patterns and potential causes for disputes, allowing for targeted interventions.\n\n* **Measuring effectiveness of mitigation strategies:** The report allows for the assessment of the impact of implemented fraud prevention measures and customer service improvements on chargeback rates.\n\n* **Improving operational efficiency:** By pinpointing areas with high chargeback rates, the report assists in optimizing processes and reducing costs associated with disputes.\n\n* **Informing strategic decision-making:** The data presented informs strategies for improving payment processing, strengthening fraud prevention, and enhancing customer service to minimize future chargebacks.\n\nThe report typically includes visualizations such as charts and graphs to illustrate trends in chargeback metrics, potentially segmented by various factors like transaction type, payment method, or geographical location, enabling a granular analysis of chargeback causes and their impact.  This allows stakeholders to make data-driven decisions to reduce chargeback losses and improve overall financial performance.\n"], ["243", "Compliance KRI", "Actual daily transactions and value (Budgeted collection spike), Budgeted Payout Spike, Daily Auth report values, Daily Auth Spike report, Monthly TPC Spike, Monthly TPV Spike – Collections", "## Compliance Key Risk Indicator (KRI) Report: A Detailed Description\n\nThis report, titled \"Compliance KRI,\" provides a comprehensive overview of key risk indicators related to the transactional compliance of the organization.  It focuses on monitoring the volume and value of daily transactions against pre-defined budgets and identifying potential compliance breaches or anomalies. The report is designed to proactively mitigate financial and reputational risks associated with irregular transaction activity.\n\nThe report leverages several crucial metrics to achieve its objective:\n\n* **Actual Daily Transactions and Value (Budgeted Collection Spike):** This metric compares the actual number and monetary value of daily transactions to a pre-established budget, specifically highlighting periods of unexpectedly high collection activity.  Significant deviations from the budgeted collection spike serve as early warning signals for potential compliance violations, such as fraudulent activity or unexpected surges in legitimate, but high-risk, transactions.  The report will likely visually represent this comparison using charts and graphs, potentially incorporating percentage deviations from the budget.\n\n* **Budgeted Payout Spike:**  This metric mirrors the collection spike but focuses on outgoing payments.  It compares actual payout volume and value against a pre-determined budget, alerting to unusual or excessive payouts that might indicate fraudulent schemes, internal theft, or other compliance issues. Similarly to collections, visual representations will facilitate the identification of anomalies.\n\n* **Daily Auth Report Values:** This metric reflects the total value of authorized transactions processed daily.  This provides a raw data point against which the spike reports can be compared, providing context for the magnitude of the detected anomalies.  It may also be used to identify overall trends in transaction volume and value.\n\n* **Daily Auth Spike Report:**  This metric highlights any significant deviations from the average daily authorized transaction value, potentially indicating a need for closer investigation.  This metric is intended to be an early detection system for unusual activity, providing a complementary perspective to the Budgeted Collection and Payout Spike metrics.\n\n* **Monthly TPC Spike (Transactions Per Customer):** This metric monitors the average number of transactions per customer on a monthly basis, identifying any substantial increases that might signify unauthorized access, account compromises, or other suspicious activity.  This metric focuses on individual customer behavior rather than aggregate transaction volume.\n\n* **Monthly TPV Spike (Transaction Value Per Customer):** This metric focuses on the monetary value of transactions per customer monthly, similarly alerting to potentially risky behavior indicative of fraud or compliance violations.  Combining this metric with TPC Spike allows for a comprehensive evaluation of customer transaction patterns.\n\n\n**Purpose of the Report:**\n\nThe primary purpose of the Compliance KRI report is to provide timely and actionable intelligence for compliance and risk management teams.  By tracking these key metrics and highlighting significant deviations from established budgets and expected patterns, the report enables proactive intervention to prevent and mitigate potential compliance violations, minimize financial losses, and safeguard the organization's reputation. The report's findings are likely to inform further investigation, potential regulatory reporting, and adjustments to internal controls and processes. The report’s presentation is designed for easy interpretation and rapid identification of high-risk situations, utilizing visual aids and clear summaries of critical findings.\n"]], "shape": {"columns": 3, "rows": 20}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>Metrics Captured</th>\n", "      <th>description</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Transactions</td>\n", "      <td>All Relevent transactions fields.</td>\n", "      <td>The \"Transactions\" report provides a comprehen...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Merchant Details</td>\n", "      <td>Active Merchants, Account ID (Unique identifie...</td>\n", "      <td>## Merchant Details Report: A Comprehensive Ov...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Wallet Report (Assurance)</td>\n", "      <td>Wallet balance per merchant, per wallet/curren...</td>\n", "      <td>## Wallet Report (Assurance): A Detailed Descr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Merchant Details</td>\n", "      <td>Active Merchants, Account ID (Unique identifie...</td>\n", "      <td>## Merchant Details Report: A Comprehensive Ov...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>Swap Time Drills</td>\n", "      <td>TPV, TPV, Revenue\\n\\nTransacting customers\\n\\n...</td>\n", "      <td>**Report Description: Swap Time Drills**\\n\\nTh...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>Working Capital Report</td>\n", "      <td>TPV in local currency and USD</td>\n", "      <td>The Working Capital Report provides a comprehe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>Swap Dashboard</td>\n", "      <td>TPV, TPC, Revenue\\n\\nRevenue\\n\\nCurrency Corri...</td>\n", "      <td>The Swap Dashboard report provides a comprehen...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>Core vs Products Recon</td>\n", "      <td>Transaction details including Flwref, txid, cu...</td>\n", "      <td>## Report Description: Core vs Products Recon\\...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>Chargeback Report</td>\n", "      <td>Chargeback volume in USD\\nChargeback count\\nCh...</td>\n", "      <td>The Chargeback Report provides a comprehensive...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>HAPPYFOX DASHBOARD</td>\n", "      <td>Average Resolution Time, Closed Tickets (Same ...</td>\n", "      <td>## HAP<PERSON><PERSON>F<PERSON> DASHBOARD Report: A Comprehensive ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>HAPPYFOX DASHBOARD</td>\n", "      <td>Average Resolution Time, Closed Tickets (Same ...</td>\n", "      <td>## HAP<PERSON>YFOX DASHBOARD: A Comprehensive Perform...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>BIN Report</td>\n", "      <td>Daily TPC, Daily TPV, Monthly Collections Tren...</td>\n", "      <td>## BIN Report: Performance Overview and Mercha...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>Payout and Wallet Funding</td>\n", "      <td>$TPV, $TPV, Currency, First Payout View, First...</td>\n", "      <td>This report, titled \"Payout and Wallet Funding...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>PAN Failed Transactions</td>\n", "      <td>Charge Message: The message or code related to...</td>\n", "      <td>The \"PAN Failed Transactions\" report provides ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>All Transactions</td>\n", "      <td>Count of Successful Auth Transactions, Merchan...</td>\n", "      <td>The \"All Transactions\" report provides a compr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>Provider Cost Report</td>\n", "      <td>1. COS\\n2. REV\\n3. TPV\\n4. TPC</td>\n", "      <td>**Provider Cost Report: A Detailed Description...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>All Transactions</td>\n", "      <td>Count of Successful Auth Transactions, Merchan...</td>\n", "      <td>The \"All Transactions\" report provides a compr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>PAYOUT TRANSACTION ANALYSIS</td>\n", "      <td>NGN Rave payout analysis, Pay With Bank Transf...</td>\n", "      <td>**Report Title: PAYOUT TRANSACTION ANALYSIS**\\...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>Chargeback Report</td>\n", "      <td>Chargeback volume in USD\\nChargeback count\\nCh...</td>\n", "      <td>The Chargeback Report provides a comprehensive...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>Compliance KRI</td>\n", "      <td>Actual daily transactions and value (Budgeted ...</td>\n", "      <td>## Compliance Key Risk Indicator (KRI) Report:...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            name  \\\n", "10                  Transactions   \n", "12              Merchant Details   \n", "17     Wallet Report (Assurance)   \n", "23              Merchant Details   \n", "43              Swap Time Drills   \n", "45        Working Capital Report   \n", "55                Swap Dashboard   \n", "57        Core vs Products Recon   \n", "66             Chargeback Report   \n", "72            HAPPYFOX DASHBOARD   \n", "75            HAPPYFOX DASHBOARD   \n", "76                    BIN Report   \n", "77     Payout and Wallet Funding   \n", "82       PAN Failed Transactions   \n", "205             All Transactions   \n", "210         Provider Cost Report   \n", "234             All Transactions   \n", "240  PAYOUT TRANSACTION ANALYSIS   \n", "242            Chargeback Report   \n", "243               Compliance KRI   \n", "\n", "                                      Metrics Captured  \\\n", "10                   All Relevent transactions fields.   \n", "12   Active Merchants, Account ID (Unique identifie...   \n", "17   Wallet balance per merchant, per wallet/curren...   \n", "23   Active Merchants, Account ID (Unique identifie...   \n", "43   TPV, TPV, Revenue\\n\\nTransacting customers\\n\\n...   \n", "45                       TPV in local currency and USD   \n", "55   TPV, TPC, Revenue\\n\\nRevenue\\n\\nCurrency Corri...   \n", "57   Transaction details including Flwref, txid, cu...   \n", "66   Chargeback volume in USD\\nChargeback count\\nCh...   \n", "72   Average Resolution Time, Closed Tickets (Same ...   \n", "75   Average Resolution Time, Closed Tickets (Same ...   \n", "76   Daily TPC, Daily TPV, Monthly Collections Tren...   \n", "77   $TPV, $TPV, Currency, First Payout View, First...   \n", "82   Charge Message: The message or code related to...   \n", "205  Count of Successful Auth Transactions, Merchan...   \n", "210                     1. COS\\n2. REV\\n3. TPV\\n4. TPC   \n", "234  Count of Successful Auth Transactions, Merchan...   \n", "240  NGN Rave payout analysis, Pay With Bank Transf...   \n", "242  Chargeback volume in USD\\nChargeback count\\nCh...   \n", "243  Actual daily transactions and value (Budgeted ...   \n", "\n", "                                           description  \n", "10   The \"Transactions\" report provides a comprehen...  \n", "12   ## Merchant Details Report: A Comprehensive Ov...  \n", "17   ## Wallet Report (Assurance): A Detailed Descr...  \n", "23   ## Merchant Details Report: A Comprehensive Ov...  \n", "43   **Report Description: Swap Time Drills**\\n\\nTh...  \n", "45   The Working Capital Report provides a comprehe...  \n", "55   The Swap Dashboard report provides a comprehen...  \n", "57   ## Report Description: Core vs Products Recon\\...  \n", "66   The Chargeback Report provides a comprehensive...  \n", "72   ## HAPPYFOX DASHBOARD Report: A Comprehensive ...  \n", "75   ## HAPPYFOX DASHBOARD: A Comprehensive Perform...  \n", "76   ## BIN Report: Performance Overview and Mercha...  \n", "77   This report, titled \"Payout and Wallet Funding...  \n", "82   The \"PAN Failed Transactions\" report provides ...  \n", "205  The \"All Transactions\" report provides a compr...  \n", "210  **Provider Cost Report: A Detailed Description...  \n", "234  The \"All Transactions\" report provides a compr...  \n", "240  **Report Title: PAYOUT TRANSACTION ANALYSIS**\\...  \n", "242  The Chargeback Report provides a comprehensive...  \n", "243  ## Compliance Key Risk Indicator (KRI) Report:...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Apply the function to create the new 'description' column\n", "new_df_top_20['description'] = new_df_top_20.apply(generate_description, axis=1)\n", "\n", "# Display the updated DataFrame\n", "display(new_df_top_20)"]}, {"cell_type": "code", "execution_count": 15, "id": "e9cac2b1", "metadata": {}, "outputs": [], "source": ["new_df_top_20.to_csv('top_20_report_with_generated_description.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "5dea69f6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 5}