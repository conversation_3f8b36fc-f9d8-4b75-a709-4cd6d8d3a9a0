import logging
import os
import sys

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add the parent directory of 'agent_lab' to the Python path
# This allows importing modules from 'agent_lab.knowledge_base.vector_store'
# Adjust the number of '..' based on where you place this script relative to 'flw-nlp-platform'
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, '..')) # Assumes run_embedding.py is in agent_lab
sys.path.append(PROJECT_ROOT)

try:
    from gemini_faiss_retriever import build_index_and_datastore
    logging.info("Successfully imported build_index_and_datastore function.")
except ImportError as e:
    logging.error(f"Failed to import build_index_and_datastore: {e}")
    logging.error("Please ensure that the script is run from a location where 'agent_lab' module is accessible,")
    logging.error("or that the Python path is configured correctly.")
    sys.exit(1)

if __name__ == "__main__":
    logging.info("Starting the report embedding process...")
    success = build_index_and_datastore()
    if success:
        logging.info("Report embedding process completed successfully.")
    else:
        logging.error("Report embedding process failed. Please check the logs for details.")