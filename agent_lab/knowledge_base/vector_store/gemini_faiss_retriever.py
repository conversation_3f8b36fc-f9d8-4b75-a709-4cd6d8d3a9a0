import pandas as pd
import numpy as np
import faiss
import os
import json
import time
from dotenv import load_dotenv
import logging
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions # For specific API error handling

# === Get a logger for this module ===
logger = logging.getLogger(__name__)

# === Load environment variables and configure Gemini API ===
logger.debug("Loading environment variables from .env file.")
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        logger.info("Google Generative AI SDK configured successfully.")
    except Exception as e:
        logger.critical(f"Failed to configure Google Generative AI SDK: {e}", exc_info=True)
        
# === Configuration ===
# Get the directory of the current script
SCRIPT_DIR = os.path.dirname(__file__)

# === Define file paths relative to the script's directory ===
CSV_FILE_PATH = os.path.join(SCRIPT_DIR, 'reports.csv')  # Your CSV file path
RELEVANT_COLUMN_TO_EMBED = 'Description' # Updated
LINK_COLUMN = 'webUrl' # Updated
COLUMNS_TO_SUMMARIZE = ['name', 'Metrics Captured', 'reportType', 'Data Partner'] # Updated

# === Gemini API Configuration ===
API_KEY = GEMINI_API_KEY

# === Using text-embedding-004 for embeddings as it's specialized for this task. ===
EMBEDDING_API_MODEL = "text-embedding-004" 
SUMMARIZATION_API_MODEL = "gemini-1.5-flash-latest"

# === 
FAISS_INDEX_PATH = os.path.join(SCRIPT_DIR, "report_index_gemini.faiss")
REPORT_DATA_STORE_PATH = os.path.join(SCRIPT_DIR, "report_data_gemini.json")

# --- Helper Functions ---
def get_embedding_gemini(text_to_embed, task_type="RETRIEVAL_DOCUMENT"):
    """
    Generates an embedding for a given text using the Gemini Embedding API.
    Args:
        text_to_embed (str): The text to embed.
        task_type (str): The task type for the embedding (e.g., "RETRIEVAL_DOCUMENT", "RETRIEVAL_QUERY").
    Returns:
        np.array or None: The embedding vector or None if an error occurs."""
    if not text_to_embed or not isinstance(text_to_embed, str) or not text_to_embed.strip():
        logger.warning("Empty text provided for embedding. Returning None.")
        return None
    
    if not API_KEY:
        logger.error("GEMINI_API_KEY is missing at the time of calling get_embedding_gemini. Cannot proceed.")
        return None
    
    try:
        logger.debug(f"Attempting to get embedding for text (first 100 chars): '{str(text_to_embed)[:100]}...'")
        # The SDK expects the model name to be 'models/text-embedding-004'
        # Task type can be specified for better results, e.g., "RETRIEVAL_DOCUMENT" or "SEMANTIC_SIMILARITY"
        result = genai.embed_content(
            model=f"models/{EMBEDDING_API_MODEL}",
            content=text_to_embed,
            task_type=task_type
        )
        
        if "embedding" in result:
            logger.debug(f"Successfully generated embedding for text (first 100 chars): '{str(text_to_embed)[:100]}...'")
            return np.array(result["embedding"])
        else:
            logger.error(f"Embedding not found in SDK response for text (first 100 chars): '{str(text_to_embed)[:100]}...'. Response: {result}")
            return None
    except google_exceptions.GoogleAPIError as e:
        logger.error(f"Google API Error calling Gemini Embedding API for text (first 100 chars): '{str(text_to_embed)[:100]}...'. Error: {e}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred in get_embedding_gemini for text '{str(text_to_embed)[:100]}...': {e}", exc_info=True)
        return None


def generate_summary_gemini(prompt_text):
    """Generates a summary using the Gemini API."""
    if not API_KEY:
        logger.error("GEMINI_API_KEY is missing at the time of calling generate_summary_gemini. Cannot proceed.")
        return "[Error generating summary: API Key missing]"

    generation_config = genai.types.GenerationConfig(
        temperature=0.7,
        top_p=0.9,
        top_k=50,
        max_output_tokens=150,
        stop_sequences=[]
    )
    
    # For summarization, a simple prompt is often sufficient.
    # If chat history is needed, the 'contents' structure would be more complex.
    # For this function, we assume prompt_text is the direct input for summarization.
    contents_for_sdk = [{"role": "user", "parts": [{"text": prompt_text}]}]

    try:
        logger.debug(f"Attempting to generate summary for prompt (first 100 chars): '{str(prompt_text)[:100]}...'")
        model = genai.GenerativeModel(SUMMARIZATION_API_MODEL)
        response = model.generate_content(
            contents=contents_for_sdk,
            generation_config=generation_config
        )

        if response.parts:
            logger.debug(f"Successfully generated summary for prompt (first 100 chars): '{str(prompt_text)[:100]}...'")
            return response.text.strip()
        elif response.prompt_feedback and response.prompt_feedback.block_reason:
            block_reason_message = response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason
            logger.error(f"Prompt blocked by Gemini API for summarization. Reason: {block_reason_message}")
            if response.prompt_feedback.safety_ratings:
                logger.error(f"Safety Ratings: {response.prompt_feedback.safety_ratings}")
            return f"[Summary generation blocked due to: {block_reason_message}]"
        else:
            logger.error(f"Error: Unexpected response structure or empty content from Gemini API for summarization. Response: {response}")
            return "[Error generating summary: Empty or unexpected response]"

    except google_exceptions.GoogleAPIError as e:
        logger.error(f"Google API Error calling Gemini API for summarization: {e}", exc_info=True)
        return "[Error generating summary due to API error]"
    except Exception as e:
        logger.error(f"An unexpected error occurred in generate_summary_gemini: {e}", exc_info=True)
        return "[Error generating summary due to an unexpected issue]"

# --- Phase 1 & 2: Offline Indexing Pipeline ---
def build_index_and_datastore():
    """Loads data, generates embeddings via API, builds FAISS index, and saves data store."""
    print("Starting offline indexing pipeline with Gemini API (using SDK)...")

    # Explicit API Key Check
    if not API_KEY:
        print("\nCritical Error: GEMINI_API_KEY is not set or not loaded from .env file, or SDK not configured.")
        print("The indexing process requires a valid Gemini API key to generate embeddings.")
        print("Please ensure GEMINI_API_KEY is correctly set in your .env file and accessible.\n")
        return False
    
    # Check if genai was configured (if API_KEY was present at module load)
    # This is a bit indirect; a better way would be a dedicated status flag from genai.configure
    try:
        # A light check to see if SDK is responsive, e.g., listing a model
        # This is optional and adds a small overhead.
        # genai.get_model(f"models/{EMBEDDING_API_MODEL}") 
        pass # Assuming genai.configure() at the top was sufficient if no exception was raised
    except Exception as e:
        print(f"\nCritical Error: Gemini SDK does not seem to be configured correctly: {e}")
        print("Please check API key and SDK setup.\n")
        return False

    try:
        df = pd.read_csv(CSV_FILE_PATH)
        print(f"Loaded {len(df)} rows from {CSV_FILE_PATH}")
    except FileNotFoundError:
        print(f"Error: CSV file not found at {CSV_FILE_PATH}")
        print("Creating a dummy 'reports.csv' for demonstration.")
        dummy_data = {
            'reportType': ['PowerBIReport', 'PowerBIReport', 'PaginatedReport', 'PowerBIReport'],
            'name': ['Q1 Sales Review', 'User Churn Analysis', 'Marketing Campaign Performance', 'Website Traffic Overview'],
            RELEVANT_COLUMN_TO_EMBED: [
                'A comprehensive review of sales performance for the first quarter, highlighting key achievements and areas for improvement.',
                'Detailed analysis of customer churn rates, identifying primary reasons for churn and proposing retention strategies.',
                'Performance metrics for the recent marketing campaign, including reach, engagement, and conversion rates.',
                'Overview of website traffic sources, user behavior, and popular content for the last month.'
            ],
            'Metrics Captured': ['Revenue, Units Sold', 'Churn Rate, LTV', 'Impressions, Clicks, ROI', 'Page Views, Bounce Rate'],
            LINK_COLUMN: ['http://example.com/sales-q1', 'http://example.com/churn-analysis', 'http://example.com/marketing-perf', 'http://example.com/web-traffic'],
            'Data Partner': ['Internal DB', 'CRM System', 'Ad Platform', 'Google Analytics'],
            'createdDateTime': ['2023-01-01T10:00:00Z', '2023-01-02T11:00:00Z', '2023-01-03T12:00:00Z', '2023-01-04T13:00:00Z'],
            'modifiedDateTime': ['2023-01-01T10:00:00Z', '2023-01-02T11:00:00Z', '2023-01-03T12:00:00Z', '2023-01-04T13:00:00Z'],
            'modifiedBy': ['user1', 'user2', 'user1', 'user3'],
            'createdBy': ['user1', 'user2', 'user1', 'user3']
        }
        df = pd.DataFrame(dummy_data)
        df.to_csv(CSV_FILE_PATH, index=False)
        print(f"Dummy CSV created with {len(df)} rows. Please re-run the script.")
        return False

    # Drop specified 'Unnamed' columns if they exist
    # No longer dropping 'Unnamed: 7', 'Unnamed: 8' explicitly, 
    # as the new schema doesn't mention them.
    existing_columns_to_drop = [col for col in df.columns if 'Unnamed' in col]
    if existing_columns_to_drop:
        df.drop(columns=existing_columns_to_drop, inplace=True)
        print(f"Dropped columns: {existing_columns_to_drop}")

    df.dropna(subset=[RELEVANT_COLUMN_TO_EMBED, LINK_COLUMN], inplace=True)
    df.reset_index(drop=True, inplace=True)
    if df.empty:
        print("No valid data after filtering. Please check your CSV.")
        return False
    print(f"Data after filtering NAs: {len(df)} rows")

    report_data_list = df.to_dict(orient='records')
    with open(REPORT_DATA_STORE_PATH, 'w') as f:
        json.dump(report_data_list, f, indent=4)
    print(f"Report data store saved to {REPORT_DATA_STORE_PATH}")

    print("Generating embeddings via Gemini API (SDK) (this may take a while)...")
    embeddings = []
    valid_indices_for_faiss = [] # Keep track of rows for which embedding was successful

    for i, row in df.iterrows():
        description = row[RELEVANT_COLUMN_TO_EMBED]
        # Ensure description is a string and not NaN or other non-string types
        if not isinstance(description, str):
            logger.warning(f"Document index {i} has non-string description ('{description}'). Skipping.")
            print(f"Skipping document index {i} due to non-string description: '{description}'")
            continue
            
        print(f"Embedding document {i+1}/{len(df)}...")
        logger.info(f"Processing document index {i} for embedding.") # Log the index being processed
        emb = get_embedding_gemini(description, task_type="RETRIEVAL_DOCUMENT") # Specify task_type for indexing
        if emb is not None:
            embeddings.append(emb)
            valid_indices_for_faiss.append(i) # Store original df index
        else:
            print(f"Failed to get embedding for document index {i}. Skipping.")
        time.sleep(0.2)  # Rate limiting: Adjusted for SDK, can be faster. Test for optimal value. Max 60 QPM for free tier.

    if not embeddings:
        print("No embeddings were successfully generated. Exiting.")
        return False
        
    embeddings_np = np.array(embeddings).astype('float32')
    if embeddings_np.ndim == 1: # If only one embedding was generated, it might be flat
        if embeddings: # Check if embeddings list itself is not empty
            embedding_dim = embeddings[0].shape[0] # Get dim from the first (and only) item
            embeddings_np = embeddings_np.reshape(1, embedding_dim) # Reshape to (1, D)
        else: # Should have been caught by "if not embeddings"
            print("Error: Embeddings list is empty but not caught by initial check.")
            return False
    elif embeddings_np.ndim == 2:
        embedding_dim = embeddings_np.shape[1]
    else:
        print(f"Error: Unexpected shape for embeddings_np: {embeddings_np.shape}")
        return False
        
    print(f"Generated {len(embeddings_np)} embeddings with dimension {embedding_dim}.")

    index = faiss.IndexFlatL2(embedding_dim)
    index.add(embeddings_np)
    print(f"FAISS index populated with {index.ntotal} vectors.")
    faiss.write_index(index, FAISS_INDEX_PATH)
    print(f"FAISS index saved to {FAISS_INDEX_PATH}")
    
    # Update report_data_store to only include successfully embedded items,
    # ensuring FAISS indices map correctly to this filtered list.
    filtered_report_data_list = [report_data_list[i] for i in valid_indices_for_faiss]
    with open(REPORT_DATA_STORE_PATH, 'w') as f:
        json.dump(filtered_report_data_list, f, indent=4)
    print(f"Filtered report data store (with {len(filtered_report_data_list)} items) saved to {REPORT_DATA_STORE_PATH}")

    print("Offline indexing pipeline completed successfully.")
    return True

# --- Phase 3: Retrieval & Summarization Service (Simulated) ---
class RetrievalService:
    def __init__(self):
        logger.info("Initializing Retrieval Service with Gemini API (SDK)...")
        
        if not os.path.exists(FAISS_INDEX_PATH):
            logger.error(f"FAISS index not found at {FAISS_INDEX_PATH}. Run indexing first.")
            raise FileNotFoundError(f"FAISS index not found at {FAISS_INDEX_PATH}. Run indexing first.")
        self.index = faiss.read_index(FAISS_INDEX_PATH)
        logger.info(f"FAISS index loaded with {self.index.ntotal} vectors.")

        if not os.path.exists(REPORT_DATA_STORE_PATH):
            logger.error(f"Report data store not found at {REPORT_DATA_STORE_PATH}. Run indexing first.")
            raise FileNotFoundError(f"Report data store not found at {REPORT_DATA_STORE_PATH}. Run indexing first.")
        with open(REPORT_DATA_STORE_PATH, 'r') as f:
            self.report_data_store = json.load(f)
        logger.info(f"Report data store loaded with {len(self.report_data_store)} entries.")
        logger.info("Retrieval Service initialized.")

    def search_reports(self, user_query, top_k=3):
        """
        Finds the most relevant report(s) based on the user query.
        Returns a list of dictionaries, each containing report details including similarity distance.
        """
        logger.info(f"\nProcessing query: '{user_query}'")

        # Use RETRIEVAL_QUERY for user queries
        query_embedding = get_embedding_gemini(user_query, task_type="RETRIEVAL_QUERY")
        if query_embedding is None:
            logger.warning("Failed to generate embedding for the query.")
            # Return a structure indicating failure, consistent with expected output
            return [{"error": "Could not process query due to embedding failure."}]
        
        query_embedding_np = np.array([query_embedding]).astype('float32')

        logger.info(f"Searching FAISS index for top {top_k} results...")
        distances, indices = self.index.search(query_embedding_np, top_k)

        results = []
        if indices.size == 0 or (indices.ndim > 1 and indices[0].size > 0 and indices[0][0] == -1) or (indices.ndim == 1 and indices.size > 0 and indices[0] == -1) :
            logger.info("No relevant documents found in FAISS index.")
            return [{"message": "No relevant reports found for your query."}]

        for i in range(len(indices[0])): # Assuming indices is 2D: (num_queries, top_k)
            doc_index = indices[0][i]
            if doc_index < 0 or doc_index >= len(self.report_data_store):
                logger.warning(f"Warning: FAISS returned invalid index {doc_index}. Skipping.")
                continue
            
            retrieved_doc = self.report_data_store[doc_index]
            distance = distances[0][i]

            report_details = { # Updated keys to match new CSV columns
                "name": retrieved_doc.get("name", "N/A"),
                "Report Description": retrieved_doc.get(RELEVANT_COLUMN_TO_EMBED, "N/A"),
                "Metrics Captured": retrieved_doc.get("Metrics Captured", "N/A"),
                "Relevant Links": retrieved_doc.get(LINK_COLUMN, "N/A"),
                "Data Partner": retrieved_doc.get("Data Partner", "N/A"), # Added Data Partner
                "similarity_distance": float(distance)
            }
            results.append(report_details)
            logger.info(f"Retrieved document index: {doc_index}, Report: '{report_details['name']}', Distance: {distance:.4f}")
            
        if not results: # Should be caught by the earlier check on indices, but as a safeguard
            logger.info("No results formulated after processing FAISS indices.")
            return [{"message": "No relevant reports found after processing."}]
            
        return results

# --- Main Execution (for testing retrieval) ---
if __name__ == "__main__":
    if not GEMINI_API_KEY:
        logger.critical("Gemini API Key is not configured. Please set the GEMINI_API_KEY environment variable.")
        logger.critical("The script cannot proceed without API key for query embedding.")
    else:
        try:
            # Initialize the service
            retrieval_service = RetrievalService()

            # Example User Query 1
            user_query_text_1 = "I need a report about merchant activities in East Africa"
            retrieved_info_1 = retrieval_service.search_reports(user_query_text_1, top_k=1)

            print(f"\n--- Top Result for query: '{user_query_text_1}' ---")
            if retrieved_info_1:
                for item in retrieved_info_1:
                    if "error" in item or "message" in item:
                        print(f"Notification: {item.get('error') or item.get('message')}")
                    else:
                        print(f"  Report Name: {item.get('name')}") # Updated key
                        print(f"  Description: {item.get('Report Description')}")
                        print(f"  Metrics: {item.get('Metrics Captured')}")
                        print(f"  Data Partner: {item.get('Data Partner')}") # Added Data Partner
                        print(f"  Link: {item.get('Relevant Links')}")
                        if 'similarity_distance' in item:
                             print(f"  Similarity Distance: {item['similarity_distance']:.4f}")
                        print("---------------------------------")
            else: # Should not happen if error/message is returned
                print("No information retrieved for the query.")

        except FileNotFoundError as e:
            logger.error(f"A required file was not found: {e}. Please ensure FAISS index and JSON data store exist.")
            print(f"Error: {e}. Please ensure '{FAISS_INDEX_PATH}' and '{REPORT_DATA_STORE_PATH}' exist.")
            print("You might need to run the indexing script (e.g., 'python gemini_faiss_retriever.py' or 'python run_embedding.py') first.")
        except Exception as e:
            logger.error(f"An unexpected error occurred during execution: {e}", exc_info=True)
            print(f"An unexpected error occurred: {e}")