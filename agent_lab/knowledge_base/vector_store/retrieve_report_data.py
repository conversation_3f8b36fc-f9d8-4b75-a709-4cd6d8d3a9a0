import faiss
import json
import numpy as np
import os
import logging
from dotenv import load_dotenv
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions

# --- Basic Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Environment and API Key Setup ---
logger.debug("Loading environment variables from .env file.")
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        logger.info("Google Generative AI SDK configured successfully.")
    except Exception as e:
        logger.critical(f"Failed to configure Google Generative AI SDK: {e}", exc_info=True)
        # Depending on the desired behavior, you might want to exit or raise an error here
        # For now, it will allow the script to continue but embedding will fail.
else:
    logger.warning("GEMINI_API_KEY not found in environment variables.")

# --- Constants ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
FAISS_INDEX_PATH = os.path.join(SCRIPT_DIR, "report_index_gemini.faiss")
REPORT_DATA_STORE_PATH = os.path.join(SCRIPT_DIR, "report_data_gemini.json")
EMBEDDING_API_MODEL = "text-embedding-004" # Gemini model for embeddings

# --- Helper Function for Embeddings ---
def get_embedding_gemini(text_to_embed):
    """Generates an embedding for a given text using the Gemini Embedding API."""
    if not text_to_embed or not isinstance(text_to_embed, str) or not text_to_embed.strip():
        logger.warning("Empty or invalid text provided for embedding. Returning None.")
        return None

    if not GEMINI_API_KEY: # Check if API key was loaded and genai configured
        logger.error("GEMINI_API_KEY is missing or SDK not configured. Cannot generate embedding.")
        return None
    
    try:
        logger.debug(f"Attempting to get embedding for text (first 100 chars): '{str(text_to_embed)[:100]}...'")
        result = genai.embed_content(
            model=f"models/{EMBEDDING_API_MODEL}",
            content=text_to_embed,
            task_type="RETRIEVAL_QUERY" # Use RETRIEVAL_QUERY for user queries
        )
        
        if "embedding" in result:
            logger.debug(f"Successfully generated embedding for text (first 100 chars): '{str(text_to_embed)[:100]}...'")
            return np.array(result["embedding"])
        else:
            logger.error(f"Embedding not found in SDK response for text (first 100 chars): '{str(text_to_embed)[:100]}...'. Response: {result}")
            return None
    except google_exceptions.GoogleAPIError as e:
        logger.error(f"Google API Error calling Gemini Embedding API for text (first 100 chars): '{str(text_to_embed)[:100]}...'. Error: {e}", exc_info=True)
        return None
    except AttributeError: # This can happen if genai was not configured due to missing API key initially
        logger.error("Google Generative AI SDK (genai) was not properly configured, likely due to a missing API key. Cannot generate embedding.")
        return None
    except Exception as e:
        logger.error(f"An unexpected error occurred in get_embedding_gemini for text '{str(text_to_embed)[:100]}...': {e}", exc_info=True)
        return None

# --- Report Query Service ---
class ReportQueryService:
    def __init__(self, faiss_index_path, report_data_path):
        logger.info("Initializing Report Query Service...")
        self.faiss_index_path = faiss_index_path
        self.report_data_path = report_data_path
        self.index = None
        self.report_data_store = []

        self._load_resources()

    def _load_resources(self):
        """Loads the FAISS index and the JSON report data."""
        try:
            if not os.path.exists(self.faiss_index_path):
                logger.error(f"FAISS index file not found at {self.faiss_index_path}")
                raise FileNotFoundError(f"FAISS index file not found at {self.faiss_index_path}")
            self.index = faiss.read_index(self.faiss_index_path)
            logger.info(f"FAISS index loaded successfully from {self.faiss_index_path} with {self.index.ntotal} vectors.")
        except Exception as e:
            logger.error(f"Failed to load FAISS index: {e}", exc_info=True)
            raise

        try:
            if not os.path.exists(self.report_data_path):
                logger.error(f"Report data JSON file not found at {self.report_data_path}")
                raise FileNotFoundError(f"Report data JSON file not found at {self.report_data_path}")
            with open(self.report_data_path, 'r', encoding='utf-8') as f:
                self.report_data_store = json.load(f)
            logger.info(f"Report data store loaded successfully from {self.report_data_path} with {len(self.report_data_store)} entries.")
        except Exception as e:
            logger.error(f"Failed to load report data store: {e}", exc_info=True)
            raise
        
        if self.index and self.report_data_store and self.index.ntotal != len(self.report_data_store):
            logger.warning(
                f"Mismatch between FAISS index size ({self.index.ntotal}) and data store size ({len(self.report_data_store)}). "
                "Ensure they correspond correctly."
            )


    def find_closest_report(self, user_query, top_k=1):
        """
        Finds the most relevant report(s) based on the user query.
        Returns a list of dictionaries, each containing the requested report details.
        """
        if not self.index or not self.report_data_store:
            logger.error("FAISS index or report data store not loaded. Cannot perform search.")
            return [{"error": "Search resources not available."}]

        logger.info(f"Processing query: '{user_query}'")
        query_embedding = get_embedding_gemini(user_query)

        if query_embedding is None:
            logger.warning("Failed to generate embedding for the query.")
            return [{"error": "Could not process query due to embedding failure."}]
        
        query_embedding_np = np.array([query_embedding]).astype('float32')

        logger.info(f"Searching FAISS index for top {top_k} result(s)...")
        try:
            distances, indices = self.index.search(query_embedding_np, top_k)
        except Exception as e:
            logger.error(f"Error during FAISS search: {e}", exc_info=True)
            return [{"error": "Failed to search the FAISS index."}]

        results = []
        if indices.size == 0 or (indices.ndim > 0 and indices[0].size > 0 and indices[0][0] == -1): # Check for no results
            logger.info("No relevant documents found in FAISS index for the query.")
            return [{"message": "No relevant reports found for your query."}]

        for i in range(len(indices[0])): # indices[0] because we search for one query vector
            doc_index = indices[0][i]
            distance = distances[0][i]

            if 0 <= doc_index < len(self.report_data_store):
                retrieved_doc = self.report_data_store[doc_index]
                report_details = {
                    "Report": retrieved_doc.get("Report", "N/A"),
                    "Report Description": retrieved_doc.get("Report Description", "N/A"),
                    "Metrics Captured": retrieved_doc.get("Metrics Captured", "N/A"),
                    "Relevant Links": retrieved_doc.get("Relevant Links", "N/A"),
                    "similarity_distance": float(distance) # Optional: include distance
                }
                results.append(report_details)
                logger.info(f"Retrieved document index: {doc_index}, Report: '{report_details['Report']}', Distance: {distance:.4f}")
            else:
                logger.warning(f"FAISS returned an invalid index: {doc_index}. Skipping.")
        
        if not results:
             return [{"message": "No relevant reports found after processing search results."}]
        return results

# --- Main Execution ---
if __name__ == "__main__":
    if not GEMINI_API_KEY:
        logger.critical("Gemini API Key is not configured. Please set the GEMINI_API_KEY environment variable.")
        logger.critical("The script cannot proceed without API key for query embedding.")
    else:
        try:
            # Initialize the service
            query_service = ReportQueryService(
                faiss_index_path=FAISS_INDEX_PATH,
                report_data_path=REPORT_DATA_STORE_PATH
            )

            # Example User Query
            user_query_text = "I need a report about merchant activities in East Africa"
            
            # Find the closest report (top_k=1 by default)
            retrieved_info = query_service.find_closest_report(user_query_text)

            # Print the results
            if retrieved_info:
                for item in retrieved_info:
                    if "error" in item or "message" in item:
                        print(f"\nNotification: {item.get('error') or item.get('message')}")
                    else:
                        print("\n--- Retrieved Report Details ---")
                        print(f"  Report Name: {item.get('Report')}")
                        print(f"  Description: {item.get('Report Description')}")
                        print(f"  Metrics: {item.get('Metrics Captured')}")
                        print(f"  Link: {item.get('Relevant Links')}")
                        if 'similarity_distance' in item:
                             print(f"  Similarity Distance: {item['similarity_distance']:.4f}") # Lower is better for L2 distance
                        print("---------------------------------")
            else:
                print("No information retrieved for the query.")

            # Example with top_k=1 (modified from the original top_k=2 example)
            user_query_text_2 = "summary of partner transactions"
            retrieved_info_2 = query_service.find_closest_report(user_query_text_2, top_k=1) # Ensure top_k is 1
            if retrieved_info_2:
                print(f"\n--- Top Result for query: '{user_query_text_2}' ---")
                # Since top_k=1, retrieved_info_2 will have at most one item.
                # Access the first (and only) item directly.
                item = retrieved_info_2[0] 
                if "error" in item or "message" in item:
                    print(f"\nNotification: {item.get('error') or item.get('message')}")
                else:
                    print(f"  Report Name: {item.get('Report')}")
                    print(f"  Description: {item.get('Report Description')}") # Added Description
                    print(f"  Metrics: {item.get('Metrics Captured')}") # Added Metrics
                    print(f"  Link: {item.get('Relevant Links')}")
                    if 'similarity_distance' in item:
                         print(f"  Similarity Distance: {item['similarity_distance']:.4f}")
                    print("---------------------------------") # Consistent formatting
        except FileNotFoundError as e:
            logger.error(f"A required file was not found: {e}. Please ensure FAISS index and JSON data store exist.")
            print(f"Error: {e}. Please ensure '{FAISS_INDEX_PATH}' and '{REPORT_DATA_STORE_PATH}' exist.")
            print("You might need to run the indexing script (like the one in 'gemini_faiss_retriever.py' or 'run_embedding.py') first.")
        except Exception as e:
            logger.error(f"An unexpected error occurred during execution: {e}", exc_info=True)
            print(f"An unexpected error occurred: {e}")
