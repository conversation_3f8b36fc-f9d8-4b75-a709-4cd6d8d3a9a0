import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# Define scraped_links_list at the module level so it can be imported by other modules.
scraped_links_list = []

def scrape_links(start_url, max_depth=1):
    """
    Scrapes links from a given URL and its sub-pages up to a specified depth.

    Args:
        start_url (str): The URL to begin scraping from.
        max_depth (int): The maximum depth of sub-links to follow.
                         0 means only scrape the start_url.
                         1 means scrape start_url and its direct links, etc.

    Returns:
        list: A sorted list of unique absolute URLs found, or None if an error occurs.
    """
    all_found_links = set()
    visited_urls = set()
    queue = [(start_url, 0)]
    base_domain = urlparse(start_url).netloc

    print(f"\nStarting scrape from: {start_url}")
    print(f"Maximum depth: {max_depth}")
    print(f"Scraping links from domain: {base_domain}\n")

    while queue:
        current_url, current_depth = queue.pop(0)

        if current_url in visited_urls or current_depth > max_depth:
            continue

        print(f"Scraping (Depth {current_depth}): {current_url}")
        visited_urls.add(current_url)

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(current_url, headers=headers, timeout=10)
            response.raise_for_status()  # Raise an exception for HTTP errors

            soup = BeautifulSoup(response.text, 'html.parser')

            for link_tag in soup.find_all('a', href=True):
                href = link_tag['href']
                absolute_url = urljoin(current_url, href)
                
                # Clean the URL by removing fragment identifiers
                parsed_url = urlparse(absolute_url)
                cleaned_url = parsed_url._replace(fragment="").geturl()

                # Only add the URL if it's within the same domain
                if urlparse(cleaned_url).netloc == base_domain:
                    all_found_links.add(cleaned_url)
                    # Add to the queue for further scraping if within depth limits
                    if cleaned_url not in visited_urls:
                        queue.append((cleaned_url, current_depth + 1))

        except requests.exceptions.RequestException as e:
            print(f"Error fetching {current_url}: {e}")
            # In case of a critical error on the start_url, return None
            if current_url == start_url:
                return None
        except Exception as e:
            print(f"An unexpected error occurred while processing {current_url}: {e}")

    return sorted(list(all_found_links))

def main():
    """
    Main function to run the link scraping process interactively.
    It prompts the user for inputs, calls the scraper, and updates
    the global list of scraped links for other modules to use.
    """
    session_scraped_links = set()
    successfully_scraped_count = 0
    unsuccessfully_scraped_count = 0

    while True:
        main_website_url = input("\nEnter the full website URL to scrape (e.g., http://example.com): ")
        if not main_website_url:
            print("URL cannot be empty.")
            continue

        try:
            depth_level = int(input("Enter the scraping depth (e.g., 0 for the page only, 1 for its links, etc.): "))
            if depth_level < 0:
                print("Depth must be a non-negative integer.")
                continue
        except ValueError:
            print("Invalid input. Please enter an integer for the depth.")
            continue

        # Execute the scraping operation for the given URL
        scraped_from_op = scrape_links(main_website_url, depth_level)

        if scraped_from_op is not None:
            print(f"\nSuccessfully found {len(scraped_from_op)} unique links for {main_website_url}.")
            session_scraped_links.update(scraped_from_op)
            successfully_scraped_count += 1
        else:
            print(f"\nFailed to scrape the initial URL: {main_website_url}.")
            unsuccessfully_scraped_count += 1

        another_op = input("\nDo you want to scrape another site? (yes/no): ").lower()
        if another_op != 'yes':
            break

    # --- Update the global list ---
    # This is the crucial step for making the links available to other modules.
    global scraped_links_list
    scraped_links_list.clear()
    scraped_links_list.extend(sorted(list(session_scraped_links)))

    # --- Summary ---
    grand_total_attempted = successfully_scraped_count + unsuccessfully_scraped_count
    summary_tuple = (len(scraped_links_list), successfully_scraped_count, unsuccessfully_scraped_count, grand_total_attempted)

    print("\n--- Overall Scraping Summary ---")
    print(f"Total unique links collected in this session: {summary_tuple[0]}")
    print(f"Successful scraping operations: {summary_tuple[1]}")
    print(f"Unsuccessful scraping operations: {summary_tuple[2]}")
    print(f"Grand total of scraping attempts: {summary_tuple[3]}")

    if scraped_links_list:
        print("\n--- All Unique Links Collected This Session ---")
        for i, link in enumerate(scraped_links_list, 1):
            print(f"{i}. {link}")
    else:
        print("\nNo links were collected in total.")

    return scraped_links_list, summary_tuple

if __name__ == "__main__":
    # When this script is run directly, it executes the main interactive session.
    # The results populate the `scraped_links_list`.
    links, summary = main()

    # To verify that the global list is populated and accessible after main() runs:
    print("\n--- Verification ---")
    print(f"The module-level 'scraped_links_list' now contains {len(scraped_links_list)} links.")
    print("This list is now ready to be imported and used by other modules.")