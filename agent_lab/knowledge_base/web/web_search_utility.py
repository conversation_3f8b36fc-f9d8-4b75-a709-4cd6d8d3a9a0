import asyncio
import aiohttp
from bs4 import BeautifulSoup
import logging
import os
import re # For regular expressions
from collections import deque
from urllib.parse import urlparse, urljoin, parse_qs, unquote # For URL manipulation
from typing import Dict, List, Union, Optional, Tuple

try:
    import google.generativeai as genai
    # Ensure the user has a new enough version of google-generativeai
    if not hasattr(genai.types, 'GenerationConfig'): # A simple check for a relatively new feature
        logging.warning("A newer version of google-generativeai might be required for all features.")
except ImportError:
    genai = None
    logging.error("google.generativeai library is not installed. This script requires it to function.")
    # You could raise an error here or allow the script to load if it's part of a larger system
    # where genai might be conditionally used. For this script's core purpose, it's essential.
    # raise ImportError("google.generativeai is required but not installed.")

# Import for Google Custom Search
try:
    from googleapiclient.discovery import build
    from google.auth.exceptions import DefaultCredentialsError
except ImportError:
    build = None # Placeholder if library not installed
    DefaultCredentialsError = None # Placeholder
    logging.error("google-api-python-client is not installed. Google Custom Search will not function.")

# Configure basic logging
if not logging.getLogger().hasHandlers():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
logger = logging.getLogger(__name__)

# Configuration Constants
DEFAULT_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' # Updated to a slightly newer version
DEFAULT_TIMEOUT_SECONDS = 15
MAX_CONTENT_LENGTH_FOR_LLM_ANALYSIS = 12000 # Max characters of page content for relevance analysis (already updated in a previous step)
MAX_CHUNK_SIZE_FOR_SYNTHESIS = 150000 
MAX_SEARCH_RESULTS_TO_EXTRACT = 7 # How many result links to parse from the search engine
MAX_SEARCH_RESULTS_TO_PROCESS_CONTENT = 5  # How many of those links to fetch content for and analyze

# Google Custom Search Configuration (load from environment variables or config)
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY") # Your Google API Key
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID")   # Your Custom Search Engine ID



async def fetch_page_content(
    session: aiohttp.ClientSession,
    site_url: str,
    headers: Dict[str, str],
    ssl_verify: bool,
    timeout: int,
) -> Tuple[Optional[str], List[str]]:
    """
    Asynchronously fetches content from a single URL.

    Args:
        session: The aiohttp client session.
        site_url: The URL to fetch.
        headers: HTTP headers to use for the request.
        ssl_verify: Whether to verify SSL certificates.
        timeout: Request timeout in seconds.

    Returns:
        A tuple containing:
        - The processed text content of the page (or None if an error).
        - A list of unique absolute HTTP/HTTPS links found on the page.
    """
    found_links: List[str] = []
    try:
        logger.debug(f"Fetching: {site_url}")
        async with session.get(site_url, headers=headers, ssl=ssl_verify, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
            response.raise_for_status()
            content_type = response.headers.get('Content-Type', '').lower()
            if 'text' not in content_type:
                logger.warning(f"Skipping non-text content at {site_url} (Content-Type: {content_type})")
                return None, []
            # Let aiohttp's response.text() handle encoding detection.
            # It uses Content-Type, then chardet if available (if installed), then fallback.
            try:
                html_content = await response.text(errors='replace')
            except UnicodeDecodeError as ude: # More specific error for fallback
                logger.warning(f"UnicodeDecodeError with auto-detected encoding for {site_url}: {ude}. Falling back to raw read with utf-8.")
                raw_content = await response.read()
                html_content = raw_content.decode('utf-8', errors='replace')
            except Exception as enc_ex:
                logger.warning(f"Generic encoding/text extraction issue for {site_url}: {enc_ex}. Falling back to raw read with utf-8.")
                raw_content = await response.read()
                html_content = raw_content.decode('utf-8', errors='replace')


            soup = BeautifulSoup(html_content, 'html.parser')
            # Remove script and style elements
            for script_or_style in soup(["script", "style"]):
                script_or_style.decompose()
            page_text = soup.get_text(separator=' ', strip=True)

            # Extract links
            base_url_parts = urlparse(site_url)
            # base_domain = f"{base_url_parts.scheme}://{base_url_parts.netloc}" # Not used here, but good for context
            for link_tag in soup.find_all('a', href=True):
                href = link_tag['href']
                if href.startswith('#') or href.lower().startswith('javascript:'): # Skip anchors and JS links
                    continue
                full_url = urljoin(site_url, href)
                parsed_full_url = urlparse(full_url)
                if parsed_full_url.scheme in ['http', 'https']:
                    found_links.append(full_url)
            
            # Regex to extract longer paragraphs or blocks of text.
            # This aims to filter out short navigation links, footers, etc.
            # It looks for sequences of at least 15 words (approx 15 non-space groups).
            # This is a heuristic and might need tuning.
            extracted_blocks = re.findall(r'\b(?:\S+\s+){14,}\S+\b', page_text)
            if extracted_blocks:
                page_text_for_analysis = "\n\n".join(extracted_blocks)
                logger.debug(f"Reduced content length from {len(page_text)} to {len(page_text_for_analysis)} using regex for {site_url}")
            else:
                page_text_for_analysis = page_text # Fallback to original if regex yields nothing
                logger.debug(f"Regex found no distinct blocks for {site_url}, using full text (length {len(page_text)}).")

            logger.info(f"Successfully fetched and parsed {site_url} (Original Length: {len(page_text)}, Processed Length: {len(page_text_for_analysis)}, Links Found: {len(found_links)})")
            return page_text_for_analysis, list(set(found_links)) # Return unique links

    except aiohttp.ClientResponseError as e:
        logger.warning(f"HTTP error {e.status} for {site_url}: {e.message}")
    except aiohttp.ClientConnectionError as e:
        logger.warning(f"Connection error for {site_url}: {e}")
    except asyncio.TimeoutError:
        logger.warning(f"Timeout while trying to access {site_url}.")
    except Exception as e:
        logger.error(f"An unexpected error occurred while fetching {site_url}: {e}", exc_info=True)
    return None, []

async def fetch_google_custom_search_results(
    query: str,
    api_key: Optional[str],
    cse_id: Optional[str],
    max_results_to_extract: int = MAX_SEARCH_RESULTS_TO_EXTRACT
) -> List[str]:
    """
    Fetches search results from Google Custom Search API.
    This function runs synchronously due to the google-api-python-client library.
    It's wrapped in an async function to be callable with await,
    but the underlying API call is blocking.

    Args:
        query: The search query.
        api_key: Google API Key for Custom Search.
        cse_id: Google Custom Search Engine ID.
        max_results_to_extract: Maximum number of search result links to extract.

    Returns:
        A list of extracted URLs from search results.
    """
    if not build:
        logger.error("Google API client library not available. Cannot perform Google Custom Search.")
        return []
    if not api_key or not cse_id:
        logger.error("Google API Key or CSE ID is missing. Cannot perform Google Custom Search.")
        return []

    urls: List[str] = []
    try:
        # The Google API client is synchronous, so we run it in a thread pool
        # to avoid blocking the asyncio event loop if this function is called
        # from an async context. For simplicity here, we'll call it directly,
        # but in a larger async application, consider `loop.run_in_executor`.
        # For now, this function itself is async, but the `build` call is sync.
        # This is a common pattern when integrating sync libraries into async code.
        # The actual network call happens in `execute()`.
        
        # Add "Flutterwave" to the query to restrict search results if desired
        # For Google CSE, this is often better handled by CSE configuration itself.
        # If you want to programmatically add it:
        # restricted_query = f"{query} site:flutterwave.com OR site:developer.flutterwave.com" 
        # Or simply: restricted_query = f"{query} Flutterwave"
        # For now, we'll use the query as is, assuming CSE is configured for Flutterwave.
        logger.info(f"Fetching Google Custom Search results for: {query}")
        service = build("customsearch", "v1", developerKey=api_key)
        # Request up to `max_results_to_extract` (Google CSE API `num` parameter is 1-10)
        num_to_request = min(max_results_to_extract, 10)
        res = await asyncio.to_thread(service.cse().list(q=query, cx=cse_id, num=num_to_request).execute)
        
        search_items = res.get('items', [])
        for item in search_items:
            if 'link' in item:
                urls.append(item['link'])
        logger.info(f"Found {len(urls)} search result links from Google Custom Search for query '{query}'.")
    except DefaultCredentialsError:
        logger.error("Google Custom Search: Default credentials error. Ensure GOOGLE_APPLICATION_CREDENTIALS is set or API key is valid.")
    except Exception as e:
        logger.error(f"Error fetching or parsing Google Custom Search results for query '{query}': {e}", exc_info=True)
    return urls

async def analyze_content_relevance_and_extract(
    llm: 'genai.GenerativeModel',
    query: str,
    page_content: str,
    site_url: str,
    max_length: int = MAX_CONTENT_LENGTH_FOR_LLM_ANALYSIS
) -> Optional[Dict[str, str]]:
    """
    Uses an LLM to analyze page content for relevance to the query and extract key information.

    Args:
        llm: An initialized instance of google.generativeai.GenerativeModel.
        query: The user's query.
        page_content: The text content of the web page.
        site_url: The URL of the web page (for context).
        max_length: Maximum characters of page content to send for analysis.

    Returns:
        A dictionary with "url" and "extracted_info" if relevant, otherwise None.
    """
    if not llm:
        logger.error("LLM not available for content analysis.")
        return None
    if not page_content:
        logger.debug(f"No content to analyze for {site_url}")
        return None

    content_snippet = page_content[:max_length]

    prompt = f"""
User Query: "{query}"

Source URL: {site_url}
Page Content Snippet (potentially pre-filtered by regex, up to {max_length} characters for LLM):
---
{content_snippet}
---

Instructions:
1.  Carefully review the Page Content Snippet in relation to the User Query.
2.  **Prioritize Flutterwave Relevance**: Determine if the snippet contains substantial information that DIRECTLY and meaningfully answers or addresses the User Query, *with a strong emphasis on relevance to Flutterwave*.
3.  Do not consider tangential or vaguely related information, especially if it's not about Flutterwave.
3.  If relevant and contains answerable information:
    a.  Respond with "Relevance: Yes" on the first line.
    b.  On subsequent lines, provide "Extracted Information:", followed by a concise summary of the key facts, figures, or statements from the snippet that directly answer the User Query. Focus on extracting factual information.
5.  If not relevant (especially if not Flutterwave-related or doesn't answer the query):
    a.  Respond with "Relevance: No" on the first line.
    b.  On the second line, provide "Extracted Information: No substantial information found on this page to answer the query."

Provide your response strictly in the format specified.
Focus on Flutterwave-related information.
"""
    try:
        logger.debug(f"Sending content from {site_url} to LLM for analysis. Query: '{query}'. Snippet length: {len(content_snippet)}")
        # Safety settings can be configured here if needed
        # Using a slightly lower temperature for more deterministic relevance assessment.
        analysis_generation_config = genai.types.GenerationConfig(temperature=0.2)
        response = await llm.generate_content_async(
            prompt,
            generation_config=analysis_generation_config)

        if not response.parts:
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                logger.warning(f"LLM analysis for {site_url} blocked: {response.prompt_feedback.block_reason_message}")
            else:
                logger.warning(f"LLM analysis for {site_url} returned no parts and no block reason.")
            return None

        analysis_text = response.text.strip()
        logger.info(f"LLM analysis for {site_url} completed. Raw LLM output (first 100 chars): {analysis_text[:100]}")

        # More robust parsing for relevance and extracted information
        relevance_marker_yes = "relevance: yes"
        extracted_info_marker = "extracted information:"
        analysis_text_lower = analysis_text.lower()

        if relevance_marker_yes in analysis_text_lower:
            extracted_info_start_index = analysis_text_lower.find(extracted_info_marker)
            
            if extracted_info_start_index != -1:
                # Get the text after "extracted information:"
                extracted_info = analysis_text[extracted_info_start_index + len(extracted_info_marker):].strip()
                
                # Check if extracted_info is meaningful
                if extracted_info and extracted_info.lower() != "no substantial information found on this page to answer the query." and extracted_info.lower() != "no substantial information found on this page to answer the query, or the content is not relevant to flutterwave.":
                    logger.info(f"Relevant information extracted by LLM from {site_url}")
                    return {"url": site_url, "info": extracted_info}
                else:
                    # Handles "Relevance: Yes" but "Extracted Information: No substantial..." or empty extracted_info
                    logger.info(f"LLM deemed relevant for {site_url} but extracted no substantial information (or empty). Treating as not providing useful info.")
            else:
                # "Relevance: Yes" was found, but "Extracted Information:" marker is missing.
                logger.warning(f"LLM deemed {site_url} relevant, but the '{extracted_info_marker}' marker is missing or malformed in response: {analysis_text}")
        else:
            # "relevance: no" or other non-yes relevance indication
            logger.info(f"LLM deemed {site_url} not relevant for the query (or relevance not explicitly 'yes').")
    except google.api_core.exceptions.ResourceExhausted as r_exc: # Specifically catch rate limit errors
        logger.error(f"ResourceExhausted (Rate Limit) during LLM analysis for {site_url}: {r_exc}", exc_info=False) # exc_info=False to reduce noise for this specific error
    except Exception as e: # General errors
        logger.error(f"Error during LLM analysis for {site_url}: {e}", exc_info=True)
    return None


async def synthesize_answer_with_llm(
    llm: 'genai.GenerativeModel',
    query: str,
    collected_info: List[Dict[str, str]]
) -> str:
    """
    Synthesizes a natural language answer from collected information using an LLM.

    Args:
        llm: An initialized instance of google.generativeai.GenerativeModel.
        query: The user's original query.
        collected_info: A list of dictionaries, each with "url" and "info".

    Returns:
        A natural language answer.
    """
    if not llm:
        return "Error: LLM not available for synthesizing an answer."
    if not collected_info:
        return f"I couldn't find any specific information to answer your query: '{query}' from the provided websites."

    # Chunking collected_info to avoid overly long prompts
    # This is a simple way to ensure the prompt doesn't get too big.
    # More sophisticated summarization/chunking could be done if needed.
    formatted_info_parts = []
    current_chunk_length = 0
    current_chunk_content = ""

    for item in collected_info:
        item_text = f"Source: {item['url']}\nContent:\n{item['info']}\n\n"
        if current_chunk_length + len(item_text) > MAX_CHUNK_SIZE_FOR_SYNTHESIS and current_chunk_content:
            formatted_info_parts.append(current_chunk_content)
            current_chunk_content = ""
            current_chunk_length = 0
        current_chunk_content += item_text
        current_chunk_length += len(item_text)

    if current_chunk_content:
        formatted_info_parts.append(current_chunk_content)

    # For simplicity, we'll just use the first chunk if it's too much,
    # or you could process chunks iteratively (more complex).
    # A more robust approach might be to summarize each chunk first, then summarize summaries.
    formatted_collected_info = formatted_info_parts[0] # Using the first chunk
    if len(formatted_info_parts) > 1:
        logger.warning(f"Collected information was too long for a single synthesis prompt ({len(formatted_info_parts)} chunks). Using the first chunk only.")


    prompt = f"""
User Query: "{query}"

Collected Information:
I have gathered the following information from various web sources. Each piece of information is clearly marked with its source URL.
---
{formatted_collected_info}
---

Your Task: Synthesize a Comprehensive Answer with Accurate Citations.

Instructions:
1.  **Answer from Provided Information**: Base your answer *exclusively* on the "Collected Information". Do NOT use any external knowledge or make assumptions.
2.  **Mandatory Inline Citations**: For *every* piece of information used from the "Collected Information", provide an inline citation *immediately* after that piece of information.
    *   **Citation Format**: `(Source: <[URL_FROM_COLLECTED_INFO]|Source>)`
    *   **Example**: "Flutterwave was founded in 2016 (Source: <http://example.com/data|Source>)."
3.  **Multiple Sources**: If a single statement is supported by multiple sources, cite all relevant URLs. Example: "...supported by findings (Source: URL1) and further data (Source: URL2)."
4.  **Structure and Formatting (Slack Mrkdwn)**: Your response should be well-organized and easy to read.
    *   For bulleted lists: Each item *must* begin with an (`•`) followed immediately by a single standard space character, and then the item's text (e.g., `• This is a bullet point.`). Each bullet item must be on its own new line.
    *   Headings: You can use bold text for headings if appropriate (e.g., `*Key Findings*`).
    *   Emphasis: Use bolding (`*text*`) for emphasis on key terms.
    *   Newlines for lists: If a list is preceded by introductory text (e.g., "Key points include:"), ensure there is a literal newline character (`\\n`) between that text and the first bullet item.
    *   Overall: Ensure the response is clear, concise, and directly addresses the User Query.
    *   Do NOT use markdown for the `(Source: URL)` citations themselves; they should be plain text within parentheses.
5.  **Completeness and Flutterwave Focus**: If the information allows for a direct answer (especially concerning Flutterwave), provide it with citations. If the information is incomplete or not Flutterwave-related, answer what you can (with citations) and clearly state any limitations based *only* on the provided information.
6.  **Relevance to Flutterwave**: If the User Query is about Flutterwave, prioritize information directly related to Flutterwave. If the query is general, provide a general answer.
7.  **Sensitive Information**: Be mindful of sensitive information (PII, confidential business details) that might inadvertently appear in web search snippets. Avoid directly quoting or emphasizing such information. If crucial to the answer, paraphrase or refer to it generically.
8.  **Tone**: Present the answer in a helpful, direct, and professional tone, as if you are directly answering the user. Do not refer to "the snippets" or "the text provided" in your narrative, only in citations.
9.  **Insufficient Information**: If the "Collected Information" is ultimately insufficient to form a meaningful answer to the User Query, respond with a polite message like: "I found some information related to your query, but it wasn't specific enough to provide a direct answer. Here's what I could gather: [briefly mention any tangentially related info found, with citations, if any, otherwise omit this part]. You might want to try rephrasing your query or asking something more specific." If absolutely nothing relevant was found in the collected info, state: "I searched the web but couldn't find specific information to answer your query: '[User Query]'."
10. **Concluding Remark**: If you provide a substantive answer, end with a polite closing, like "I hope this helps!" or "Let me know if you have more questions about this."

Begin Final Answer:
"""
    try:
        logger.debug(f"Sending {len(collected_info)} pieces of info to LLM for synthesis. Query: '{query}'. Info length: {len(formatted_collected_info)}")
        # Use a lower temperature for more factual and instruction-following synthesis
        synthesis_generation_config = genai.types.GenerationConfig(temperature=0.2)
        response = await llm.generate_content_async(
            prompt,
            generation_config=synthesis_generation_config)

        if not response.parts:
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                 logger.warning(f"LLM synthesis blocked: {response.prompt_feedback.block_reason_message}")
                 return f"Could not synthesize an answer due to content restrictions (Reason: {response.prompt_feedback.block_reason_message}). The raw information found was extensive but could not be processed into a final response."
            else:
                logger.warning("LLM synthesis returned no parts and no block reason.")
            return f"I found some information related to '{query}', but encountered an issue while trying to synthesize a final answer."

        final_answer = response.text.strip()
        logger.info("LLM synthesis completed.")
        return final_answer

    except Exception as e:
        logger.error(f"Error during LLM synthesis: {e}", exc_info=True)
        return f"An error occurred while trying to synthesize an answer for '{query}'. Some information may have been found but could not be processed."


async def search_sites_for_answer(
    query: str,
    llm: Optional['genai.GenerativeModel'] = None, # Made LLM optional at this level, but crucial functions will check
    user_agent: str = DEFAULT_USER_AGENT,
    timeout_seconds: int = DEFAULT_TIMEOUT_SECONDS,
    max_results_to_process: int = MAX_SEARCH_RESULTS_TO_PROCESS_CONTENT
) -> Dict[str, Union[str, List[str]]]:
    """
    Asynchronously searches DuckDuckGo for a query, fetches content from top results,
    uses an LLM to find relevant information, and synthesizes a natural language answer. (Now uses Google CSE)
    
    Args:
        query: The question or query string.
        llm: An initialized instance of google.generativeai.GenerativeModel.
             If None, the function will return an error message.
        user_agent: User agent string for HTTP requests.
        timeout_seconds: Timeout for each HTTP request.
        max_results_to_process: Maximum number of search results to fetch content for and analyze.

    Returns:
        A dictionary containing:
        - 'answer': A natural language answer synthesized from the found information.
        - 'sources': A list of URLs from which information was used.
    """
    if not genai or not llm:
        logger.error("LLM (google.generativeai) is not initialized or available. Cannot perform search.")
        return {"answer": "Configuration Error: The search system's AI component is not available.", "sources": []}

    if not query or not query.strip():
        logger.warning("Query is empty. No search performed.")
        return {"answer": "The query was empty, so no search was performed.", "sources": []}

    # SSL verification setting from environment variable
    disable_ssl_verify_env = os.getenv('REQUESTS_DISABLE_SSL_VERIFY', 'false').lower()
    ssl_verify = False if disable_ssl_verify_env == 'true' else True
    if not ssl_verify:
        logger.warning("SSL certificate verification is DISABLED. This is insecure for production.")

    headers = {'User-Agent': user_agent}
    collected_relevant_info: List[Dict[str, str]] = []

    # Delay between LLM calls, e.g., 4 seconds for 15 RPM (60s/15 = 4s)
    # Add a small buffer, so maybe 4.1 or 4.2 seconds.
    delay_between_llm_calls_seconds = 4.2

    async with aiohttp.ClientSession() as session:
        # Step 1: Fetch search results using Google Custom Search
        # Note: GOOGLE_API_KEY and GOOGLE_CSE_ID are module-level constants now
        search_result_urls = await fetch_google_custom_search_results(
            query, 
            GOOGLE_API_KEY, 
            GOOGLE_CSE_ID, 
            MAX_SEARCH_RESULTS_TO_EXTRACT
        )

        if not search_result_urls:
            logger.info(f"Google Custom Search returned no results for query: '{query}'")
            return {"answer": f"I couldn't find any search results for your query: '{query}' using Google Custom Search.", "sources": []}

        # Step 2: Select URLs to process and fetch their content concurrently
        urls_to_fetch_content_for = []
        # Using a set to ensure we only consider unique URLs from search up to the extract limit
        # then take the top 'max_results_to_process' from that unique set.
        unique_search_urls = list(dict.fromkeys(search_result_urls))[:max_results_to_process]

        if not unique_search_urls:
            logger.info(f"No unique URLs to process from Google Custom Search for query: '{query}'")
            return {"answer": "No unique search results found to process for your query.", "sources": []}

        logger.info(f"Selected {len(unique_search_urls)} unique URLs from Google Custom Search results to fetch content for (max_results_to_process: {max_results_to_process}).")

        fetch_tasks = []
        for site_url in unique_search_urls:
            fetch_tasks.append(fetch_page_content(session, site_url, headers, ssl_verify, timeout_seconds))
        
        # Execute all fetch tasks concurrently
        # Results will be a list of tuples: (page_text_for_analysis, new_links)
        # We map them back to their URLs using the order from unique_search_urls
        logger.info(f"Attempting to fetch content for {len(fetch_tasks)} URLs concurrently...")
        fetched_pages_results = await asyncio.gather(*fetch_tasks, return_exceptions=True)
        logger.info(f"Finished fetching content for {len(fetched_pages_results)} URLs.")

        # Step 3: Analyze fetched content sequentially with delays for LLM rate limits
        llm_calls_made = 0
        for i, result_or_exception in enumerate(fetched_pages_results):
            current_url_being_analyzed = unique_search_urls[i]

            if isinstance(result_or_exception, Exception):
                logger.error(f"Skipping analysis for {current_url_being_analyzed} due to fetch error: {result_or_exception}", exc_info=False)
                continue

            page_text_for_analysis, _ = result_or_exception # new_links are not used in this search flow

            if page_text_for_analysis:
                # Apply delay *before* making the LLM call, if it's not the very first LLM call in this batch
                if llm_calls_made > 0:
                    logger.debug(f"Waiting {delay_between_llm_calls_seconds}s before next LLM analysis call for {current_url_being_analyzed}...")
                    await asyncio.sleep(delay_between_llm_calls_seconds)
                
                try:
                    logger.info(f"Analyzing content from: {current_url_being_analyzed} (LLM call #{llm_calls_made + 1})")
                    analysis_result = await analyze_content_relevance_and_extract(
                        llm, query, page_text_for_analysis, current_url_being_analyzed
                    )
                    llm_calls_made += 1 # Increment after a successful or attempted call
                    if analysis_result:
                        collected_relevant_info.append(analysis_result)
                except Exception as e: # Catch any unexpected error from the analysis call itself or sleep
                    logger.error(f"Unexpected error during analysis processing for {current_url_being_analyzed}: {e}", exc_info=True)
                    # If the error was an API call attempt that failed (e.g. ResourceExhausted caught inside analyze_content_relevance_and_extract),
                    # llm_calls_made would have been incremented by it. If error is before, it's fine.
            else:
                logger.info(f"No page text fetched for {current_url_being_analyzed}, skipping analysis.")

    # Step 4: Synthesize the final answer if relevant information was found
    if not collected_relevant_info:
        logger.info(f"No relevant information found across all sites for query: '{query}'")
        return {
            "answer": f"I was unable to find relevant information for your query '{query}' from the specified websites.",
            "sources": []
        }

    final_answer_text = await synthesize_answer_with_llm(llm, query, collected_relevant_info)
    source_urls = sorted(list(set(item['url'] for item in collected_relevant_info))) # Unique, sorted

    return {"answer": final_answer_text, "sources": source_urls}