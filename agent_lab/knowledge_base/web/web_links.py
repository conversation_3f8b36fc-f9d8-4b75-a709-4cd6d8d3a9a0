from link_scraper import scrape_links # Assuming this module exists and is correctly implemented

def get_user_inputs_for_scraping():
    """Prompts the user for a website URL and scraping depth."""
    main_website_url = input("Enter the main website URL to scrape (e.g., http://example.com): ")
    max_scraping_depth = -1
    while True:
        try:
            depth_str = input("Enter the maximum depth for scraping sub-links (e.g., 0 for main page only, 1 for main page + direct links): ")
            max_scraping_depth = int(depth_str)
            if max_scraping_depth < 0:
                print("Depth cannot be negative. Please enter a non-negative integer.")
            else:
                break
        except ValueError:
            print("Invalid input. Please enter an integer for the depth.")
    return main_website_url, max_scraping_depth

def main():
    # Initialize overall counts and storage for all operations
    overall_successfully_scraped_links_count = 0
    overall_unsuccessfully_scraped_attempts_count = 0
    all_scraped_links = [] # This list will store all unique links from all operations
    
    # New: Store base URLs attempted and their specific results
    attempted_base_urls = []
    results_per_base_url = {} # Stores {base_url: [links_found_for_it]}

    print("--- Web Link Scraper ---")

    while True:
        action = input("\nDo you want to add a URL to scrape? (yes/no): ").strip().lower()
        if action != 'yes':
            break

        # Get URL and depth for the current operation
        main_website_url, max_scraping_depth = get_user_inputs_for_scraping()

        # Initialize counts for the current scraping operation
        current_op_successful_links_found = 0
        current_op_unsuccessful_attempt = 0
        scraped_links_from_current_op = [] # Initialize for the current operation
        
        if not main_website_url.startswith(('http://', 'https://')):
            print(f"Invalid URL format: '{main_website_url}'. URL must start with http:// or https://. Skipping this URL.")
            current_op_unsuccessful_attempt = 1
            # Even if format is invalid, we don't add to attempted_base_urls or results_per_base_url
        else:
            attempted_base_urls.append(main_website_url) # Add to list of attempted URLs
            print(f"\nAttempting to scrape: {main_website_url} with depth {max_scraping_depth}...")
            try:
                # Attempt to scrape links for the current URL
                # scrape_links is expected to return a list of strings (links) or None/empty list on failure/no links
                scraped_links_from_current_op = scrape_links(main_website_url, max_scraping_depth)
                
                if scraped_links_from_current_op: # True if the list is not None and not empty
                    # Append new, unique links to the global list
                    for link in scraped_links_from_current_op:
                        if link not in all_scraped_links: # Optional: ensure uniqueness across all operations
                            all_scraped_links.append(link)
                    
                    current_op_successful_links_found = len(scraped_links_from_current_op) # Count all links returned by this op
                    results_per_base_url[main_website_url] = list(scraped_links_from_current_op) # Store specific results
                    print(f"Successfully found {current_op_successful_links_found} link(s) from {main_website_url}.")
                else:
                    # scrape_links returned None or an empty list, meaning the attempt for this URL yielded no links.
                    print(f"No links found for {main_website_url}, or the scraper returned no results.")
                    current_op_unsuccessful_attempt = 1
                    results_per_base_url[main_website_url] = [] # Record that no links were found for this attempt
                    
            except Exception as e:
                # Any exception during the scrape_links call means the attempt for this URL failed.
                print(f"An error occurred while scraping {main_website_url}: {e}")
                current_op_unsuccessful_attempt = 1
                results_per_base_url[main_website_url] = [] # Record error by storing empty list for this attempt
        
        # Update overall counts based on the current operation's outcome
        overall_successfully_scraped_links_count += current_op_successful_links_found
        overall_unsuccessfully_scraped_attempts_count += current_op_unsuccessful_attempt

    # --- Display Processed Base URLs and Allow Inspection ---
    if attempted_base_urls:
        print("\n--- Base URLs Processed ---")
        for i, base_url in enumerate(attempted_base_urls):
            link_count = len(results_per_base_url.get(base_url, []))
            print(f"{i+1}. {base_url} (Links found: {link_count})")

        while True:
            inspect_action = input("\nDo you want to inspect links for a specific base URL? (yes/no): ").strip().lower()
            if inspect_action != 'yes':
                break
            
            try:
                idx_str = input(f"Enter the number of the base URL to inspect (1-{len(attempted_base_urls)}): ")
                idx = int(idx_str) - 1
                if 0 <= idx < len(attempted_base_urls):
                    selected_base_url = attempted_base_urls[idx]
                    links_for_selected_url = results_per_base_url.get(selected_base_url, [])
                    print(f"\n--- Links for {selected_base_url} ---")
                    if links_for_selected_url:
                        for i_link, link_item in enumerate(links_for_selected_url):
                            print(f"  {i_link+1}. {link_item}")
                    else:
                        print("  No links were found for this base URL, or an error occurred during its processing.")
                else:
                    print(f"Invalid number. Please enter a number between 1 and {len(attempted_base_urls)}.")
            except ValueError:
                print("Invalid input. Please enter a number.")
    elif overall_unsuccessfully_scraped_attempts_count > 0 and not attempted_base_urls:
        print("\nNo valid base URLs were processed (all attempts had invalid formats).")
    else:
        print("\nNo base URLs were processed.")

    # --- Final Summary ---
    # Calculate the grand total of scrapes attempted based on the logic:
    # total = (sum of links from successful ops) + (number of failed ops)
    grand_total_scrapes_attempted = overall_successfully_scraped_links_count + overall_unsuccessfully_scraped_attempts_count
    
    summary_tuple = (overall_successfully_scraped_links_count, overall_unsuccessfully_scraped_attempts_count, grand_total_scrapes_attempted)
    print("\n--- Overall Scraping Summary ---")
    # The `all_scraped_links` list contains all unique links gathered but is not part of this specific print output.
    print(f"{summary_tuple}")

    # If you want to see all collected links, you can uncomment the following:
    # if all_scraped_links:
    #     print("\n--- All Unique Links Collected Across All Operations ---")
    #     for i, link in enumerate(all_scraped_links): # This would be all unique links across all operations
    #         print(f"{i+1}. {link}")
    # else:
    #     print("\nNo links were collected in total.")

    # Return the list of all scraped links and the summary
    return all_scraped_links, summary_tuple

if __name__ == "__main__":
    scraped_links_list, summary = main()
    # Optionally, do something with the returned list here if running web_links.py directly
    if scraped_links_list:
        print(f"\n--- First 5 of {len(scraped_links_list)} Unique Links Collected (from __main__ block) ---")
        for i, link in enumerate(scraped_links_list[:5]):
            print(f"{i+1}. {link}")