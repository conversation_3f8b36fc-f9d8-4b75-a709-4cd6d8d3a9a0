import logging
import os
import sys
import csv
import io
import datetime
import time # For latency calculation
import json # For serializing conversation history
from typing import Optional # Import Optional for type hinting
import uuid

from agent_lab.agent.permissions import get_authorized_users # For generating unique interaction IDs

# --- CSV Logging Configuration ---
LOGS_DIR = "interaction_logs"
CSV_LOG_FILE = os.path.join(LOGS_DIR, "application_activity_log.csv")
CSV_HEADER = ["timestamp", "logger_name", "log_level", "message"]

# Ensure the logs directory exists
os.makedirs(LOGS_DIR, exist_ok=True)

# --- Interaction Summary CSV Logging Configuration ---
INTERACTION_SUMMARY_CSV_FILE = os.path.join(LOGS_DIR, "interaction_summary_log.csv")
INTERACTION_SUMMARY_HEADER = ["timestamp", "user_query", "user_role", "latency_ms", "determined_kb_tool", "generated_sql_query", "agent_final_response", "conversation_context"]

# --- Agent Thoughts Logging Configuration ---
# Fixed line: Changed file extension from .log to .csv
AGENT_THOUGHTS_LOG_FILE = os.path.join(LOGS_DIR, "agent_thoughts.csv")
AGENT_THOUGHTS_CSV_HEADER = ["timestamp", "interaction_id", "level", "module", "function", "line_number", "message"]
agent_thoughts_logger = logging.getLogger("agent_thoughts")


class CsvFormatter(logging.Formatter):
    def __init__(self):
        super().__init__()
        self.output = io.StringIO()
        self.writer = csv.writer(self.output, quoting=csv.QUOTE_ALL)

    def format(self, record):
        # Reset the buffer
        self.output.seek(0)
        self.output.truncate(0)
        
        log_data = [
            datetime.datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3],
            record.name,
            record.levelname,
            record.getMessage()
        ]
        self.writer.writerow(log_data)
        return self.output.getvalue().strip()

class AgentThoughtsCsvFormatter(logging.Formatter):
    def __init__(self, header):
        super().__init__()
        self.header = header

    def format(self, record: logging.LogRecord) -> str:
        # Create a dictionary from the record, including the interaction_id from extra
        log_entry = {
            "timestamp": datetime.datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S,%f')[:-3],
            "interaction_id": getattr(record, 'interaction_id', 'N/A'), # Get from extra, default if not present
            "level": record.levelname,
            "module": record.module,
            "function": record.funcName,
            "line_number": record.lineno,
            "message": record.getMessage().replace('\n', '\\n').replace('"', '""') # Escape newlines and quotes for CSV
        }
        # Use a StringIO buffer to write CSV row correctly
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=self.header, quoting=csv.QUOTE_ALL)
        writer.writerow(log_entry)
        return output.getvalue().strip() # Get the string and remove trailing newline


# Write header for general application log CSV if file is new or empty
app_log_exists_and_not_empty = os.path.exists(CSV_LOG_FILE) and os.path.getsize(CSV_LOG_FILE) > 0
if not app_log_exists_and_not_empty:
    with open(CSV_LOG_FILE, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, quoting=csv.QUOTE_ALL)
        writer.writerow(CSV_HEADER)

csv_file_handler = logging.FileHandler(CSV_LOG_FILE, mode='a', encoding='utf-8')
csv_file_handler.setFormatter(CsvFormatter())

# Write header for interaction summary log CSV if file is new or empty
interaction_log_exists_and_not_empty = os.path.exists(INTERACTION_SUMMARY_CSV_FILE) and os.path.getsize(INTERACTION_SUMMARY_CSV_FILE) > 0
if not interaction_log_exists_and_not_empty:
    with open(INTERACTION_SUMMARY_CSV_FILE, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, quoting=csv.QUOTE_ALL)
        writer.writerow(INTERACTION_SUMMARY_HEADER)

# Configure handler for agent_thoughts_logger
agent_thoughts_file_handler = logging.FileHandler(AGENT_THOUGHTS_LOG_FILE, mode='a', encoding='utf-8')
agent_thoughts_csv_formatter = AgentThoughtsCsvFormatter(header=AGENT_THOUGHTS_CSV_HEADER)
agent_thoughts_file_handler.setFormatter(agent_thoughts_csv_formatter)

agent_thoughts_logger.addHandler(agent_thoughts_file_handler)
agent_thoughts_logger.setLevel(logging.DEBUG) 
agent_thoughts_logger.propagate = False

# Write header for agent_thoughts_logger if file is new/empty
if not os.path.exists(AGENT_THOUGHTS_LOG_FILE) or os.path.getsize(AGENT_THOUGHTS_LOG_FILE) == 0:
    with open(AGENT_THOUGHTS_LOG_FILE, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, quoting=csv.QUOTE_ALL)
        writer.writerow(AGENT_THOUGHTS_CSV_HEADER)


# Configure logging at the very beginning of your application
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        csv_file_handler
    ]
)
logging.getLogger("httpx").setLevel(logging.WARNING)
# Attempt to silence Google library loggers
logging.getLogger("google.generativeai").setLevel(logging.WARNING)
logging.getLogger("google.ai.generativelanguage").setLevel(logging.WARNING)
logging.getLogger("google.api_core").setLevel(logging.WARNING)

logger = logging.getLogger(__name__)

async def handle_interaction_and_log(user_query: str, user_role: str, agent_instance, conversation_id: str, channel_id: Optional[str] = None, message_ts: Optional[str] = None) -> dict: # Return the full agent_result dict
    """
    Handles a single user interaction: processes the query with the agent,
    logs the details, prints the response, and returns the agent's final response.

    Args:
        user_query (str): The query from the user.
        user_role (str): The role of the user.
        agent_instance (Agent): An instance of the Agent class.
        conversation_id (str): The unique ID for the current conversation context.
        channel_id (Optional[str]): The Slack channel ID for potential reactions.
        message_ts (Optional[str]): The timestamp of the user's message for potential reactions.

    Returns:
        dict: The agent_result dictionary containing final_response, final_reaction, etc.
    """
    approved = get_authorized_users(user_role)
    if not approved:
        logger.warning(f"User {user_role} is not authorized to use the agent. Using fallback permissions.")
        return {"final_response": "You are not authorized to use this agent. Please contact support if you believe this is an error."}
    interaction_id = str(uuid.uuid4())
    # Example of adding interaction_id to agent_thoughts_logger for its specific logs
    # You would typically create a LoggerAdapter or pass it via extra in actual logging calls
    # For demonstration with the current structure, this is a conceptual note.
    # Actual logging calls within your agent logic would need to pass 'extra={'interaction_id': interaction_id}'
    
    # Example: agent_thoughts_logger.debug("A thought occurred", extra={'interaction_id': interaction_id})


    logger.info(f"[Interaction: {interaction_id}] New interaction started. Conversation ID: '{conversation_id}', Query: '{user_query}', Role: '{user_role}'")

    start_time = time.time()
    agent_result, history_for_log = await agent_instance.process_query_and_manage_history(
        user_query,
        user_role,
        interaction_id,
        conversation_id, # Pass conversation_id to the agent
        channel_id,      # Pass channel_id for reactions
        message_ts       # Pass message_ts for reactions
    )
    end_time = time.time()

    latency_ms = (end_time - start_time) * 1000

    agent_final_response = agent_result.get("final_response", "Error: No response from agent.")
    determined_kb_tool = agent_result.get("determined_kb_or_tool", "N/A")
    generated_sql_query = agent_result.get("generated_sql", None)

    try:
        conversation_context_str = json.dumps(history_for_log)
    except TypeError:
        logger.error(f"[Interaction: {interaction_id}] Failed to serialize conversation history to JSON. History sample: {str(history_for_log)[:500]}", exc_info=True)
        conversation_context_str = "Error: Could not serialize conversation history to JSON (see logs for details)."

    logger.info(f"[Interaction: {interaction_id}] Agent Final Response: {agent_final_response}")
    logger.info(f"[Interaction: {interaction_id}] Determined KB/Tool: {determined_kb_tool}")
    if generated_sql_query:
        logger.info(f"[Interaction: {interaction_id}] Generated SQL Query: {generated_sql_query}")
    logger.info(f"[Interaction: {interaction_id}] Processing Latency: {latency_ms:.2f} ms")

    interaction_data_row = [
        datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3],
        user_query,
        user_role,
        f"{latency_ms:.2f}",
        determined_kb_tool,
        generated_sql_query if generated_sql_query is not None else "",
        agent_final_response,
        conversation_context_str
    ]
    with open(INTERACTION_SUMMARY_CSV_FILE, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, quoting=csv.QUOTE_ALL)
        writer.writerow(interaction_data_row)

    # print(f"\nAssistant: {agent_final_response}")
    return agent_result # Return the full dictionary