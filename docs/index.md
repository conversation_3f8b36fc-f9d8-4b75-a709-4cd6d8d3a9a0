# Project Documentation

## Overview

This project is designed to facilitate the retrieval and analysis of reports related to Flutterwave using Google’s Generative AI and embedding techniques. It integrates web search capabilities, data embedding, and summarization functionalities to provide users with relevant information efficiently.

## Table of Contents

1. [Installation](#installation)
2. [Usage](#usage)
3. [Modules](#modules)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
4. [Contributing](#contributing)
5. [License](#license)

## Installation

To set up the project, follow these steps:

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory and add the following:
   ```plaintext
   GEMINI_API_KEY=<your-gemini-api-key>
   GOOGLE_API_KEY=<your-google-api-key>
   GOOGLE_CSE_ID=<your-google-custom-search-engine-id>
   ```

## Usage

To use the functionalities provided by this project, you can run the main script or interact with the Jupyter notebook for embedding experiments. 

### Running the Agent

To start the agent, execute:
```bash
python agent.py
```

### Using the Jupyter Notebook

Open `embedding_exp.ipynb` in Jupyter Notebook and run the cells to experiment with data embeddings and summarization.

## Modules

### web_search_utility.py

This module provides utilities for performing web searches and fetching content from URLs.

#### Functions

- **fetch_page_content(session, site_url, headers, ssl_verify, timeout)**: Asynchronously fetches content from a given URL and returns the processed text and found links.
  
- **fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)**: Fetches search results from Google Custom Search API.

- **analyze_content_relevance_and_extract(llm, query, page_content, site_url, max_length)**: Analyzes page content for relevance to the user query using a language model.

- **synthesize_answer_with_llm(llm, query, collected_info)**: Synthesizes a natural language answer from collected information using a language model.

- **search_sites_for_answer(query, llm, user_agent, timeout_seconds, max_results_to_process)**: Searches for answers to a query using web search and synthesizes a response.

### embedding_exp.ipynb

This Jupyter notebook demonstrates how to load data, generate embeddings using the Gemini API, and create summaries of reports.

#### Key Steps

1. Load data from a CSV file.
2. Generate embeddings for the report descriptions.
3. Summarize the report data using the Gemini API.

### agent.py

This module defines the main agent that interacts with users and manages queries to the knowledge base.

#### Classes

- **GeminiLLM**: A class for generating responses using the Gemini language model.

#### Functions

- **query_knowledge_base(query, knowledge_bank, conversation_history, llm, thoughts_adapter)**: Queries the knowledge base and retrieves relevant information based on the user query.

- **generate_report(information, llm, conversation_history, thoughts_adapter, report_title, format)**: Generates a report based on the provided information.

### gemini_faiss_retriever.py

This module handles the retrieval of reports using FAISS (Facebook AI Similarity Search) and generates embeddings for the report descriptions.

#### Functions

- **get_embedding_gemini(text_to_embed, task_type)**: Generates an embedding for a given text using the Gemini Embedding API.

- **generate_summary_gemini(prompt_text)**: Generates a summary using the Gemini API.

- **build_index_and_datastore()**: Loads data, generates embeddings, builds a FAISS index, and saves the report data store.

- **RetrievalService**: A class that initializes the retrieval service and provides methods to search for reports based on user queries.

## Contributing

Contributions are welcome! Please follow these steps:

1. Fork the repository.
2. Create a new branch (`git checkout -b feature-branch`).
3. Make your changes and commit them (`git commit -m 'Add new feature'`).
4. Push to the branch (`git push origin feature-branch`).
5. Create a pull request.

## License

This project is licensed under the MIT License. See the LICENSE file for details.

---

This documentation provides a structured overview of the project, its modules, and how to use them. You can expand on each section as needed, especially in the usage examples and module descriptions, to provide more detailed information about parameters, return values, and potential exceptions.