# Project Documentation

## Overview

This project is a Natural Language Processing (NLP) platform designed to facilitate web searches, data retrieval, and embedding generation using Google’s Gemini API. It integrates various components to provide functionalities such as searching for information, generating embeddings for text, and summarizing reports.

## Table of Contents

1. [Installation](#installation)
2. [Usage](#usage)
3. [Modules](#modules)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
4. [Contributing](#contributing)
5. [License](#license)

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   - Create a `.env` file in the root directory and add your API keys:
     ```
     GEMINI_API_KEY=<your-gemini-api-key>
     GOOGLE_API_KEY=<your-google-api-key>
     GOOGLE_CSE_ID=<your-google-cse-id>
     ```

## Usage

### Running the Application

To run the application, execute the following command:
```bash
python agent.py
```

### Jupyter Notebook for Embeddings

To explore embeddings and data processing, open the Jupyter Notebook:
```bash
jupyter notebook embedding_exp.ipynb
```

## Modules

### web_search_utility.py

This module provides utilities for performing web searches using Google Custom Search API and fetching page content.

#### Functions

- **fetch_page_content(session, site_url, headers, ssl_verify, timeout)**
  - Asynchronously fetches content from a single URL.
  
- **fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)**
  - Fetches search results from Google Custom Search API.

- **analyze_content_relevance_and_extract(llm, query, page_content, site_url, max_length)**
  - Analyzes page content for relevance to the user query using a language model.

- **synthesize_answer_with_llm(llm, query, collected_info)**
  - Synthesizes a natural language answer from collected information using a language model.

- **search_sites_for_answer(query, llm, user_agent, timeout_seconds, max_results_to_process)**
  - Searches for answers across various sites and synthesizes a response.

### embedding_exp.ipynb

This Jupyter Notebook demonstrates how to load data, generate embeddings using the Gemini API, and create summaries from the data.

#### Key Steps

1. Load data from a CSV file.
2. Generate embeddings for the data using the Gemini API.
3. Create summaries based on the generated embeddings.

### agent.py

This module serves as the main entry point for the application, handling user queries and routing them to the appropriate services.

#### Classes

- **GeminiLLM**
  - A class for interacting with the Gemini language model.

#### Functions

- **query_knowledge_base(query, knowledge_bank, conversation_history, llm, thoughts_adapter)**
  - Queries the knowledge base and retrieves relevant information.

- **generate_report(information, llm, conversation_history, thoughts_adapter, report_title, format)**
  - Generates a report based on the provided information.

- **send_slack_message(recipient, message_text, original_user_identifier, slack_client, thoughts_adapter)**
  - Sends a message to a specified Slack channel.

### gemini_faiss_retriever.py

This module handles the retrieval of reports using embeddings and FAISS (Facebook AI Similarity Search).

#### Functions

- **get_embedding_gemini(text_to_embed, task_type)**
  - Generates an embedding for a given text using the Gemini API.

- **generate_summary_gemini(prompt_text)**
  - Generates a summary for the provided text using the Gemini API.

- **build_index_and_datastore()**
  - Loads data, generates embeddings, builds a FAISS index, and saves the data store.

- **RetrievalService**
  - A class for managing the retrieval of reports based on user queries.

## Contributing

Contributions are welcome! Please follow these steps:

1. Fork the repository.
2. Create a new branch (`git checkout -b feature-branch`).
3. Make your changes and commit them (`git commit -m 'Add new feature'`).
4. Push to the branch (`git push origin feature-branch`).
5. Create a pull request.

## License

This project is licensed under the MIT License. See the LICENSE file for details.

---

This documentation provides a structured overview of the project, its components, and how to use it effectively. Each module is described with its key functions and classes, making it easier for developers to understand and contribute to the codebase.