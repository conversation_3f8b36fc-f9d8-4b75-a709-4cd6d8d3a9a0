# Project Documentation

## Overview

This project is a Natural Language Processing (NLP) platform that integrates with Google’s Gemini API for generative AI capabilities. It includes functionalities for web searching, report generation, and embedding documents for retrieval using FAISS (Facebook AI Similarity Search). The project is designed to assist users in querying knowledge bases, generating reports, and summarizing data.

## Directory Structure

```
agent_lab/
│
├── agent/
│   ├── agent.py
│
├── knowledge_base/
│   ├── vector_store/
│   │   ├── embedding_exp.ipynb
│   │   ├── gemini_faiss_retriever.py
│   │   └── web_search_utility.py
│
└── core/
    └── config.py
```

## Modules

### 1. `agent.py`

This module defines the main agent functionalities, including routing queries, managing conversation history, and interacting with various tools and services.

#### Key Components

- **Classes**
  - `GeminiLLM`: Handles interactions with the Gemini API for generating responses based on prompts.
  - `Router`: Base class for routing queries to appropriate handlers.
  - `SemanticRouter`: Inherits from `Router` and implements semantic routing logic.
  - `Agent`: Main class that orchestrates the agent's functionalities.

- **Functions**
  - `query_knowledge_base`: Queries the knowledge base and retrieves relevant information based on user input.
  - `generate_report`: Generates a report based on provided information and conversation history.
  - `send_slack_message`: Sends messages to a specified Slack channel.
  - `parse_llm_json_output`: Parses the JSON output from the LLM.

### 2. `gemini_faiss_retriever.py`

This module is responsible for embedding documents using the Gemini API and managing a FAISS index for efficient retrieval of relevant reports.

#### Key Components

- **Functions**
  - `get_embedding_gemini`: Generates embeddings for a given text using the Gemini API.
  - `generate_summary_gemini`: Generates a summary for a given prompt using the Gemini API.
  - `build_index_and_datastore`: Loads data from a CSV file, generates embeddings, and builds a FAISS index.
  - `search_reports`: Searches for relevant reports based on user queries and returns the top results.

### 3. `web_search_utility.py`

This module provides utilities for performing web searches using Google Custom Search API and fetching page content.

#### Key Components

- **Functions**
  - `fetch_page_content`: Asynchronously fetches content from a specified URL.
  - `fetch_google_custom_search_results`: Fetches search results from Google Custom Search API.
  - `analyze_content_relevance_and_extract`: Analyzes page content for relevance to a user query.
  - `synthesize_answer_with_llm`: Synthesizes a natural language answer from collected information.

### 4. `embedding_exp.ipynb`

This Jupyter notebook demonstrates the process of loading data, generating embeddings, and summarizing reports using the Gemini API.

#### Key Components

- **Data Handling**: Loads data from a CSV file and processes it for embedding.
- **Embedding Generation**: Uses the Gemini API to generate embeddings for the data.
- **Description Generation**: Generates descriptions for reports based on the data.

## Configuration

### Environment Variables

The project requires the following environment variables to be set:

- `GEMINI_API_KEY`: API key for accessing the Gemini API.
- `GOOGLE_API_KEY`: API key for Google Custom Search.
- `GOOGLE_CSE_ID`: Custom Search Engine ID for Google Search.

### Logging

The project uses Python's built-in logging module to log information, warnings, and errors. The logging configuration can be adjusted in the respective modules.

## Usage

1. **Set Up Environment**: Ensure all required environment variables are set in a `.env` file.
2. **Install Dependencies**: Install required Python packages, including `google-api-python-client`, `numpy`, `pandas`, `faiss`, and `aiohttp`.
3. **Run the Agent**: Execute the `agent.py` module to start the agent and interact with it through queries.
4. **Indexing Reports**: Use the `build_index_and_datastore` function in `gemini_faiss_retriever.py` to load reports and create embeddings.

## Error Handling

The project includes error handling for various operations, including API calls and file operations. Errors are logged, and user-friendly messages are provided when operations fail.

## Conclusion

This documentation provides an overview of the codebase, its structure, and its functionalities. For further details, refer to the inline comments within the code and the specific module documentation.

--- 

This documentation can be expanded with examples, usage scenarios, and additional details as needed.