# Project Documentation: Flutterwave NLP Platform

## Overview
The Flutterwave NLP Platform is designed to facilitate natural language processing tasks, including web search, report generation, and data retrieval using Google Generative AI and FAISS for vector storage and retrieval. The platform integrates various components to provide a seamless experience for users querying financial and operational data related to Flutterwave.

## Table of Contents
1. [Installation](#installation)
2. [Configuration](#configuration)
3. [File Structure](#file-structure)
4. [Modules](#modules)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
5. [Usage](#usage)
6. [Contributing](#contributing)
7. [License](#license)

## Installation
To set up the project, clone the repository and install the required dependencies. Ensure you have Python 3.7 or higher installed.

```bash
git clone <repository-url>
cd flw-nlp-platform
pip install -r requirements.txt
```

## Configuration
Before running the project, configure the environment variables. Create a `.env` file in the root directory with the following content:

```plaintext
GEMINI_API_KEY=<your_gemini_api_key>
GOOGLE_API_KEY=<your_google_api_key>
GOOGLE_CSE_ID=<your_google_custom_search_engine_id>
```

## File Structure
```
flw-nlp-platform/
│
├── agent_lab/
│   ├── agent/
│   │   └── agent.py
│   └── knowledge_base/
│       ├── vector_store/
│       │   ├── embedding_exp.ipynb
│       │   └── gemini_faiss_retriever.py
│       └── web/
│           └── web_search_utility.py
└── requirements.txt
```

## Modules

### web_search_utility.py
This module provides functionalities for performing web searches using Google Custom Search API and fetching page content.

#### Key Functions:
- `fetch_page_content(session, site_url, headers, ssl_verify, timeout)`: Asynchronously fetches content from a given URL.
- `fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)`: Fetches search results from Google Custom Search API.
- `analyze_content_relevance_and_extract(llm, query, page_content, site_url)`: Analyzes the relevance of page content to the user's query using a language model.
- `synthesize_answer_with_llm(llm, query, collected_info)`: Synthesizes a natural language answer from collected information.

### embedding_exp.ipynb
This Jupyter notebook demonstrates the process of loading report data, generating embeddings using the Gemini API, and summarizing the data.

#### Key Steps:
1. Load data from a CSV file.
2. Generate embeddings for the 'Description' column using the Gemini API.
3. Create summaries for the top 20 reports based on the generated embeddings.

### agent.py
This module serves as the main agent that interacts with users, processes queries, and manages the conversation history.

#### Key Classes and Functions:
- `GeminiLLM`: A class that handles interactions with the Gemini API for generating responses.
- `Router`: A class that routes queries to the appropriate service based on user input.
- `query_knowledge_base(query, knowledge_bank, conversation_history, llm, thoughts_adapter)`: Queries the knowledge base and retrieves relevant information.

### gemini_faiss_retriever.py
This module handles the embedding and retrieval of report data using FAISS for efficient similarity search.

#### Key Functions:
- `get_embedding_gemini(text_to_embed, task_type)`: Generates embeddings for the provided text using the Gemini API.
- `generate_summary_gemini(prompt_text)`: Generates a summary for the provided text using the Gemini API.
- `build_index_and_datastore()`: Loads data, generates embeddings, builds a FAISS index, and saves the data store.
- `RetrievalService`: A class that initializes the retrieval service and provides methods to search for reports based on user queries.

## Usage
To use the platform, run the `agent.py` script. The agent will prompt for user input and process queries based on the available functionalities.

```bash
python agent_lab/agent/agent.py
```

## Contributing
Contributions are welcome! Please fork the repository and submit a pull request with your changes.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.

---

This documentation provides a comprehensive overview of the project, its structure, and its functionalities. You can expand on each section with more details as needed, especially in the usage examples and contributing guidelines.