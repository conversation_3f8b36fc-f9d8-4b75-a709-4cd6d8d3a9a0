# Project Documentation: Flutterwave NLP Platform

## Overview
The Flutterwave NLP Platform is designed to facilitate natural language processing tasks, including web search, data retrieval, and report generation. It leverages Google Generative AI and FAISS for efficient data handling and embedding generation.

## Table of Contents
1. [Modules](#modules)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
2. [Classes](#classes)
   - [GeminiLLM](#geminillm)
   - [Router](#router)
   - [SemanticRouter](#semanticrouter)
   - [RetrievalService](#retrievalservice)
   - [Agent](#agent)
3. [Functions](#functions)
4. [Environment Setup](#environment-setup)
5. [Usage](#usage)

## Modules

### web_search_utility.py
This module provides utilities for performing web searches using Google Custom Search API and fetching page content.

#### Key Functions
- `fetch_page_content(session, site_url, headers, ssl_verify, timeout)`: Asynchronously fetches content from a given URL.
- `fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)`: Fetches search results from Google Custom Search API.
- `analyze_content_relevance_and_extract(llm, query, page_content, site_url, max_length)`: Analyzes page content for relevance to a user query using a language model.
- `synthesize_answer_with_llm(llm, query, collected_info)`: Synthesizes a natural language answer from collected information using a language model.
- `search_sites_for_answer(query, llm, user_agent, timeout_seconds, max_results_to_process)`: Searches for answers across web sources.

### embedding_exp.ipynb
This Jupyter notebook demonstrates the process of loading data, generating embeddings, and summarizing reports using Google Generative AI.

#### Key Steps
1. Load data from a CSV file.
2. Generate embeddings for report descriptions.
3. Summarize the reports using the Gemini API.

### agent.py
This module defines the main agent that interacts with various services, manages conversation history, and routes queries to appropriate handlers.

#### Key Classes
- `GeminiLLM`: Handles interactions with the Google Generative AI model.
- `Router`: Base class for routing queries.
- `SemanticRouter`: Inherits from Router and implements semantic routing logic.
- `Agent`: Main class that orchestrates the agent's functionalities.

### gemini_faiss_retriever.py
This module provides functionalities for generating embeddings, building a FAISS index, and retrieving relevant reports based on user queries.

#### Key Functions
- `get_embedding_gemini(text_to_embed, task_type)`: Generates an embedding for a given text using the Gemini API.
- `generate_summary_gemini(prompt_text)`: Generates a summary for a given prompt using the Gemini API.
- `build_index_and_datastore()`: Loads data, generates embeddings, builds a FAISS index, and saves the data store.
- `RetrievalService`: Class that initializes the retrieval service and provides methods for searching reports.

## Classes

### GeminiLLM
Handles interactions with the Google Generative AI model for generating responses based on prompts.

#### Methods
- `__init__()`: Initializes the GeminiLLM instance.
- `generate(prompt)`: Generates a response based on the provided prompt.

### Router
Base class for routing queries to appropriate handlers.

#### Methods
- `route(query, conversation_history, thoughts_adapter)`: Routes the query based on its content.

### SemanticRouter
Inherits from `Router` and implements semantic routing logic.

#### Methods
- `route(query, conversation_history, thoughts_adapter)`: Routes the query semantically.

### RetrievalService
Manages the retrieval of reports based on user queries using embeddings and FAISS.

#### Methods
- `__init__()`: Initializes the retrieval service and loads the FAISS index and report data store.
- `search_reports(user_query, top_k)`: Searches for relevant reports based on the user query.

### Agent
Main class that orchestrates the agent's functionalities, including handling user queries and managing conversation history.

#### Methods
- Various methods for handling tool execution, sending messages, and querying knowledge bases.

## Functions
- Various utility functions for embedding generation, summarization, and data handling.

## Environment Setup
1. Clone the repository.
2. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```
3. Set up environment variables in a `.env` file:
   ```
   GEMINI_API_KEY=your_api_key
   GOOGLE_API_KEY=your_google_api_key
   GOOGLE_CSE_ID=your_custom_search_engine_id
   ```

## Usage
1. Start the agent by running `agent.py`.
2. Interact with the agent through a command line or integrate it into a web application.
3. Use the Jupyter notebook `embedding_exp.ipynb` for experimentation with embeddings and summarization.

---

This documentation provides a comprehensive overview of the codebase, detailing the purpose and functionality of each module, class, and function. It serves as a guide for developers and users to understand and utilize the Flutterwave NLP Platform effectively.