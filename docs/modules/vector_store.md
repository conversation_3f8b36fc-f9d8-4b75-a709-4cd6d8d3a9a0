# Project Documentation

## Overview

This project is designed to facilitate the retrieval and analysis of reports using Google Generative AI and FAISS for vector similarity search. It includes functionalities for web searching, embedding generation, and summarization of report data.

## Table of Contents

1. [Modules](#modules)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
2. [Usage](#usage)
3. [Environment Variables](#environment-variables)
4. [Logging](#logging)
5. [Error Handling](#error-handling)

---

## Modules

### web_search_utility.py

This module provides utilities for performing web searches using Google Custom Search API and fetching page content.

#### Key Functions

- **fetch_page_content(session, site_url, headers, ssl_verify, timeout)**: 
  - Asynchronously fetches content from a given URL and returns the processed text content along with unique links found on the page.

- **fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)**: 
  - Fetches search results from Google Custom Search API based on the provided query.

- **analyze_content_relevance_and_extract(llm, query, page_content, site_url, max_length)**: 
  - Analyzes the relevance of page content to the user's query using a language model and extracts key information.

- **synthesize_answer_with_llm(llm, query, collected_info)**: 
  - Synthesizes a natural language answer from collected information using a language model.

- **search_sites_for_answer(query, llm, user_agent, timeout_seconds, max_results_to_process)**: 
  - Searches for answers to a query by fetching content from top search results and synthesizing a response.

### embedding_exp.ipynb

This Jupyter notebook demonstrates the process of loading report data, generating embeddings using Google Generative AI, and summarizing the data.

#### Key Steps

1. Load report data from a CSV file.
2. Generate embeddings for the report descriptions.
3. Summarize the report data using the Gemini API.

### agent.py

This module defines the main agent responsible for handling user queries, interacting with various services, and managing conversation history.

#### Key Classes

- **GeminiLLM**: 
  - A class for generating responses using the Gemini language model.

- **Router**: 
  - A base class for routing queries to the appropriate service.

- **SemanticRouter**: 
  - A subclass of Router that implements semantic routing logic.

#### Key Functions

- **query_knowledge_base(query, knowledge_bank, conversation_history, llm, thoughts_adapter)**: 
  - Queries the knowledge base for information based on the user's query.

- **generate_report(information, llm, conversation_history, thoughts_adapter, report_title, format)**: 
  - Generates a report based on the provided information.

- **send_slack_message(recipient, message_text, original_user_identifier, slack_client, thoughts_adapter)**: 
  - Sends a message to a specified Slack channel.

### gemini_faiss_retriever.py

This module handles the embedding generation and retrieval of reports using FAISS for vector similarity search.

#### Key Classes

- **RetrievalService**: 
  - A class that initializes the retrieval service, loads the FAISS index, and provides methods for searching reports.

#### Key Functions

- **get_embedding_gemini(text_to_embed, task_type)**: 
  - Generates an embedding for a given text using the Gemini API.

- **generate_summary_gemini(prompt_text)**: 
  - Generates a summary for a given prompt using the Gemini API.

- **build_index_and_datastore()**: 
  - Loads report data, generates embeddings, builds a FAISS index, and saves the report data store.

---

## Usage

To use this project, ensure you have the required environment variables set up, including the `GEMINI_API_KEY`. You can run the modules individually or integrate them into a larger application.

1. **Set up environment variables**: Create a `.env` file with the necessary API keys.
2. **Run the Jupyter notebook**: Execute `embedding_exp.ipynb` to see the embedding and summarization process in action.
3. **Use the agent**: Interact with the `agent.py` module to handle user queries and retrieve information.

---

## Environment Variables

- **GEMINI_API_KEY**: Your API key for accessing Google Generative AI services.
- **GOOGLE_API_KEY**: Your API key for Google Custom Search.
- **GOOGLE_CSE_ID**: Your Custom Search Engine ID.

---

## Logging

The project uses Python's built-in logging module to log information, warnings, and errors. Ensure that logging is configured appropriately to capture the desired output.

---

## Error Handling

The codebase includes error handling for various operations, including API calls and file operations. It logs errors and provides user-friendly messages when issues occur.

---

This documentation provides a comprehensive overview of the codebase, its modules, and how to use them effectively. For further details, refer to the inline comments within the code and the specific function and class docstrings.