# Project Documentation

## Overview

This project is designed to facilitate the retrieval and analysis of information related to Flutterwave using various tools, including Google Custom Search and generative AI models. It incorporates functionalities for web searching, embedding generation, and report retrieval using a FAISS index.

## Table of Contents

1. [Installation](#installation)
2. [Configuration](#configuration)
3. [File Descriptions](#file-descriptions)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
4. [Usage](#usage)
5. [Logging](#logging)
6. [Error Handling](#error-handling)
7. [Contributing](#contributing)
8. [License](#license)

## Installation

To set up the project, follow these steps:

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables in a `.env` file:
   ```plaintext
   GEMINI_API_KEY=<your_gemini_api_key>
   GOOGLE_API_KEY=<your_google_api_key>
   GOOGLE_CSE_ID=<your_google_custom_search_engine_id>
   ```

## Configuration

The project uses environment variables for configuration. Ensure that the following variables are set in your `.env` file:

- `GEMINI_API_KEY`: API key for Google Generative AI.
- `GOOGLE_API_KEY`: API key for Google Custom Search.
- `GOOGLE_CSE_ID`: Custom Search Engine ID for Google Search.

## File Descriptions

### web_search_utility.py

This module provides utilities for performing web searches using Google Custom Search and fetching page content.

- **Key Functions:**
  - `fetch_page_content`: Asynchronously fetches content from a given URL.
  - `fetch_google_custom_search_results`: Fetches search results from Google Custom Search API.
  - `analyze_content_relevance_and_extract`: Analyzes page content for relevance to a user query.
  - `synthesize_answer_with_llm`: Synthesizes a natural language answer from collected information.
  - `search_sites_for_answer`: Orchestrates the search and analysis process.

### embedding_exp.ipynb

This Jupyter notebook demonstrates the process of loading data, generating embeddings using the Gemini API, and summarizing reports.

- **Key Steps:**
  - Load data from a CSV file.
  - Generate embeddings for the report descriptions.
  - Summarize the reports using the Gemini API.

### agent.py

This module defines the main agent that interacts with users, processes queries, and retrieves information from various sources.

- **Key Classes and Functions:**
  - `GeminiLLM`: Class for generating responses using the Gemini API.
  - `Router`: Base class for routing queries.
  - `SemanticRouter`: Inherits from `Router` and implements semantic routing.
  - `query_knowledge_base`: Queries the knowledge base based on user input.

### gemini_faiss_retriever.py

This module handles the retrieval of reports using a FAISS index and generates embeddings for the report descriptions.

- **Key Functions:**
  - `get_embedding_gemini`: Generates embeddings for a given text using the Gemini API.
  - `generate_summary_gemini`: Generates a summary for a given prompt using the Gemini API.
  - `build_index_and_datastore`: Loads data, generates embeddings, and builds a FAISS index.
  - `RetrievalService`: Class for searching reports based on user queries.

## Usage

To use the project, run the main script or the Jupyter notebook. For example, to start the agent, execute:

```bash
python agent.py
```

You can also run the Jupyter notebook to experiment with embedding generation and summarization.

## Logging

The project uses Python's built-in logging module to log information, warnings, and errors. Logs are configured to display timestamps, log levels, and messages.

## Error Handling

The project includes error handling for various operations, such as API calls and file operations. Errors are logged, and user-friendly messages are provided when necessary.

## Contributing

Contributions are welcome! Please fork the repository and submit a pull request with your changes.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.

---

This documentation provides a comprehensive overview of the project, its structure, and how to use it. You can expand on each section with more details as needed, especially in the usage examples and configuration instructions.