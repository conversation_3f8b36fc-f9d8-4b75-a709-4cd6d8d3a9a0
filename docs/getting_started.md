# Project Documentation

## Overview

This project is a Natural Language Processing (NLP) platform designed to facilitate web searches, data retrieval, and report generation using Google’s Gemini API and FAISS for vector storage and retrieval. The system is capable of embedding text, generating summaries, and synthesizing answers based on user queries.

## Table of Contents

1. [Installation](#installation)
2. [Configuration](#configuration)
3. [Usage](#usage)
4. [Modules](#modules)
   - [web_search_utility.py](#web_search_utilitypy)
   - [embedding_exp.ipynb](#embedding_expipynb)
   - [agent.py](#agentpy)
   - [gemini_faiss_retriever.py](#gemini_faiss_retrieverpy)
5. [Classes and Functions](#classes-and-functions)
6. [Logging](#logging)
7. [Contributing](#contributing)
8. [License](#license)

## Installation

To set up the project, follow these steps:

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   - Create a `.env` file in the root directory and add your API keys:
     ```
     GEMINI_API_KEY=<your-gemini-api-key>
     GOOGLE_API_KEY=<your-google-api-key>
     GOOGLE_CSE_ID=<your-google-cse-id>
     ```

## Configuration

The project uses environment variables for configuration. Ensure that the following variables are set in your `.env` file:

- `GEMINI_API_KEY`: API key for Google Generative AI.
- `GOOGLE_API_KEY`: API key for Google Custom Search.
- `GOOGLE_CSE_ID`: Custom Search Engine ID for Google Search.

## Usage

To use the project, you can run the main script or interact with the Jupyter notebook for embedding and summarization tasks. 

### Example Usage

1. **Run the Agent**:
   ```bash
   python agent.py
   ```

2. **Execute the Jupyter Notebook**:
   Open `embedding_exp.ipynb` in Jupyter Notebook and run the cells to see how embeddings and summaries are generated.

## Modules

### web_search_utility.py

This module provides utilities for performing web searches using Google Custom Search API. It includes functions for fetching page content and analyzing relevance using a language model.

#### Key Functions

- `fetch_page_content(session, site_url, headers, ssl_verify, timeout)`: Asynchronously fetches content from a URL.
- `fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)`: Fetches search results from Google Custom Search API.
- `analyze_content_relevance_and_extract(llm, query, page_content, site_url, max_length)`: Analyzes page content for relevance to a user query.
- `synthesize_answer_with_llm(llm, query, collected_info)`: Synthesizes a natural language answer from collected information.

### embedding_exp.ipynb

This Jupyter notebook demonstrates how to load data, generate embeddings, and create summaries using the Gemini API.

#### Key Sections

- Loading data from CSV files.
- Generating embeddings for text data.
- Summarizing the generated embeddings.

### agent.py

This module serves as the main entry point for the agent functionalities, handling user queries and routing them to the appropriate services.

#### Key Classes

- `GeminiLLM`: Class for interacting with the Gemini API to generate responses.
- `Router`: Base class for routing queries.
- `SemanticRouter`: Inherits from `Router` and implements semantic routing logic.

### gemini_faiss_retriever.py

This module handles the embedding and retrieval of reports using FAISS for efficient similarity search.

#### Key Functions

- `get_embedding_gemini(text_to_embed, task_type)`: Generates embeddings for the provided text.
- `generate_summary_gemini(prompt_text)`: Generates a summary for the given prompt using the Gemini API.
- `build_index_and_datastore()`: Loads data, generates embeddings, and builds a FAISS index.

## Classes and Functions

### Classes

- **GeminiLLM**
  - Methods:
    - `generate(prompt: str)`: Generates a response based on the provided prompt.

- **Router**
  - Methods:
    - `route(query: str, conversation_history: list, thoughts_adapter: logging.LoggerAdapter)`: Routes the query to the appropriate service.

- **SemanticRouter**
  - Inherits from `Router`.

- **RetrievalService**
  - Methods:
    - `search_reports(user_query, top_k=3)`: Searches for reports based on the user query.

### Functions

- `fetch_page_content(...)`
- `fetch_google_custom_search_results(...)`
- `analyze_content_relevance_and_extract(...)`
- `synthesize_answer_with_llm(...)`
- `get_embedding_gemini(...)`
- `generate_summary_gemini(...)`
- `build_index_and_datastore(...)`

## Logging

The project uses Python's built-in logging module to log information, warnings, and errors. Logs are configured to output to the console with timestamps and log levels.

## Contributing

Contributions are welcome! Please follow these steps:

1. Fork the repository.
2. Create a new branch (`git checkout -b feature-branch`).
3. Make your changes and commit them (`git commit -m 'Add new feature'`).
4. Push to the branch (`git push origin feature-branch`).
5. Create a pull request.

## License

This project is licensed under the MIT License. See the LICENSE file for details.

---

This documentation provides a comprehensive overview of the project, its modules, and how to use it effectively. You can expand on specific sections as needed, especially in the usage examples and detailed function descriptions.