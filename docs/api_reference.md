# Project Documentation

## Overview

This project is a Natural Language Processing (NLP) platform designed to facilitate web searches, data retrieval, and report generation using Google’s Gemini API and FAISS for vector storage and retrieval. The system is structured to handle user queries, generate embeddings, and summarize information effectively.

## Table of Contents

1. [Installation](#installation)
2. [Configuration](#configuration)
3. [Usage](#usage)
4. [Modules](#modules)
   - [web_search_utility](#web_search_utility)
   - [embedding_exp](#embedding_exp)
   - [agent](#agent)
   - [gemini_faiss_retriever](#gemini_faiss_retriever)
5. [Classes](#classes)
   - [GeminiLLM](#geminillm)
   - [Router](#router)
   - [SemanticRouter](#semanticrouter)
   - [RetrievalService](#retrievalservice)
   - [Agent](#agent)
6. [Functions](#functions)
7. [Logging](#logging)
8. [Error Handling](#error-handling)
9. [Contributing](#contributing)
10. [License](#license)

## Installation

To set up the project, follow these steps:

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   - Create a `.env` file in the root directory and add your API keys:
     ```
     GEMINI_API_KEY=<your-gemini-api-key>
     GOOGLE_API_KEY=<your-google-api-key>
     GOOGLE_CSE_ID=<your-google-cse-id>
     ```

## Configuration

The project uses environment variables for configuration. Ensure that the following variables are set in your `.env` file:

- `GEMINI_API_KEY`: Your API key for Google Generative AI.
- `GOOGLE_API_KEY`: Your API key for Google Custom Search.
- `GOOGLE_CSE_ID`: Your Custom Search Engine ID.

## Usage

To use the platform, you can run the main script or interact with the provided classes and functions. Here’s a basic example of how to initiate a search:

```python
from agent import Agent

agent = Agent()
response = await agent.query_knowledge_base("What is Flutterwave?", "web_search", conversation_history=[])
print(response)
```

## Modules

### web_search_utility

This module provides utilities for performing web searches using Google Custom Search API and fetching page content.

#### Functions

- `fetch_page_content(session, site_url, headers, ssl_verify, timeout)`: Asynchronously fetches content from a URL.
- `fetch_google_custom_search_results(query, api_key, cse_id, max_results_to_extract)`: Fetches search results from Google Custom Search API.
- `analyze_content_relevance_and_extract(llm, query, page_content, site_url)`: Analyzes page content for relevance to the query.
- `synthesize_answer_with_llm(llm, query, collected_info)`: Synthesizes a natural language answer from collected information.

### embedding_exp

This module handles the embedding of text data and the generation of summaries using the Gemini API.

#### Functions

- `get_embedding_gemini(text_to_embed)`: Generates an embedding for a given text.
- `generate_summary_gemini(prompt_text)`: Generates a summary using the Gemini API.
- `build_index_and_datastore()`: Loads data, generates embeddings, builds a FAISS index, and saves the data store.

### agent

This module defines the main agent that interacts with the user and manages the conversation history.

#### Classes

- `Agent`: The main class that handles user queries and manages interactions with various services.

### gemini_faiss_retriever

This module provides functionality for retrieving data from a FAISS index and generating embeddings.

#### Classes

- `RetrievalService`: Handles the retrieval of reports based on user queries.

## Classes

### GeminiLLM

This class encapsulates the functionality for generating responses using the Gemini API.

#### Methods

- `generate(prompt: str) -> str`: Generates a response based on the provided prompt.

### Router

This class is responsible for routing queries to the appropriate service based on the user input.

### SemanticRouter

Inherits from `Router` and implements semantic routing logic.

### RetrievalService

Handles the retrieval of reports based on user queries and manages the FAISS index.

#### Methods

- `search_reports(user_query, top_k=3)`: Finds the most relevant reports based on the user query.

### Agent

The main agent class that manages user interactions and queries.

## Functions

- `query_knowledge_base(query, knowledge_bank, conversation_history, llm, thoughts_adapter)`: Queries the knowledge base for relevant information.
- `generate_report(information, llm, conversation_history, thoughts_adapter)`: Generates a report based on the provided information.

## Logging

The project uses Python's built-in logging module to log information, warnings, and errors. Logs are configured to output to the console with timestamps and log levels.

## Error Handling

The project includes error handling for various operations, particularly when interacting with external APIs. Errors are logged, and user-friendly messages are returned when operations fail.

## Contributing

Contributions are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.

---

This documentation provides a structured overview of the project, its modules, classes, and functions, along with installation and usage instructions. You can expand on each section with more details as needed, especially for complex functions or classes.