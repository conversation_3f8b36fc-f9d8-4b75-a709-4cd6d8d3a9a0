# Flaire AI Assistant

## Description

The Flaire AI Assistant is an interactive command-line application designed to understand user queries, route them to appropriate knowledge bases or tools, and provide relevant responses. It incorporates features like role-based access control, conversation history management, and relevance checking to enhance user interaction and response quality. The assistant is built to be extensible, allowing for the integration of various data sources and specialized tools.

## Features

*   **Natural Language Understanding:** Leverages Google's Gemini LLM to interpret user queries.
*   **Semantic Routing:** Intelligently directs queries to the most suitable knowledge bank (e.g., SQL databases, vector stores, company-specific knowledge) or specialized tool (e.g., chart generator, report generator).
*   **Tool Integration:** Supports tools like a (simulated) chart generator and report generator.
*   **Role-Based Access Control (RBAC):** Restricts access to certain knowledge banks or tools based on user-defined roles.
*   **Conversation History:** Maintains a history of the current session to provide context for follow-up questions.
*   **Relevance Checking:** Employs an LLM-based check to assess if the generated response is relevant to the user's query, and provides user-friendly feedback if not.
*   **Structured Logging:** Comprehensive logging of interactions, decisions, and errors for debugging and monitoring.
*   **Dummy Database Interaction:** Includes a simulated database interaction layer (`db_execute`) for development and testing without a live database.

## Prerequisites

*   Python 3.8+
*   Google Gemini API Key

## Setup and Installation

1.  **Clone the repository:**
    ```bash
    git clone <your-repository-url>
    cd flw-nlp-platform
    ```

2.  **Create and activate a virtual environment:**
    ```bash
    python -m venv .venv
    # On Windows
    .\.venv\Scripts\activate
    # On macOS/Linux
    source .venv/bin/activate
    ```

3.  **Install dependencies:**
    To install your requirements from the `requirements.txt` file, run:
    ```bash
    pip install -r requirements.txt
    ```

4.  **Set up environment variables:**
    Create a `.env` file in the project root directory and add your Google Gemini API key:
    ```env
    GEMINI_API_KEY="YOUR_GEMINI_API_KEY"
    ```

## Usage

Run the main application script from the project root directory:

```bash
python main.py
```

The assistant will prompt you for your query and your role.

```
Flaire AI Assistant. Type 'quit' at any time to exit.

How may I assist you today?: <Your query here>
Enter your role (e.g., data_team, general_user): <Your role here>
```

## Project Structure

```
flw-nlp-platform/
├── .venv/                     # Virtual environment
├── agent_lab/
│   ├── agent/
│   │   ├── __init__.py
│   │   ├── agent.py           # Core agent logic, routing, tool execution
│   │   ├── retrieval.py       # Relevance checking and hypothetical document generation
│   │   └── db_execution.py    # (Potentially) Actual database execution logic
│   ├── charting_agent/
│   │   └── flutterplot.py           # Utility functions 
│   ├── data_processing/
│   │   
│   │   
│   ├── knowledge_base/
│   │   ├── sql/
│   │   ├── vectorstore/      
│   │   └── web/          
│   ├── report_generation_agent/
│   |
|   ├── interaction_logs/
│   
├── .env                       # Environment variables (API keys)
├── main.py                    # Main application entry point
├── logs.py                    # Logging setup and interaction handling
└── README.md                  # This file
```

## Logging

The application uses Python's `logging` module. Logs are configured in `logs.py` and provide detailed information about the agent's operations, including query processing, routing decisions, tool usage, and relevance checks. By default, logs are output to the console and may be configured to write to files.

## Key Components

*   **`main.py`**: Entry point of the application. Handles user interaction loop and calls the agent.
*   **`agent_lab/agent/agent.py`**: Contains the `Agent` class, which encapsulates the core logic for processing queries, managing conversation history, semantic routing, tool parameter extraction, and invoking knowledge bases or tools.
*   **`agent_lab/agent/retrieval.py`**: Implements the `RelevanceChecker` class, responsible for assessing the relevance of the agent's response to the user's query using hypothetical document generation.
*   **`logs.py`**: Sets up application-wide logging and includes the `handle_interaction_and_log` function to manage and log each user-agent interaction.
*   **`agent_lab/core/llm_calls.py`**: Provides a wrapper for making calls to the Gemini LLM.
*   **`agent_lab/core/utils.py`**: Contains utility functions, potentially including rate limiters and other helpers.

## How It Works (High-Level Flow)

1.  User enters a query and their role via `main.py`.
2.  `main.py` passes the query and role to an `Agent` instance.
3.  The `Agent` in `agent.py`:
    *   Uses `SemanticRouter` to determine if the query should go to a knowledge bank or a specific tool.
    *   Checks user role permissions for the determined destination.
    *   If a tool is selected, it extracts necessary parameters using the LLM.
    *   If a knowledge bank is selected (or data is needed for a tool), it formulates a request (simulated by `db_execute` in the current setup).
    *   Executes the tool or queries the knowledge bank.
    *   The `RelevanceChecker` from `retrieval.py` assesses the generated response against the original query.
    *   The final response (or a user-friendly message if relevance check fails) is returned.
4.  `main.py` displays the assistant's response.
5.  Conversation history is maintained within the `Agent` instance for contextual understanding in subsequent turns.
6.  All significant steps are logged via `logs.py`.

## Future Enhancements / To-Dos

*   Replace dummy `db_execute` with actual database connectors (e.g., for SQL, vector databases).
*   Implement actual chart generation in `generate_chart` (e.g., using Matplotlib, Plotly).
*   Expand the range of tools and knowledge banks.
*   Refine role-based access control with more granular permissions.
*   Add persistent storage for conversation history across sessions.
*   Develop a more sophisticated error handling and retry mechanism.
*   Create a web interface or API for the assistant.

## Contributing

Contributions are not welcome.

## License

(Specify your chosen license here, e.g., MIT, Apache 2.0. If unsure, MIT is a common choice for open source projects.)
