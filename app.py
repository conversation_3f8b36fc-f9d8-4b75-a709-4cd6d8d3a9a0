from fastapi import FastAP<PERSON>
from agent_lab.agent.permissions import get_authorized_users
from agent_lab.bot.slack_setup import slack_bolt_app # Adjusted import path
from slack_bolt.adapter.socket_mode.async_handler import AsyncSocketModeHandler
from core.config import config

import asyncio
# Added imports
import logging
from slack_sdk.web.async_client import Async<PERSON>eb<PERSON>lient
from agent_lab.bot import slack_setup # Import the slack_setup module itself
from agent_lab.bot.slack_setup import flaire_agent, bot_user_id # Adjusted import path
from logs import handle_interaction_and_log # For consistent interaction processing

app = FastAPI()

# Logger for this file
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup():
    # The AsyncSocketModeHandler uses the slack_bolt_app, which will now have the app_mention listener.
    # It's assumed that fetch_bot_user_id_on_startup is called by your main application runner (e.g., main.py)
    # or by another event handler if bot_user_id is initially None.
    handler = AsyncSocketModeHandler(slack_bolt_app, config.slack_app_token)
    slack_setup.socket_mode_client = handler.client # Assign the Socket Mode client    
    asyncio.create_task(handler.start_async())
    print("trying to print something out")
    print("SocketModeHandler started via FastAPI startup, listening for Slack events.")


# New event handler for app_mentions
@slack_bolt_app.event("app_mention")
async def handle_app_mentions(event: dict, say, client: AsyncWebClient, logger_bolt: logging.Logger):
    """Handles direct mentions to the bot in channels."""
    print(f"App mention event received: User {event.get('user')}, Text: {event.get('text')}")

    if not flaire_agent:
        print("Agent (flaire_agent) not initialized. Cannot process app_mention.")
        await say(channel=event.get("channel"), text="I'm currently unable to process requests as I'm not fully initialized.")
        return

    if not bot_user_id:
        print("Bot User ID is not available. Cannot process mention correctly. This should be fetched at startup.")
        await say(channel=event.get("channel"), text="I'm having trouble identifying myself right now. Please try again shortly.")
        return

    user_id_slack = event.get("user")
    text_from_event = event.get("text", "")
    channel_id = event.get("channel")
    message_ts = event.get("ts") # Timestamp of the mention message
    thread_ts = event.get("thread_ts") # For replying in thread

    if not user_id_slack or not text_from_event or not channel_id:
        print("Missing user, text, or channel_id in app_mention event. Ignoring.")
        return

    # Remove bot mention from text (e.g., "<@BOT_ID> your query")
    processed_text = text_from_event.replace(f"<@{bot_user_id}>", "").strip()

    if not processed_text: # If it was just a mention with no further text
        print(f"App mention by {user_id_slack} in {channel_id} was empty after removing mention. Replying with a greeting.")
        greeting_response = f"Hi <@{user_id_slack}>! How can I help you today?"
        reply_args_greeting = {"channel": channel_id, "text": greeting_response}
        if thread_ts:
            reply_args_greeting["thread_ts"] = thread_ts
        await say(**reply_args_greeting)
        return

    # Send typing indicator
    if slack_setup.socket_mode_client and channel_id:
        try:
            await slack_setup.socket_mode_client.send_socket_mode_message({
                "type": "typing",
                "channel": channel_id
            })
            print(f"Sent typing indicator to channel {channel_id} for app_mention.")
        except Exception as e:
            print(f"Failed to send typing indicator for app_mention to {channel_id}: {e}", exc_info=True)
    elif not slack_setup.socket_mode_client:
        logger.warning(f"Could not send typing indicator to channel {channel_id}: socket_mode_client is not available.")
    elif not channel_id: # Should be caught by earlier checks in the function, but for completeness
        logger.warning("Could not send typing indicator: channel_id is missing for app_mention.")


    # Add initial "white_check_mark" reaction
    initial_reaction_added = False
    if message_ts and channel_id:
        try:
            await client.reactions_add(channel=channel_id, name="white_check_mark", timestamp=message_ts)
            print(f"Added 'white_check_mark' (acknowledged) reaction to message {message_ts} in channel {channel_id} for app_mention.")
            initial_reaction_added = True
        except Exception as e:
            print(f"Failed to add initial 'white_check_mark' reaction for app_mention: {e}", exc_info=True)

    try:
        # Fetch user email - agent expects email for user_role/user_identifier

        user_info_response = await client.users_info(user=user_id_slack)
        user_email = user_info_response.get("user", {}).get("profile", {}).get("email")
        if not user_email:
            logger.warning(f"Could not retrieve email for Slack user_id: {user_id_slack}. Using Slack ID as identifier (agent may have issues with role resolution).")
            user_email = user_id_slack # Fallback
        print(f"User {user_email} is authorized to use the agent.")
        # print(f"Processing app_mention from user {user_email} in channel {channel_id} with text: '{processed_text}'")
        # approved = get_authorized_users(user_email)
        # if not approved:
        #     logger.warning(f"User {user_email} is not authorized to use the agent. Using fallback permissions.")
        #     return await say(
        #         channel=channel_id,
        #         text="You are not authorized to use this agent. Please contact support if you believe this is an error."
        #     )
        if not user_email:
            pass
        else:
            print(f"User {user_email} is authorized to use the agent.")
            # Call the centralized interaction handler from logs.py
            agent_result_dict = await handle_interaction_and_log(
                user_query=processed_text,
                user_role=user_email, # This is the user_identifier (email) for the agent
                agent_instance=flaire_agent,
                conversation_id=channel_id,
                channel_id=channel_id,    # Pass for potential mid-process reactions
                message_ts=message_ts     # Pass for potential mid-process reactions
            )
            
            agent_final_text_response = agent_result_dict.get("final_response", "Sorry, I encountered an issue processing your request.")
            final_suggested_reaction = agent_result_dict.get("final_reaction")

            # Add the agent-suggested final reaction
            if final_suggested_reaction and message_ts and channel_id:
                try:
                    await client.reactions_add(channel=channel_id, name=final_suggested_reaction, timestamp=message_ts)
                    print(f"Added final reaction '{final_suggested_reaction}' to message {message_ts} in channel {channel_id}.")
                except Exception as e:
                    print(f"Failed to add final_reaction '{final_suggested_reaction}' for app_mention: {e}", exc_info=True)
            
            reply_args = {"channel": channel_id, "text": agent_final_text_response}
            if thread_ts:
                reply_args["thread_ts"] = thread_ts
            await say(**reply_args)
            print(f"Sent agent response via app_mention handler to channel {channel_id} (thread: {thread_ts}).")

    except Exception as e:
        print(f"Error processing app_mention: {e}", exc_info=True)
        fallback_message = "Sorry, I encountered an error while trying to understand your request."
        error_reply_args = {"channel": channel_id, "text": fallback_message}
        if thread_ts:
            error_reply_args["thread_ts"] = thread_ts
        await say(**error_reply_args)

@app.get("/")
def read_root():
    return {"Hello": "Flutterwave"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) # Added host/port for typical local run


#  .\venv\Scripts\Activate
#  uvicorn app:app