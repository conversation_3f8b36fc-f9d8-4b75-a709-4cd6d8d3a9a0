from core.config import config
from agent_lab.bot import slack_setup # Import the module itself
import logging
import os
from fastapi import FastAP<PERSON> # FastAPI is used here as well
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles # For serving static plot files
from slack_bolt.adapter.socket_mode.async_handler import AsyncSocketModeHandler  # noqa: F401
import datetime
import re # For parsing filename
from agent_lab.agent import query_consumer 


# Get a logger for this module (optional, but good practice if you want to log from main.py too)
logger = logging.getLogger(__name__)

app = FastAPI()


# Ensure the directory for generated files exists (agent also does this, but good for explicitness)
os.makedirs(config.generated_files_dir, exist_ok=True) # For general files like CSVs
os.makedirs(config.plot_hosting_dir, exist_ok=True) # Specifically for plots

# We are removing the direct StaticFiles mount for the whole directory.
# Instead, we'll use a custom endpoint for downloads.
# Mount for serving static plot HTML files
app.mount(f"/{config.plot_hosting_url_path}", StaticFiles(directory=config.plot_hosting_dir), name="hosted_plots")

@app.get("/download/{filename:path}")
async def download_generated_file(filename: str):
    logger.info(f"Download request received for file: {filename}")
    file_path = os.path.join(config.generated_files_dir, filename)

    if not os.path.exists(file_path):
        logger.warning(f"File not found for download: {file_path}")
        return HTMLResponse(content="<h1>File not found or has expired.</h1>", status_code=404)

    # Extract timestamp from filename (e.g., some_file_YYYYMMDDHHMMSS_uniqueid.csv)
    match = re.search(r"_(\d{14})_", filename) # Looks for 14 digits surrounded by underscores
    if not match:
        logger.warning(f"Could not parse timestamp from filename: {filename}. Serving without expiry check (or deny). For now, denying.")
        # Decide policy: serve without check, or deny if timestamp is unparsable. Denying is safer.
        return HTMLResponse(content="<h1>Invalid file link.</h1>", status_code=400)

    timestamp_str = match.group(1)
    try:
        file_creation_time = datetime.datetime.strptime(timestamp_str, "%Y%m%d%H%M%S")
    except ValueError:
        logger.warning(f"Invalid timestamp format '{timestamp_str}' in filename: {filename}. Denying access.")
        return HTMLResponse(content="<h1>Invalid file link (timestamp format).</h1>", status_code=400)

    current_time = datetime.datetime.now()
    file_age_seconds = (current_time - file_creation_time).total_seconds()

    if file_age_seconds > config.generated_file_expiry_seconds:
        logger.info(f"File '{filename}' has expired. Age: {file_age_seconds}s, Expiry: {config.generated_file_expiry_seconds}s")
        # Optionally, delete the expired file here: os.remove(file_path)
        return HTMLResponse(content="<h1>This download link has expired.</h1>", status_code=410) # 410 Gone

    logger.info(f"Serving file: {file_path}")
    return FileResponse(path=file_path, filename=filename, media_type='application/octet-stream')

# Note: The StaticFiles mount above handles serving files from config.plot_hosting_dir
# under the /plots/ (or configured plot_hosting_url_path) path.
# No separate @app.get("/plots/{filename}") is strictly needed if StaticFiles is used correctly.
# If you wanted more complex logic (like auth or dynamic serving for plots),
# you would create a specific endpoint like the /download/ one.
# For simplicity and direct access, StaticFiles is good.

logger.info(f"Plots will be hosted from directory: {config.plot_hosting_dir}")
logger.info(f"Plots will be accessible under URL path: /{config.plot_hosting_url_path}")

@app.on_event("startup")
async def startup():
    # Initialize bot resources (like fetching bot_user_id) before starting the handler
    if slack_setup.slack_bolt_app and hasattr(slack_setup.slack_bolt_app, 'client'):
        await slack_setup.fetch_bot_user_id_on_startup(slack_setup.slack_bolt_app.client)
    handler = AsyncSocketModeHandler(slack_setup.slack_bolt_app, config.slack_app_token)
    slack_setup.socket_mode_client = handler.client # Assign the Socket Mode client here as well
    await handler.start_async()

logger.info("Slack Bolt App started")


if __name__ == "__main__":
    # asyncio.run(main())
    df = query_consumer.read_sql_query_dwh(""" 
                                select top 10 * from datamart.customers
                        """)
    
    print(df.head())