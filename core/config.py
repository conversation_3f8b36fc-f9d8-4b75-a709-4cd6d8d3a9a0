from pydantic_settings import BaseSettings, SettingsConfigDict
from pathlib import Path
import os # Added for os.makedirs
base_dir = Path(__file__).resolve().parent.parent
class Config(BaseSettings):
    # slack_client_secret: str
    # slack_client_id: str
    slack_app_id: str
    slack_app_token: str
    slack_signing_secret: str
    slack_bot_token: str
    allowed_users: list
    google_credentials_file: Path = f"{base_dir}/credentials.json"
    permissions_sheet_id: str = ""
    permissions_sheet_name: str = "FLW SlackBot Users"
    gemini_api_key: str = ""
    app_base_url: str = "http://localhost:8000" # Default for local development
    generated_files_dir: str = "generated_files" # General directory for generated files
    # --- New for Plot Hosting ---
    # Directory to store generated plot HTML files, defaults to a subdirectory of generated_files_dir
    plot_hosting_dir: str = os.path.join(generated_files_dir, "hosted_plots")
    # Publicly accessible base URL path for these plots (relative to app_base_url)
    plot_hosting_url_path: str = "plots" # e.g. http://localhost:8000/plots/filename.html
    # --- End New for Plot Hosting ---
    generated_file_expiry_seconds: int = 3600 # Default to 1 hour (3600 seconds)
    host: str
    port: int
    dwh_u: str
    dwh_p: str
    dbname: str

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
config = Config()
os.makedirs(config.plot_hosting_dir, exist_ok=True) # Ensure the plot hosting directory exists